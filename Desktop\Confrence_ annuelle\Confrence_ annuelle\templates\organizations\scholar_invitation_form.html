{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .hero-mini {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        position: relative;
        overflow: hidden;
    }

    .hero-mini::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('{% static "img/pattern.png" %}') repeat;
        opacity: 0.1;
    }

    .form-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        border: none;
        overflow: hidden;
        margin-top: -50px;
        position: relative;
        z-index: 2;
    }

    .form-card .card-header {
        background: linear-gradient(135deg, #3949ab 0%, #5e72e4 100%);
        border: none;
        padding: 2rem;
        text-align: center;
    }

    .form-card .card-body {
        padding: 3rem;
    }

    .form-label {
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 0.5rem;
        font-size: 1rem;
    }

    .form-control, .form-select {
        border: 2px solid #e2e8f0;
        border-radius: 12px;
        padding: 0.75rem 1rem;
        font-size: 1rem;
        transition: all 0.3s ease;
        background-color: #f8fafc;
    }

    .form-control:focus, .form-select:focus {
        border-color: #3949ab;
        box-shadow: 0 0 0 3px rgba(57, 73, 171, 0.1);
        background-color: white;
    }

    .btn-primary {
        background: linear-gradient(135deg, #3949ab 0%, #5e72e4 100%);
        border: none;
        border-radius: 12px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(57, 73, 171, 0.3);
    }

    .btn-secondary {
        background: #6c757d;
        border: none;
        border-radius: 12px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-secondary:hover {
        background: #5a6268;
        transform: translateY(-2px);
    }

    .back-link {
        color: white;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .back-link:hover {
        color: #f8f9fa;
        text-decoration: none;
    }

    .required-field::after {
        content: ' *';
        color: #e53e3e;
    }

    .form-text {
        color: #718096;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .alert {
        border-radius: 12px;
        border: none;
        margin-bottom: 2rem;
    }

    .alert-info {
        background: linear-gradient(135deg, #e6f3ff 0%, #cce7ff 100%);
        color: #0c5aa6;
    }

    .scholar-info {
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        border: 2px solid #0ea5e9;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .scholar-name {
        font-size: 1.25rem;
        font-weight: 700;
        color: #0c4a6e;
        margin-bottom: 0.5rem;
    }

    .scholar-details {
        color: #0369a1;
        font-size: 0.95rem;
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="hero-mini text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-md-8 mx-auto text-center">
                <h1 class="display-5 fw-bold mb-3">{{ title }}</h1>
                <p class="lead">دعوة العلماء للمشاركة في المؤتمر السنوي</p>
                <a href="{% url 'organizations:scholar_list' %}" class="back-link">
                    <i class="fas fa-arrow-right me-2"></i> العودة إلى قائمة العلماء
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card form-card">
                <div class="card-header">
                    <h4 class="mb-0 text-white">
                        <i class="fas fa-paper-plane me-2"></i>إرسال دعوة لعالم
                    </h4>
                </div>
                <div class="card-body">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <div class="alert alert-info">
                        <i class="fab fa-whatsapp me-2"></i>
                        <strong>ملاحظة:</strong> سيتم إعداد رابط WhatsApp لإرسال الدعوة إلى رقم هاتف العالم. تأكد من وجود رقم هاتف صحيح للعالم.
                    </div>

                    <form method="post" class="needs-validation" novalidate>
                        {% csrf_token %}

                        <!-- Scholar Selection -->
                        <div class="mb-4">
                            <label for="{{ form.scholar.id_for_label }}" class="form-label required-field">
                                {{ form.scholar.label }}
                            </label>
                            {{ form.scholar }}
                            {% if form.scholar.errors %}
                                <div class="text-danger mt-1">{{ form.scholar.errors }}</div>
                            {% endif %}
                            <div class="form-text">اختر العالم الذي تريد إرسال الدعوة إليه</div>
                        </div>

                        <!-- Subject -->
                        <div class="mb-4">
                            <label for="{{ form.subject.id_for_label }}" class="form-label required-field">
                                {{ form.subject.label }}
                            </label>
                            {{ form.subject }}
                            {% if form.subject.errors %}
                                <div class="text-danger mt-1">{{ form.subject.errors }}</div>
                            {% endif %}
                            <div class="form-text">عنوان الدعوة الذي سيظهر في البريد الإلكتروني</div>
                        </div>

                        <!-- Message -->
                        <div class="mb-4">
                            <label for="{{ form.message.id_for_label }}" class="form-label required-field">
                                {{ form.message.label }}
                            </label>
                            {{ form.message }}
                            {% if form.message.errors %}
                                <div class="text-danger mt-1">{{ form.message.errors }}</div>
                            {% endif %}
                            <div class="form-text">نص الدعوة الذي سيتم إرساله للعالم</div>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <button type="submit" class="btn btn-primary btn-lg me-3">
                                    <i class="fab fa-whatsapp me-2"></i>إعداد دعوة WhatsApp
                                </button>
                                <a href="{% url 'organizations:scholar_list' %}" class="btn btn-secondary btn-lg">
                                    <i class="fas fa-times me-2"></i>إلغاء
                                </a>
                            </div>
                            <div class="text-muted">
                                <small><i class="fas fa-shield-alt me-1"></i> جميع البيانات محمية ومشفرة</small>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const form = document.querySelector('.needs-validation');

    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }

        form.classList.add('was-validated');
    });

    // Scholar selection change handler
    const scholarSelect = document.getElementById('{{ form.scholar.id_for_label }}');
    if (scholarSelect) {
        scholarSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            if (selectedOption.value) {
                // You can add logic here to update the message template based on selected scholar
                console.log('Selected scholar:', selectedOption.text);
            }
        });
    }

    // Auto-resize textarea
    const messageTextarea = document.getElementById('{{ form.message.id_for_label }}');
    if (messageTextarea) {
        messageTextarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = this.scrollHeight + 'px';
        });

        // Initial resize
        messageTextarea.style.height = messageTextarea.scrollHeight + 'px';
    }
});
</script>
{% endblock %}
