{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .hero-mini {
        background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
        color: white;
        padding: 3rem 0;
        text-align: center;
        position: relative;
        overflow: hidden;
        margin-bottom: 2rem;
    }
    
    .hero-mini::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url('{% static "img/pattern.png" %}');
        opacity: 0.1;
    }
    
    .hero-mini .container {
        position: relative;
        z-index: 1;
    }
    
    .form-card {
        border: none;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
        border-radius: 0.5rem;
        overflow: hidden;
        margin-top: -1rem;
    }
    
    .form-card .card-header {
        background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
        padding: 1.25rem 1.5rem;
    }
    
    .form-card .card-body {
        padding: 2rem;
    }
    
    .form-label {
        font-weight: 600;
        color: #333;
    }
    
    .form-control, .form-select {
        padding: 0.75rem 1rem;
        border-radius: 0.5rem;
        border: 1px solid #dee2e6;
        transition: all 0.3s ease;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #3949ab;
        box-shadow: 0 0 0 0.25rem rgba(57, 73, 171, 0.25);
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-primary:hover {
        background: linear-gradient(135deg, #15196d 0%, #303f9f 100%);
        transform: translateY(-2px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }
    
    .btn-secondary {
        background: #f8f9fa;
        color: #333;
        border: 1px solid #dee2e6;
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-secondary:hover {
        background: #e9ecef;
        transform: translateY(-2px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
    }
    
    .current-logo {
        border: 1px solid #dee2e6;
        border-radius: 0.5rem;
        padding: 0.5rem;
        background-color: white;
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="hero-mini text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-md-8 mx-auto text-center">
                <h1 class="display-5 fw-bold mb-3">{{ title }}</h1>
                <p class="lead">
                    {% if form.instance.pk %}
                    تعديل معلومات المؤسسة وتحديث بياناتها
                    {% else %}
                    إضافة مؤسسة جديدة للمشاركة في المؤتمر السنوي
                    {% endif %}
                </p>
            </div>
        </div>
    </div>
</div>
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card form-card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0">
                    {% if form.instance.pk %}
                    <i class="fas fa-edit me-2"></i>{{ title }}
                    {% else %}
                    <i class="fas fa-plus-circle me-2"></i>{{ title }}
                    {% endif %}
                </h4>
            </div>
            <div class="card-body">
                <form method="post" enctype="multipart/form-data" class="needs-validation" novalidate>
                    {% csrf_token %}
                    
                    <div class="mb-3">
                        <label for="id_name" class="form-label">اسم المؤسسة *</label>
                        {{ form.name.errors }}
                        <input type="text" name="name" id="id_name" class="form-control {% if form.name.errors %}is-invalid{% endif %}" value="{{ form.name.value|default:'' }}" required>
                        <div class="invalid-feedback">
                            يرجى إدخال اسم المؤسسة.
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="id_name_fr" class="form-label">الاسم بالفرنسية</label>
                        {{ form.name_fr.errors }}
                        <input type="text" name="name_fr" id="id_name_fr" class="form-control {% if form.name_fr.errors %}is-invalid{% endif %}" value="{{ form.name_fr.value|default:'' }}">
                        <div class="form-text">اسم المؤسسة باللغة الفرنسية (اختياري).</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="id_email" class="form-label">البريد الإلكتروني *</label>
                        {{ form.email.errors }}
                        <input type="email" name="email" id="id_email" class="form-control {% if form.email.errors %}is-invalid{% endif %}" value="{{ form.email.value|default:'' }}" required>
                        <div class="invalid-feedback">
                            يرجى إدخال عنوان بريد إلكتروني صحيح.
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="id_phone" class="form-label">الهاتف</label>
                                {{ form.phone.errors }}
                                <input type="text" name="phone" id="id_phone" class="form-control {% if form.phone.errors %}is-invalid{% endif %}" value="{{ form.phone.value|default:'' }}">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="id_contact_person" class="form-label">الشخص المسؤول</label>
                                {{ form.contact_person.errors }}
                                <input type="text" name="contact_person" id="id_contact_person" class="form-control {% if form.contact_person.errors %}is-invalid{% endif %}" value="{{ form.contact_person.value|default:'' }}">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="id_website" class="form-label">الموقع الإلكتروني</label>
                        {{ form.website.errors }}
                        <input type="url" name="website" id="id_website" class="form-control {% if form.website.errors %}is-invalid{% endif %}" value="{{ form.website.value|default:'' }}">
                        <div class="invalid-feedback">
                            يرجى إدخال رابط صحيح.
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="id_address" class="form-label">العنوان</label>
                        {{ form.address.errors }}
                        <textarea name="address" id="id_address" rows="3" class="form-control {% if form.address.errors %}is-invalid{% endif %}">{{ form.address.value|default:'' }}</textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="id_description" class="form-label">الوصف</label>
                        {{ form.description.errors }}
                        <textarea name="description" id="id_description" rows="4" class="form-control {% if form.description.errors %}is-invalid{% endif %}">{{ form.description.value|default:'' }}</textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="id_participation_status" class="form-label">حالة المشاركة</label>
                        {{ form.participation_status.errors }}
                        <select name="participation_status" id="id_participation_status" class="form-select {% if form.participation_status.errors %}is-invalid{% endif %}">
                            {% for value, text in form.fields.participation_status.choices %}
                            <option value="{{ value }}" {% if form.participation_status.value == value %}selected{% endif %}>{{ text }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="id_logo" class="form-label">الشعار</label>
                        {{ form.logo.errors }}
                        <input type="file" name="logo" id="id_logo" class="form-control {% if form.logo.errors %}is-invalid{% endif %}" accept="image/*">
                        {% if form.instance.logo %}
                        <div class="mt-2">
                            <img src="{{ form.instance.logo.url }}" alt="الشعار الحالي" style="max-height: 100px;">
                            <p class="text-muted">الشعار الحالي</p>
                        </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{% url 'organizations:organization_list' %}" class="btn btn-secondary me-md-2">إلغاء</a>
                        <button type="submit" class="btn btn-primary">حفظ</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}