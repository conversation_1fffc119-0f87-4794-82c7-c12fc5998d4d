{% extends 'base.html' %}
{% load static %}

{% block title %}المسؤولون الرسميون{% endblock %}

{% block extra_css %}
<style>
    .officials-container {
        padding: 20px;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
    }

    .page-header {
        text-align: center;
        margin-bottom: 40px;
        padding: 30px;
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }

    .page-title {
        color: #2c3e50;
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 10px;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
    }

    .page-subtitle {
        color: #7f8c8d;
        font-size: 1.2rem;
        margin-bottom: 20px;
    }

    .stats-row {
        display: flex;
        justify-content: center;
        gap: 30px;
        margin-top: 20px;
    }

    .stat-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 10px;
        text-align: center;
        min-width: 150px;
    }

    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        display: block;
    }

    .stat-label {
        font-size: 0.9rem;
        opacity: 0.9;
    }

    .search-filters {
        background: white;
        padding: 25px;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        margin-bottom: 30px;
    }

    .ministry-section {
        margin-bottom: 40px;
        background: white;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
    }

    .ministry-section:hover {
        transform: translateY(-5px);
    }

    .ministry-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 25px;
        text-align: center;
        position: relative;
    }

    .ministry-title {
        font-size: 1.5rem;
        font-weight: bold;
        margin: 0;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
    }

    .ministry-count {
        position: absolute;
        top: 15px;
        right: 20px;
        background: rgba(255,255,255,0.2);
        padding: 5px 15px;
        border-radius: 20px;
        font-size: 0.9rem;
    }

    .officials-grid {
        padding: 30px;
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 25px;
    }

    .official-card {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 12px;
        padding: 20px;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .official-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        border-color: #667eea;
    }

    .official-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #667eea, #764ba2);
    }

    .official-title {
        color: #667eea;
        font-size: 0.85rem;
        font-weight: 600;
        margin-bottom: 8px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .official-name {
        color: #2c3e50;
        font-size: 1.1rem;
        font-weight: bold;
        margin-bottom: 12px;
        line-height: 1.3;
    }

    .official-position {
        color: #5a6c7d;
        font-size: 0.95rem;
        line-height: 1.4;
        margin-bottom: 15px;
    }

    .official-meta {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 15px;
        border-top: 1px solid #e9ecef;
        font-size: 0.8rem;
        color: #6c757d;
    }

    .no-officials {
        text-align: center;
        padding: 60px 20px;
        color: #6c757d;
    }

    .no-officials i {
        font-size: 4rem;
        margin-bottom: 20px;
        opacity: 0.3;
    }

    .filter-form {
        display: flex;
        gap: 15px;
        align-items: end;
        flex-wrap: wrap;
    }

    .form-group {
        flex: 1;
        min-width: 200px;
    }

    .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 600;
        color: #495057;
    }

    .form-control {
        width: 100%;
        padding: 10px 15px;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        font-size: 0.95rem;
        transition: border-color 0.3s ease;
    }

    .form-control:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .btn-search {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        padding: 10px 25px;
        border-radius: 8px;
        font-weight: 600;
        cursor: pointer;
        transition: transform 0.2s ease;
    }

    .btn-search:hover {
        transform: translateY(-2px);
    }

    @media (max-width: 768px) {
        .officials-grid {
            grid-template-columns: 1fr;
            padding: 20px;
        }
        
        .stats-row {
            flex-direction: column;
            gap: 15px;
        }
        
        .filter-form {
            flex-direction: column;
        }
        
        .form-group {
            min-width: 100%;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="officials-container">
    <!-- Page Header -->
    <div class="page-header">
        <h1 class="page-title">
            <i class="fas fa-users-cog"></i>
            المسؤولون الرسميون
        </h1>
        <p class="page-subtitle">دليل شامل للمسؤولين الحكوميين والإداريين في الجمهورية الإسلامية الموريتانية</p>
        
        <div class="stats-row">
            <div class="stat-card">
                <span class="stat-number">{{ total_count }}</span>
                <span class="stat-label">إجمالي المسؤولين</span>
            </div>
            <div class="stat-card">
                <span class="stat-number">{{ categories_count }}</span>
                <span class="stat-label">الوزارات والمؤسسات</span>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="search-filters">
        <form method="get" class="filter-form">
            <div class="form-group">
                <label for="search">البحث في الأسماء والمناصب</label>
                <input type="text" id="search" name="search" class="form-control" 
                       value="{{ request.GET.search }}" placeholder="ابحث عن مسؤول...">
            </div>
            <div class="form-group">
                <label for="category">الوزارة أو المؤسسة</label>
                <select id="category" name="category" class="form-control">
                    <option value="">جميع الوزارات</option>
                    {% for category_key, category_name in category_choices %}
                        <option value="{{ category_key }}" {% if request.GET.category == category_key %}selected{% endif %}>
                            {{ category_name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="form-group">
                <button type="submit" class="btn-search">
                    <i class="fas fa-search"></i> بحث
                </button>
            </div>
        </form>
    </div>

    <!-- Officials by Ministry -->
    {% for category_key, category_name in categories_with_officials %}
        <div class="ministry-section">
            <div class="ministry-header">
                <h2 class="ministry-title">{{ category_name }}</h2>
                <div class="ministry-count">
                    {{ category_key.1 }} مسؤول
                </div>
            </div>
            
            <div class="officials-grid">
                {% for official in category_key.0 %}
                    <div class="official-card">
                        <div class="official-title">{{ official.title }}</div>
                        <div class="official-name">{{ official.name }}</div>
                        <div class="official-position">{{ official.position }}</div>
                        <div class="official-meta">
                            <span>
                                <i class="fas fa-calendar-plus"></i>
                                {{ official.created_at|date:"Y/m/d" }}
                            </span>
                            {% if official.phone or official.email %}
                                <span>
                                    {% if official.phone %}
                                        <i class="fas fa-phone"></i>
                                    {% endif %}
                                    {% if official.email %}
                                        <i class="fas fa-envelope"></i>
                                    {% endif %}
                                </span>
                            {% endif %}
                        </div>
                    </div>
                {% endfor %}
            </div>
        </div>
    {% empty %}
        <div class="ministry-section">
            <div class="no-officials">
                <i class="fas fa-search"></i>
                <h3>لا توجد نتائج</h3>
                <p>لم يتم العثور على مسؤولين يطابقون معايير البحث المحددة</p>
            </div>
        </div>
    {% endfor %}
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Auto-submit form on category change
    document.getElementById('category').addEventListener('change', function() {
        this.form.submit();
    });
    
    // Smooth scroll animation
    document.querySelectorAll('.ministry-section').forEach(section => {
        section.addEventListener('click', function() {
            this.scrollIntoView({ behavior: 'smooth', block: 'start' });
        });
    });
</script>
{% endblock %}
