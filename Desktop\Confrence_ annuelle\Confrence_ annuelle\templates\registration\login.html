{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card glass-effect fade-in-element">
            <div class="card-header">
                <h3 class="neon-text text-center">{% trans "تسجيل الدخول" %}</h3>
            </div>
            <div class="card-body">
                {% if form.errors %}
                <div class="alert alert-danger glass-effect animate__animated animate__fadeInDown">
                    {% trans "اسم المستخدم أو كلمة المرور غير صحيحة. الرجاء المحاولة مرة أخرى." %}
                </div>
                {% endif %}
                
                {% if next %}
                    {% if user.is_authenticated %}
                    <div class="alert alert-warning glass-effect animate__animated animate__fadeInDown">
                        {% trans "ليس لديك صلاحية للوصول إلى هذه الصفحة. للمتابعة، يرجى تسجيل الدخول بحساب لديه الصلاحيات المناسبة." %}
                    </div>
                    {% else %}
                    <div class="alert alert-info glass-effect animate__animated animate__fadeInDown">
                        {% trans "يرجى تسجيل الدخول للوصول إلى هذه الصفحة." %}
                    </div>
                    {% endif %}
                {% endif %}
                
                <form method="post" action="{% url 'login' %}" class="fade-in-element">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="id_username" class="form-label">{% trans "اسم المستخدم" %}</label>
                        <input type="text" name="username" id="id_username" class="form-control glass-effect" required autofocus>
                    </div>
                    <div class="mb-3">
                        <label for="id_password" class="form-label">{% trans "كلمة المرور" %}</label>
                        <input type="password" name="password" id="id_password" class="form-control glass-effect" required>
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" name="remember" id="id_remember" class="form-check-input">
                        <label class="form-check-label" for="id_remember">{% trans "تذكرني" %}</label>
                    </div>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-3d btn-primary">{% trans "تسجيل الدخول" %}</button>
                    </div>
                    <input type="hidden" name="next" value="{{ next }}">
                </form>
                
                <div class="mt-3 text-center">
                    <a href="{% url 'password_reset' %}" class="btn-link glow-border">{% trans "نسيت كلمة المرور؟" %}</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}