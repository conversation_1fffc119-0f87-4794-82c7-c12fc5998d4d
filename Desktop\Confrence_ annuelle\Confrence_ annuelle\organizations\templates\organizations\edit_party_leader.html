{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .form-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        padding: 30px;
        margin-top: 20px;
    }
    
    .form-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 30px;
        text-align: center;
    }
    
    .form-section {
        margin-bottom: 30px;
        padding: 20px;
        border: 1px solid #e9ecef;
        border-radius: 10px;
        background: #f8f9fa;
    }
    
    .form-section h5 {
        color: #667eea;
        margin-bottom: 20px;
        font-weight: bold;
        border-bottom: 2px solid #667eea;
        padding-bottom: 10px;
    }
    
    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 8px;
    }
    
    .form-control, .form-select {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 12px 15px;
        transition: all 0.3s ease;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: bold;
        transition: all 0.3s ease;
    }
    
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
    }
    
    .btn-secondary {
        background: #6c757d;
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: bold;
        transition: all 0.3s ease;
    }
    
    .btn-secondary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(108, 117, 125, 0.4);
    }
    
    .current-photo {
        max-width: 150px;
        max-height: 150px;
        border-radius: 10px;
        margin-bottom: 10px;
        border: 3px solid #e9ecef;
    }
    
    .photo-preview {
        max-width: 200px;
        max-height: 200px;
        border-radius: 10px;
        margin-top: 10px;
        display: none;
    }
    
    .required-field::after {
        content: " *";
        color: #dc3545;
    }
    
    .help-text {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 5px;
    }
    
    .error-message {
        color: #dc3545;
        font-size: 0.875rem;
        margin-top: 5px;
    }
    
    .info-card {
        background: #e3f2fd;
        border: 1px solid #bbdefb;
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .info-card .info-label {
        font-weight: bold;
        color: #1976d2;
    }
    
    .info-card .info-value {
        color: #424242;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="form-container">
                <div class="form-header">
                    <h3><i class="fas fa-user-edit"></i> {{ title }}</h3>
                    <p class="mb-0">تعديل بيانات رئيس الحزب</p>
                </div>

                <!-- معلومات سريعة -->
                <div class="info-card">
                    <div class="row">
                        <div class="col-md-6">
                            <span class="info-label">تاريخ الإضافة:</span>
                            <span class="info-value">{{ party_leader.created_at|date:"Y-m-d H:i" }}</span>
                        </div>
                        <div class="col-md-6">
                            <span class="info-label">آخر تحديث:</span>
                            <span class="info-value">{{ party_leader.updated_at|date:"Y-m-d H:i" }}</span>
                        </div>
                    </div>
                </div>

                <form method="post" enctype="multipart/form-data" novalidate>
                    {% csrf_token %}
                    
                    <!-- المعلومات الأساسية -->
                    <div class="form-section">
                        <h5><i class="fas fa-user"></i> المعلومات الأساسية</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.name.id_for_label }}" class="form-label required-field">{{ form.name.label }}</label>
                                    {{ form.name }}
                                    {% if form.name.errors %}
                                        <div class="error-message">{{ form.name.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.party_name.id_for_label }}" class="form-label required-field">{{ form.party_name.label }}</label>
                                    {{ form.party_name }}
                                    {% if form.party_name.errors %}
                                        <div class="error-message">{{ form.party_name.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.title.id_for_label }}" class="form-label">{{ form.title.label }}</label>
                                    {{ form.title }}
                                    {% if form.title.errors %}
                                        <div class="error-message">{{ form.title.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.status.id_for_label }}" class="form-label">{{ form.status.label }}</label>
                                    {{ form.status }}
                                    {% if form.status.errors %}
                                        <div class="error-message">{{ form.status.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات الاتصال -->
                    <div class="form-section">
                        <h5><i class="fas fa-phone"></i> معلومات الاتصال</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.phone.id_for_label }}" class="form-label">{{ form.phone.label }}</label>
                                    {{ form.phone }}
                                    {% if form.phone.errors %}
                                        <div class="error-message">{{ form.phone.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.email.id_for_label }}" class="form-label">{{ form.email.label }}</label>
                                    {{ form.email }}
                                    {% if form.email.errors %}
                                        <div class="error-message">{{ form.email.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="{{ form.address.id_for_label }}" class="form-label">{{ form.address.label }}</label>
                            {{ form.address }}
                            {% if form.address.errors %}
                                <div class="error-message">{{ form.address.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- معلومات الحزب -->
                    <div class="form-section">
                        <h5><i class="fas fa-flag"></i> معلومات الحزب</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.party_founded_year.id_for_label }}" class="form-label">{{ form.party_founded_year.label }}</label>
                                    {{ form.party_founded_year }}
                                    {% if form.party_founded_year.errors %}
                                        <div class="error-message">{{ form.party_founded_year.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.party_ideology.id_for_label }}" class="form-label">{{ form.party_ideology.label }}</label>
                                    {{ form.party_ideology }}
                                    {% if form.party_ideology.errors %}
                                        <div class="error-message">{{ form.party_ideology.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="{{ form.party_headquarters.id_for_label }}" class="form-label">{{ form.party_headquarters.label }}</label>
                            {{ form.party_headquarters }}
                            {% if form.party_headquarters.errors %}
                                <div class="error-message">{{ form.party_headquarters.errors.0 }}</div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- معلومات إضافية -->
                    <div class="form-section">
                        <h5><i class="fas fa-info-circle"></i> معلومات إضافية</h5>
                        <div class="mb-3">
                            <label for="{{ form.biography.id_for_label }}" class="form-label">{{ form.biography.label }}</label>
                            {{ form.biography }}
                            {% if form.biography.errors %}
                                <div class="error-message">{{ form.biography.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div class="mb-3">
                            <label for="{{ form.achievements.id_for_label }}" class="form-label">{{ form.achievements.label }}</label>
                            {{ form.achievements }}
                            {% if form.achievements.errors %}
                                <div class="error-message">{{ form.achievements.errors.0 }}</div>
                            {% endif %}
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.photo.id_for_label }}" class="form-label">{{ form.photo.label }}</label>
                                    {% if party_leader.photo %}
                                        <div class="mb-2">
                                            <img src="{{ party_leader.photo.url }}" alt="{{ party_leader.name }}" class="current-photo">
                                            <div class="help-text">الصورة الحالية</div>
                                        </div>
                                    {% endif %}
                                    {{ form.photo }}
                                    <img id="photoPreview" class="photo-preview" alt="معاينة الصورة الجديدة">
                                    {% if form.photo.errors %}
                                        <div class="error-message">{{ form.photo.errors.0 }}</div>
                                    {% endif %}
                                    <div class="help-text">اختر صورة جديدة لتغيير الصورة الحالية</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="{{ form.notes.id_for_label }}" class="form-label">{{ form.notes.label }}</label>
                                    {{ form.notes }}
                                    {% if form.notes.errors %}
                                        <div class="error-message">{{ form.notes.errors.0 }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- أزرار الحفظ -->
                    <div class="text-center">
                        <button type="submit" class="btn btn-primary me-3">
                            <i class="fas fa-save"></i> حفظ التغييرات
                        </button>
                        <a href="{% url 'organizations:party_leaders' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> العودة للقائمة
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // معاينة الصورة الجديدة
    const photoInput = document.getElementById('{{ form.photo.id_for_label }}');
    const photoPreview = document.getElementById('photoPreview');
    
    if (photoInput) {
        photoInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    photoPreview.src = e.target.result;
                    photoPreview.style.display = 'block';
                };
                reader.readAsDataURL(file);
            } else {
                photoPreview.style.display = 'none';
            }
        });
    }
    
    // التحقق من صحة النموذج
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        let isValid = true;
        
        // التحقق من الحقول المطلوبة
        const requiredFields = ['{{ form.name.id_for_label }}', '{{ form.party_name.id_for_label }}'];
        requiredFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field && !field.value.trim()) {
                isValid = false;
                field.classList.add('is-invalid');
            } else if (field) {
                field.classList.remove('is-invalid');
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            alert('يرجى ملء جميع الحقول المطلوبة');
        }
    });
});
</script>
{% endblock %}
