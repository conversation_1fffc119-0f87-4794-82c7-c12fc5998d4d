{% load i18n %}
{% load static %}
{% get_current_language as LANGUAGE_CODE %}
<!DOCTYPE html>
<html lang="{% if LANGUAGE_CODE == 'ar' %}ar{% else %}en{% endif %}" dir="{% if LANGUAGE_CODE == 'ar' %}rtl{% else %}ltr{% endif %}" {% if LANGUAGE_CODE == 'ar' %}class="rtl-layout"{% endif %}>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{% trans "مؤتمر السيرة النبوية السنوي" %}{% endblock %}</title>

    <!-- Bootstrap CSS RTL/LTR -->
    {% if LANGUAGE_CODE == 'ar' %}
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css">
    {% else %}
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    {% endif %}

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <!-- Font Awesome Backup -->
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.15.4/css/all.css" crossorigin="anonymous">
    <!-- Font Awesome 6 Backup -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">

    <!-- Custom CSS -->
    <link rel="stylesheet" href="{% static 'css/modern-theme.css' %}">
    <link rel="stylesheet" href="{% static 'css/style.css' %}">

    <style>
        /* تنسيق القائمة المنسدلة الفرعية */
        .dropdown-submenu {
            position: relative;
        }

        .dropdown-submenu .dropdown-menu {
            top: 100%;
            left: 0;
            margin-top: 0;
            border-radius: 0.375rem;
            display: none;
            min-width: 200px;
        }

        .dropdown-submenu:hover .dropdown-menu,
        .dropdown-submenu .dropdown-menu:hover {
            display: block;
        }

        .dropdown-submenu .dropdown-toggle::after {
            display: inline-block;
            margin-left: 0.255em;
            vertical-align: 0.255em;
            content: "";
            border-top: 0.3em solid;
            border-right: 0.3em solid transparent;
            border-bottom: 0;
            border-left: 0.3em solid transparent;
        }

        .dropdown-submenu .dropdown-toggle:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }

        /* تحسين للغة العربية */
        [dir="rtl"] .dropdown-submenu .dropdown-menu {
            left: 0;
            right: auto;
        }

        [dir="rtl"] .dropdown-submenu .dropdown-toggle::after {
            border-top: 0.3em solid;
            border-right: 0.3em solid transparent;
            border-bottom: 0;
            border-left: 0.3em solid transparent;
        }

        /* منع إخفاء القائمة عند التمرير عليها */
        .dropdown-submenu .dropdown-menu:hover {
            display: block;
        }

        /* تحسين المظهر */
        .dropdown-submenu .dropdown-item {
            padding: 0.5rem 1rem;
        }

        .dropdown-submenu .dropdown-item:hover {
            background-color: rgba(0, 0, 0, 0.05);
        }

        /* إصلاح موضع الـ footer */
        html, body {
            height: 100%;
        }

        body {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
        }

        .main-content {
            flex: 1;
        }

        .footer-modern {
            margin-top: auto;
        }
    </style>
    <link rel="stylesheet" href="{% static 'css/visual_effects.css' %}">

    {% block extra_css %}{% endblock %}
</head>
<body class="d-flex flex-column min-vh-100">

    <div class="bg-decoration bg-decoration-1 animate__animated animate__fadeIn animate__delay-1s"></div>
    <div class="bg-decoration bg-decoration-2 animate__animated animate__fadeIn animate__delay-2s"></div>
    <div class="bg-decoration bg-decoration-3 animate__animated animate__fadeIn animate__delay-3s"></div>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light sticky-top navbar-modern" id="mainNavbar">
        <div class="container">
            <a class="navbar-brand logo-animated" href="{% url 'core:home' %}">
                <img src="{% static 'img/new_logo.png' %}?v=123456789" alt="{% trans 'شعار المؤتمر' %}" height="100" class="animate__animated animate__fadeIn">
                <span class="{% if LANGUAGE_CODE == 'ar' %}me-2{% else %}ms-2{% endif %} animate__animated animate__fadeIn animate__delay-1s">{% trans "مؤتمر السيرة النبوية" %}</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <!-- تم تعديل الترتيب هنا لجعل عناوين الصفحات على اليمين في اللغة العربية -->
                {% if LANGUAGE_CODE == 'ar' %}
                <!-- للغة العربية (RTL) -->
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                {% else %}
                <!-- للغة الإنجليزية (LTR) -->
                <ul class="navbar-nav ms-auto mb-2 mb-lg-0">
                {% endif %}
                    <li class="nav-item me-3">
                        <a class="nav-link-modern" href="{% url 'core:home' %}">{% trans "الرئيسية" %}</a>
                    </li>
                    <li class="nav-item me-3">
                        <a class="nav-link-modern" href="{% url 'core:about' %}">{% trans "عن المؤتمر" %}</a>
                    </li>

                    <li class="nav-item me-3">
                        <a class="nav-link-modern" href="{% url 'core:contact' %}">{% trans "اتصل بنا" %}</a>
                    </li>
                </ul>

                <!-- تم تعديل الترتيب هنا لجعل أزرار اللغة والمستخدم على اليسار في اللغة العربية -->
                {% if LANGUAGE_CODE == 'ar' %}
                <!-- للغة العربية (RTL) -->
                <div class="d-flex ms-auto">
                {% else %}
                <!-- للغة الإنجليزية (LTR) -->
                <div class="d-flex me-auto">
                {% endif %}
                    <div class="dropdown me-4">
                        <button class="btn btn-outline-secondary dropdown-toggle btn-glow" type="button" id="languageDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            {% if LANGUAGE_CODE == 'ar' %}
                                <i class="fas fa-globe icon-float"></i> العربية
                            {% elif LANGUAGE_CODE == 'fr' %}
                                <i class="fas fa-globe icon-float"></i> Français
                            {% else %}
                                <i class="fas fa-globe icon-float"></i> English
                            {% endif %}
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end glass-effect" aria-labelledby="languageDropdown">
                            <li>
                                <form action="/i18n/setlang/" method="post">
                                    {% csrf_token %}
                                    <input name="next" type="hidden" value="{{ request.path }}">
                                    <input name="language" type="hidden" value="ar">
                                    <button class="dropdown-item" type="submit">العربية</button>
                                </form>
                            </li>
                            <li>
                                <form action="/i18n/setlang/" method="post">
                                    {% csrf_token %}
                                    <input name="next" type="hidden" value="{{ request.path }}">
                                    <input name="language" type="hidden" value="fr">
                                    <button class="dropdown-item" type="submit">Français</button>
                                </form>
                            </li>
                        </ul>
                    </div>

                    {% if user.is_authenticated %}
                    <div class="dropdown">
                        <button class="btn btn-primary dropdown-toggle btn-3d" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user icon-float"></i> {{ user.username }}
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end glass-effect" aria-labelledby="userDropdown">
                            {% if user.is_staff %}
                            <li><a class="dropdown-item" href="{% url 'core:admin_dashboard' %}">{% trans "لوحة الإدارة الرئيسية" %}</a></li>
                            <li><a class="dropdown-item" href="{% url 'organizations:admin_organizations_dashboard' %}">{% trans "إدارة المؤسسات" %}</a></li>
                            {% if user.admin_profile and user.admin_profile.is_super_admin %}
                            <li><a class="dropdown-item" href="{% url 'core:admin_list' %}">
                                <i class="fas fa-users-cog me-2"></i>{% trans "إدارة المسؤولين" %}
                            </a></li>
                            {% endif %}
                            <li><a class="dropdown-item" href="{% url 'organizations:scholar_list' %}">{% trans "إدارة العلماء" %}</a></li>
                            <li><a class="dropdown-item" href="{% url 'organizations:elected_officials_list' %}">
                                <i class="fas fa-users me-2"></i>{% trans "المنتخبون" %}
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'organizations:former_ministers_list' %}">
                                <i class="fas fa-user-tie me-2"></i>{% trans "وزراء سابقون" %}
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'organizations:diplomatic_corps_list' %}">
                                <i class="fas fa-globe me-2"></i>{% trans "السلك الدبلوماسي" %}
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'organizations:notables_list' %}">
                                <i class="fas fa-crown me-2"></i>{% trans "أعيان" %}
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'organizations:party_leaders_public' %}">
                                <i class="fas fa-users-cog me-2"></i>{% trans "رؤساء الأحزاب" %}
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'organizations:officials_list' %}">
                                <i class="fas fa-user-tie me-2"></i>{% trans "الرسمي" %}
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'admin:index' %}">{% trans "لوحة الإدارة المتقدمة" %}</a></li>
                            <li><hr class="dropdown-divider"></li>
                            {% endif %}
                            <li><a class="dropdown-item" href="{% url 'organizations:send_bulk_invitation' %}">{% trans "إرسال دعوات جماعية" %}</a></li>
                            <li><a class="dropdown-item" href="{% url 'organizations:invitation_list' %}">{% trans "قائمة الدعوات" %}</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <form action="{% url 'logout' %}" method="post">
                                    {% csrf_token %}
                                    <button class="dropdown-item" type="submit">{% trans "تسجيل الخروج" %}</button>
                                </form>
                            </li>
                        </ul>
                    </div>
                    {% else %}
                    <a href="{% url 'login' %}" class="btn btn-outline-primary btn-glow shadow-pulse">
                        <i class="fas fa-sign-in-alt icon-float"></i> {% trans "تسجيل الدخول" %}
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container py-4 main-content">
        {% if messages %}
        <div class="row">
            <div class="col-12">
                {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show glass-effect animate__animated animate__fadeInDown" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        {% block content %}{% endblock %}
    </div>

    <!-- Footer -->
    <footer class="footer-modern mt-auto">
        <div class="container">
            <div class="row">
                <div class="col-md-4 mb-4 mb-md-0 fade-in-element">
                    <h5 class="neon-text">{% trans "روابط سريعة" %}</h5>
                    <ul class="list-unstyled">
                        <li><a href="{% url 'core:home' %}" class="text-white footer-link">{% trans "الرئيسية" %}</a></li>
                        <li><a href="{% url 'core:about' %}" class="text-white footer-link">{% trans "عن المؤتمر" %}</a></li>

                        <li><a href="{% url 'core:contact' %}" class="text-white footer-link">{% trans "اتصل بنا" %}</a></li>
                    </ul>
                </div>
                <div class="col-md-4 mb-4 mb-md-0 fade-in-element">
                    <h5 class="neon-text">{% trans "تواصل معنا" %}</h5>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-map-marker-alt me-2 icon-float"></i> {% trans "نواكشوط ،موريتانيا" %}</li>
                        <li><i class="fas fa-phone me-2 icon-float"></i> +222 32816779</li>
                        <li><i class="fas fa-envelope me-2 icon-float"></i><EMAIL></li>
                    </ul>
                </div>
                <div class="col-md-4 fade-in-element">
                    <h5 class="neon-text">{% trans "تابعنا" %}</h5>
                    <div class="d-flex">
                        <a href="#" class="text-white me-3 social-icon"><i class="fab fa-twitter fa-2x icon-float"></i></a>
                        <a href="#" class="text-white me-3 social-icon"><i class="fab fa-facebook fa-2x icon-float"></i></a>
                        <a href="#" class="text-white me-3 social-icon"><i class="fab fa-instagram fa-2x icon-float"></i></a>
                        <a href="#" class="text-white social-icon"><i class="fab fa-youtube fa-2x icon-float"></i></a>
                    </div>
                </div>
            </div>
            <hr class="my-4 glow-border">
            <div class="text-center">
                <p class="mb-0 animate__animated animate__fadeIn">{% trans "جميع الحقوق محفوظة" %} &copy; {% now "Y" %} {% trans "مؤتمر السيرة النبوية السنوي" %}</p>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JS -->
    <script src="{% static 'js/main.js' %}"></script>
    <script src="{% static 'js/visual_effects.js' %}"></script>

    <script>
        // تحسين عمل القائمة المنسدلة الفرعية
        document.addEventListener('DOMContentLoaded', function() {
            // إضافة معالج الأحداث للقائمة المنسدلة الفرعية
            const dropdownSubmenus = document.querySelectorAll('.dropdown-submenu');

            dropdownSubmenus.forEach(function(submenu) {
                const toggle = submenu.querySelector('.dropdown-toggle');
                const menu = submenu.querySelector('.dropdown-menu');

                if (toggle && menu) {
                    // عند النقر على العنصر الرئيسي
                    toggle.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();

                        // إخفاء جميع القوائم الفرعية الأخرى
                        document.querySelectorAll('.dropdown-submenu .dropdown-menu').forEach(function(otherMenu) {
                            if (otherMenu !== menu) {
                                otherMenu.style.display = 'none';
                            }
                        });

                        // تبديل عرض القائمة الحالية
                        if (menu.style.display === 'block') {
                            menu.style.display = 'none';
                        } else {
                            menu.style.display = 'block';
                        }
                    });
                }
            });

            // إخفاء القوائم الفرعية عند النقر خارجها
            document.addEventListener('click', function(e) {
                if (!e.target.closest('.dropdown-submenu')) {
                    document.querySelectorAll('.dropdown-submenu .dropdown-menu').forEach(function(menu) {
                        menu.style.display = 'none';
                    });
                }
            });
        });
    </script>

    {% block extra_js %}{% endblock %}

    <script>
        // تفعيل تأثيرات الظهور عند التمرير للعناصر الجديدة
        document.addEventListener('DOMContentLoaded', function() {
            // تفعيل تأثير الظهور عند التمرير
            initScrollAnimations();

            // تفعيل تأثيرات الأزرار ثلاثية الأبعاد
            init3DButtons();

            // تفعيل تأثير الروابط في القائمة
            initNavLinkHover();

            // تفعيل تأثير الشريط العلوي عند التمرير
            initNavbarScroll();

            // تفعيل تحسين تغيير اللغة
            initLanguageSwitch();
        });

        // تأثير الروابط في القائمة
        function initNavLinkHover() {
            const navLinks = document.querySelectorAll('.nav-link-modern');

            navLinks.forEach(link => {
                link.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-2px)';
                });

                link.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        }

        // تأثير الشريط العلوي عند التمرير
        function initNavbarScroll() {
            const navbar = document.getElementById('mainNavbar');
            let lastScrollTop = 0;

            window.addEventListener('scroll', function() {
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

                if (scrollTop > 100) {
                    navbar.classList.add('scrolled');
                } else {
                    navbar.classList.remove('scrolled');
                }

                lastScrollTop = scrollTop;
            });
        }

        // تحسين تجربة تغيير اللغة
        function initLanguageSwitch() {
            const languageForms = document.querySelectorAll('form[action="/i18n/setlang/"]');

            languageForms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    // إضافة تأثير تحميل
                    const button = this.querySelector('button');
                    const originalText = button.textContent;
                    button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> ' + originalText;
                    button.disabled = true;
                });
            });
        }
    </script>
</body>
</html>
