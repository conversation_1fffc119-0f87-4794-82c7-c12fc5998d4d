/**
 * ملف JavaScript لتحسين واجهة الإدارة باللغة العربية
 */

document.addEventListener('DOMContentLoaded', function() {
    // التحقق من أن الصفحة تستخدم اللغة العربية
    const isRTL = document.dir === 'rtl' || document.documentElement.lang === 'ar';
    
    if (isRTL) {
        // إضافة صنف RTL للجسم
        document.body.classList.add('rtl');
        
        // تصحيح اتجاه عناصر القائمة المنسدلة
        fixDropdownMenus();
        
        // تصحيح اتجاه الجداول
        fixTableDirection();
        
        // تصحيح اتجاه النماذج
        fixFormDirection();
        
        // تصحيح اتجاه الفلاتر
        fixFilterDirection();
        
        // تحسين عرض الأزرار
        enhanceButtons();
    }
});

/**
 * تصحيح اتجاه القوائم المنسدلة
 */
function fixDropdownMenus() {
    // تصحيح اتجاه القوائم المنسدلة في واجهة الإدارة
    const dropdowns = document.querySelectorAll('.dropdown-menu');
    dropdowns.forEach(dropdown => {
        dropdown.style.textAlign = 'right';
        dropdown.style.right = '0';
        dropdown.style.left = 'auto';
    });
}

/**
 * تصحيح اتجاه الجداول
 */
function fixTableDirection() {
    // تصحيح محاذاة النص في خلايا الجداول
    const tableCells = document.querySelectorAll('td, th');
    tableCells.forEach(cell => {
        if (!cell.hasAttribute('align')) {
            cell.style.textAlign = 'right';
        }
    });
    
    // تصحيح اتجاه أيقونات الترتيب
    const sortIcons = document.querySelectorAll('.sortable .sortoptions');
    sortIcons.forEach(icon => {
        icon.style.float = 'left';
    });
}

/**
 * تصحيح اتجاه النماذج
 */
function fixFormDirection() {
    // تصحيح محاذاة حقول النماذج
    const formRows = document.querySelectorAll('.form-row');
    formRows.forEach(row => {
        row.style.direction = 'rtl';
    });
    
    // تصحيح محاذاة التسميات
    const labels = document.querySelectorAll('label');
    labels.forEach(label => {
        label.style.textAlign = 'right';
        label.style.float = 'right';
    });
    
    // تصحيح محاذاة نصوص المساعدة
    const helpTexts = document.querySelectorAll('.help');
    helpTexts.forEach(help => {
        help.style.marginRight = '160px';
        help.style.marginLeft = '0';
    });
}

/**
 * تصحيح اتجاه الفلاتر
 */
function fixFilterDirection() {
    const filters = document.querySelector('#changelist-filter');
    if (filters) {
        filters.style.right = 'auto';
        filters.style.left = '0';
        
        // تصحيح محاذاة عناوين الفلاتر
        const filterHeadings = filters.querySelectorAll('h2, h3');
        filterHeadings.forEach(heading => {
            heading.style.textAlign = 'right';
            heading.style.paddingRight = '15px';
        });
        
        // تصحيح محاذاة قوائم الفلاتر
        const filterLists = filters.querySelectorAll('ul');
        filterLists.forEach(list => {
            list.style.paddingRight = '15px';
            list.style.paddingLeft = '0';
        });
    }
}

/**
 * تحسين عرض الأزرار
 */
function enhanceButtons() {
    // تحسين صف الأزرار في أسفل النماذج
    const submitRow = document.querySelector('.submit-row');
    if (submitRow) {
        submitRow.style.textAlign = 'left';
        
        // تصحيح موضع زر الحذف
        const deleteLink = submitRow.querySelector('.deletelink-box');
        if (deleteLink) {
            deleteLink.style.float = 'right';
        }
        
        // تصحيح موضع زر الحفظ الافتراضي
        const defaultButton = submitRow.querySelector('.default');
        if (defaultButton) {
            defaultButton.style.float = 'left';
            defaultButton.style.marginRight = '8px';
            defaultButton.style.marginLeft = '0';
        }
    }
}