/* نظام الألوان والتصميم الحديث لمؤتمر السيرة النبوية */

/* ===== متغيرات الألوان الأساسية ===== */
:root {
  /* الألوان الأساسية */
  --primary-color: #1e3a8a;
  --primary-light: #3b82f6;
  --primary-dark: #1e40af;
  --secondary-color: #059669;
  --secondary-light: #10b981;
  --secondary-dark: #047857;

  /* الألوان المساعدة */
  --accent-color: #f59e0b;
  --accent-light: #fbbf24;
  --accent-dark: #d97706;

  /* الألوان الرمادية */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* ألوان الحالة */
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  --info-color: #3b82f6;

  /* التدرجات */
  --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  --gradient-secondary: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-light) 100%);
  --gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-light) 100%);
  --gradient-hero: linear-gradient(135deg, var(--primary-dark) 0%, var(--primary-color) 50%, var(--secondary-color) 100%);

  /* الظلال */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* الحدود */
  --border-radius-sm: 0.375rem;
  --border-radius-md: 0.5rem;
  --border-radius-lg: 0.75rem;
  --border-radius-xl: 1rem;
  --border-radius-2xl: 1.5rem;

  /* المساحات */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;

  /* الخطوط */
  --font-family-arabic: 'Tajawal', 'Segoe UI', sans-serif;
  --font-family-english: 'Inter', 'Segoe UI', sans-serif;

  /* أحجام الخطوط */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;
  --text-6xl: 3.75rem;

  /* أوزان الخطوط */
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;

  /* الانتقالات */
  --transition-fast: 0.15s ease-in-out;
  --transition-normal: 0.3s ease-in-out;
  --transition-slow: 0.5s ease-in-out;
}

/* ===== إعادة تعيين الأساسيات ===== */
* {
  box-sizing: border-box;
}

body {
  font-family: var(--font-family-arabic);
  line-height: 1.6;
  color: var(--gray-800);
  background-color: var(--gray-50);
  font-size: var(--text-base);
  font-weight: var(--font-normal);
}

/* ===== تحسين الخطوط ===== */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;600;700;800&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

/* ===== العناوين المحسنة ===== */
h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-bold);
  line-height: 1.2;
  margin-bottom: var(--spacing-md);
  color: var(--gray-900);
}

h1 { font-size: var(--text-5xl); }
h2 { font-size: var(--text-4xl); }
h3 { font-size: var(--text-3xl); }
h4 { font-size: var(--text-2xl); }
h5 { font-size: var(--text-xl); }
h6 { font-size: var(--text-lg); }

/* ===== الأزرار المحسنة ===== */
.btn-modern {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: var(--border-radius-lg);
  font-weight: var(--font-semibold);
  font-size: var(--text-base);
  line-height: 1.5;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all var(--transition-normal);
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

.btn-modern:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.btn-modern:active {
  transform: translateY(0);
  box-shadow: var(--shadow-md);
}

/* أنواع الأزرار */
.btn-primary-modern {
  background: var(--gradient-primary);
  color: white;
}

.btn-primary-modern:hover {
  background: var(--gradient-primary);
  filter: brightness(1.1);
  color: white;
}

.btn-secondary-modern {
  background: var(--gradient-secondary);
  color: white;
}

.btn-secondary-modern:hover {
  background: var(--gradient-secondary);
  filter: brightness(1.1);
  color: white;
}

.btn-accent-modern {
  background: var(--gradient-accent);
  color: white;
}

.btn-accent-modern:hover {
  background: var(--gradient-accent);
  filter: brightness(1.1);
  color: white;
}

.btn-outline-modern {
  background: transparent;
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
}

.btn-outline-modern:hover {
  background: var(--primary-color);
  color: white;
}

/* ===== البطاقات المحسنة ===== */
.card-modern {
  background: white;
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
  overflow: hidden;
  transition: all var(--transition-normal);
  position: relative;
}

.card-modern:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
}

.card-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
}

.card-modern-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--gray-200);
  background: var(--gray-50);
}

.card-modern-body {
  padding: var(--spacing-lg);
}

.card-modern-footer {
  padding: var(--spacing-lg);
  border-top: 1px solid var(--gray-200);
  background: var(--gray-50);
}

/* ===== الشريط العلوي المحسن ===== */
.navbar-modern {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-normal);
}

.navbar-modern.scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: var(--shadow-md);
}

.navbar-brand-modern {
  display: flex;
  align-items: center;
  font-weight: var(--font-bold);
  font-size: var(--text-xl);
  color: var(--gray-900);
  text-decoration: none;
}

.navbar-brand-modern:hover {
  color: var(--primary-color);
}

.nav-link-modern {
  color: var(--gray-700);
  font-weight: var(--font-medium);
  padding: var(--spacing-sm) var(--spacing-lg);
  margin: 0 var(--spacing-sm);
  border-radius: var(--border-radius-md);
  transition: all var(--transition-fast);
  text-decoration: none;
  position: relative;
}

.nav-link-modern:hover {
  color: var(--primary-color);
  background: var(--gray-100);
}

.nav-link-modern.active {
  color: var(--primary-color);
  background: var(--primary-color);
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  color: white;
}

/* ===== القسم البطولي المحسن ===== */
.hero-modern {
  background: var(--gradient-hero);
  color: white;
  padding: var(--spacing-3xl) 0;
  position: relative;
  overflow: hidden;
  min-height: 70vh;
  display: flex;
  align-items: center;
}

.hero-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="rgba(255,255,255,0.1)"><polygon points="1000,100 1000,0 0,100"/></svg>');
  background-size: cover;
  background-position: bottom;
}

.hero-modern-content {
  position: relative;
  z-index: 2;
}

.hero-modern h1 {
  font-size: var(--text-6xl);
  font-weight: var(--font-extrabold);
  margin-bottom: var(--spacing-lg);
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.hero-modern p {
  font-size: var(--text-xl);
  margin-bottom: var(--spacing-xl);
  opacity: 0.9;
}

/* ===== الفوتر المحسن ===== */
.footer-modern {
  background: var(--gray-900);
  color: var(--gray-300);
  padding: var(--spacing-3xl) 0 var(--spacing-xl) 0;
  position: relative;
  margin-top: auto;
  width: 100%;
}

.footer-modern::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--gradient-primary);
}

.footer-modern h5 {
  color: white;
  font-weight: var(--font-bold);
  margin-bottom: var(--spacing-lg);
}

.footer-modern a {
  color: var(--gray-400);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.footer-modern a:hover {
  color: var(--primary-light);
}

/* ===== التأثيرات البصرية المحسنة ===== */
.glass-modern {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--border-radius-xl);
}

.gradient-text-modern {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 200% auto;
  animation: gradient-shift 3s ease-in-out infinite;
}

@keyframes gradient-shift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.floating-modern {
  animation: float-modern 6s ease-in-out infinite;
}

@keyframes float-modern {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.pulse-modern {
  animation: pulse-modern 2s ease-in-out infinite;
}

@keyframes pulse-modern {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

/* ===== الاستجابة للأجهزة المختلفة ===== */
@media (max-width: 768px) {
  :root {
    --text-5xl: 2.5rem;
    --text-4xl: 2rem;
    --text-3xl: 1.75rem;
    --spacing-3xl: 2rem;
  }

  .hero-modern {
    padding: var(--spacing-2xl) 0;
    min-height: 50vh;
  }

  .card-modern {
    margin-bottom: var(--spacing-lg);
  }

  .btn-modern {
    width: 100%;
    margin-bottom: var(--spacing-sm);
  }
}

@media (max-width: 480px) {
  .hero-modern h1 {
    font-size: var(--text-4xl);
  }

  .hero-modern p {
    font-size: var(--text-lg);
  }
}

/* ===== دعم RTL محسن ===== */
[dir="rtl"] .card-modern::before {
  right: 0;
  left: auto;
}

[dir="rtl"] .nav-link-modern {
  text-align: right;
}

[dir="rtl"] .btn-modern {
  direction: rtl;
}

/* ===== تحسينات إضافية ===== */
.section-modern {
  padding: var(--spacing-3xl) 0;
}

.section-modern-header {
  text-align: center;
  margin-bottom: var(--spacing-2xl);
}

.section-modern-title {
  font-size: var(--text-4xl);
  font-weight: var(--font-extrabold);
  color: var(--gray-900);
  margin-bottom: var(--spacing-md);
}

.section-modern-subtitle {
  font-size: var(--text-xl);
  color: var(--gray-600);
  max-width: 600px;
  margin: 0 auto;
}

.divider-modern {
  width: 60px;
  height: 4px;
  background: var(--gradient-primary);
  border-radius: var(--border-radius-sm);
  margin: var(--spacing-lg) auto;
}

/* ===== تحسينات الأيقونات ===== */
.icon-modern {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: var(--border-radius-xl);
  background: var(--gradient-primary);
  color: white;
  font-size: var(--text-2xl);
  margin-bottom: var(--spacing-lg);
  box-shadow: var(--shadow-lg);
}

.icon-modern-lg {
  width: 80px;
  height: 80px;
  font-size: var(--text-3xl);
}

.icon-modern-sm {
  width: 40px;
  height: 40px;
  font-size: var(--text-lg);
}

/* ===== تحسينات النماذج ===== */
.form-modern {
  background: white;
  padding: var(--spacing-2xl);
  border-radius: var(--border-radius-xl);
  box-shadow: var(--shadow-lg);
}

.form-group-modern {
  margin-bottom: var(--spacing-lg);
}

.form-label-modern {
  display: block;
  font-weight: var(--font-semibold);
  color: var(--gray-700);
  margin-bottom: var(--spacing-sm);
}

.form-input-modern {
  width: 100%;
  padding: var(--spacing-md);
  border: 2px solid var(--gray-300);
  border-radius: var(--border-radius-lg);
  font-size: var(--text-base);
  transition: all var(--transition-fast);
  background: white;
}

.form-input-modern:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(30, 58, 138, 0.1);
}

.form-textarea-modern {
  resize: vertical;
  min-height: 120px;
}

/* ===== تحسينات الجداول ===== */
.table-modern {
  width: 100%;
  background: white;
  border-radius: var(--border-radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-md);
}

.table-modern th {
  background: var(--gray-50);
  padding: var(--spacing-lg);
  font-weight: var(--font-semibold);
  color: var(--gray-700);
  border-bottom: 1px solid var(--gray-200);
}

.table-modern td {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--gray-100);
}

.table-modern tr:hover {
  background: var(--gray-50);
}

/* ===== تحسينات الإشعارات ===== */
.alert-modern {
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-lg);
  border: none;
  box-shadow: var(--shadow-md);
  margin-bottom: var(--spacing-lg);
}

.alert-modern-success {
  background: linear-gradient(135deg, var(--success-color), #34d399);
  color: white;
}

.alert-modern-warning {
  background: linear-gradient(135deg, var(--warning-color), #fcd34d);
  color: white;
}

.alert-modern-error {
  background: linear-gradient(135deg, var(--error-color), #f87171);
  color: white;
}

.alert-modern-info {
  background: linear-gradient(135deg, var(--info-color), #60a5fa);
  color: white;
}

/* ===== إصلاح مشاكل الأيقونات في اللغة العربية ===== */

/* ضمان ظهور الأيقونات بشكل صحيح في RTL */
[dir="rtl"] i.fas,
[dir="rtl"] i.far,
[dir="rtl"] i.fab,
[dir="rtl"] i.fal {
  direction: ltr !important;
  display: inline-block !important;
  text-align: center !important;
  unicode-bidi: bidi-override !important;
}

/* إعدادات خاصة لأيقونات الأزرار */
[dir="rtl"] .btn i {
  direction: ltr !important;
  display: inline-block !important;
}

/* إعدادات خاصة لأيقونات القائمة */
[dir="rtl"] .dropdown-toggle::after {
  direction: ltr !important;
}

/* إعدادات عامة للأيقونات */
[dir="rtl"] .icon-float {
  direction: ltr !important;
  display: inline-block !important;
  margin-left: var(--spacing-xs);
  margin-right: 0;
}

/* إصلاح شامل لأيقونات Font Awesome */
.rtl-layout .fa,
.rtl-layout .fas,
.rtl-layout .far,
.rtl-layout .fab,
.rtl-layout .fal {
  direction: ltr !important;
  display: inline-block !important;
  font-family: "Font Awesome 5 Free", "Font Awesome 5 Brands" !important;
  unicode-bidi: bidi-override !important;
  text-rendering: auto !important;
  -webkit-font-smoothing: antialiased !important;
}

/* إعدادات خاصة لأيقونات العلامات التجارية */
.rtl-layout .fab {
  font-family: "Font Awesome 5 Brands" !important;
  font-weight: 400 !important;
}

/* إعدادات خاصة لأيقونات المتن */
.rtl-layout .fas {
  font-family: "Font Awesome 5 Free" !important;
  font-weight: 900 !important;
}

/* إصلاح أيقونات الفوتر */
.rtl-layout footer i {
  direction: ltr !important;
  display: inline-block !important;
  unicode-bidi: bidi-override !important;
}
