#!/usr/bin/env python
import os
import django

# Configure Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'conference.settings')
django.setup()

from django.contrib.auth.models import User
from organizations.models import OfficialPerson
from core.models import AdminProfile

print("=== تحليل المسؤولين في النظام ===\n")

# عدد المسؤولين الإجمالي
total_officials = OfficialPerson.objects.count()
print(f"إجمالي المسؤولين في النظام: {total_officials}")

# عدد المسؤولين حسب من أضافهم
print(f"\nتوزيع المسؤولين حسب من أضافهم:")

# المسؤولين الذين أضافهم كل مستخدم
users_with_officials = User.objects.filter(officialperson__isnull=False).distinct()

for user in users_with_officials:
    count = OfficialPerson.objects.filter(created_by=user).count()
    admin_type = "غير محدد"
    if hasattr(user, 'admin_profile'):
        admin_type = user.admin_profile.get_admin_type_display()
    print(f"- {user.username} ({admin_type}): {count} مسؤول")

# المسؤولين بدون منشئ
no_creator = OfficialPerson.objects.filter(created_by__isnull=True).count()
if no_creator > 0:
    print(f"- بدون منشئ محدد: {no_creator} مسؤول")

print(f"\n=== معلومات المسؤولين الإداريين ===")

# معلومات المسؤولين الإداريين
admin_profiles = AdminProfile.objects.all()
for admin in admin_profiles:
    officials_count = OfficialPerson.objects.filter(created_by=admin.user).count()
    print(f"- {admin.user.username} ({admin.get_admin_type_display()}): {officials_count} مسؤول")
    if admin.is_super_admin():
        print(f"  → يمكنه رؤية جميع الـ {total_officials} مسؤول")
    else:
        print(f"  → يمكنه رؤية {officials_count} مسؤول فقط (الذين أضافهم)")

print(f"\n=== إحصائيات سريعة ===")
print(f"- إجمالي المسؤولين: {total_officials}")
print(f"- عدد المسؤولين الإداريين: {AdminProfile.objects.count()}")
print(f"- Super Admins: {AdminProfile.objects.filter(admin_type='super_admin').count()}")
print(f"- Regular Admins: {AdminProfile.objects.filter(admin_type='regular_admin').count()}")
