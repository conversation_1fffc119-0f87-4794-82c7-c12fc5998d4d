// Additional JavaScript for Organizations Page

document.addEventListener('DOMContentLoaded', function() {
    // Add animation classes to organization cards with delay
    const cards = document.querySelectorAll('.organization-card');
    cards.forEach((card, index) => {
        setTimeout(() => {
            card.classList.add('animate__animated', 'animate__fadeInUp');
        }, 100 * index);
    });
    
    // Add parallax effect to background decorations
    document.addEventListener('mousemove', function(e) {
        const decorations = document.querySelectorAll('.bg-decoration');
        const x = e.clientX / window.innerWidth;
        const y = e.clientY / window.innerHeight;
        
        decorations.forEach((decoration, index) => {
            const speed = index % 2 === 0 ? 20 : -20;
            decoration.style.transform = `translate(${x * speed}px, ${y * speed}px)`;
        });
    });
    
    // Add tooltip functionality to buttons
    if (typeof bootstrap !== 'undefined') {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
    
    // Add hover effect to organization logos
    const logos = document.querySelectorAll('.organization-logo');
    logos.forEach(logo => {
        logo.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.1) rotate(5deg)';
        });
        
        logo.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1) rotate(0deg)';
        });
    });
    
    // تحسين تفاعل أزرار الإجراءات
    const actionButtons = document.querySelectorAll('.btn-action');
    actionButtons.forEach(button => {
        // إضافة تأثيرات بصرية عند النقر والانتقال إلى الرابط
        button.addEventListener('click', function() {
            // إضافة تأثير نبض للزر عند النقر
            this.classList.add('pulse-once');
            
            // إزالة تأثير النبض بعد انتهاء التأثير
            setTimeout(() => {
                this.classList.remove('pulse-once');
            }, 300);
        });
    });
});