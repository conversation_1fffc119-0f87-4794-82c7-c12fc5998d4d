{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .delete-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        padding: 30px;
        margin-top: 50px;
        max-width: 600px;
        margin-left: auto;
        margin-right: auto;
    }
    
    .delete-header {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 30px;
        text-align: center;
    }
    
    .warning-icon {
        font-size: 4rem;
        color: #dc3545;
        margin-bottom: 20px;
    }
    
    .party-leader-info {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 30px;
    }
    
    .party-leader-photo {
        width: 100px;
        height: 100px;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid #e9ecef;
        margin-bottom: 15px;
    }
    
    .party-leader-name {
        font-size: 1.5rem;
        font-weight: bold;
        color: #495057;
        margin-bottom: 5px;
    }
    
    .party-name {
        font-size: 1.1rem;
        color: #6c757d;
        margin-bottom: 10px;
    }
    
    .info-item {
        display: flex;
        justify-content: space-between;
        padding: 8px 0;
        border-bottom: 1px solid #e9ecef;
    }
    
    .info-item:last-child {
        border-bottom: none;
    }
    
    .info-label {
        font-weight: bold;
        color: #495057;
    }
    
    .info-value {
        color: #6c757d;
    }
    
    .warning-message {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 30px;
        text-align: center;
    }
    
    .warning-message h5 {
        color: #856404;
        margin-bottom: 15px;
    }
    
    .warning-message p {
        color: #856404;
        margin-bottom: 0;
    }
    
    .btn-danger {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: bold;
        transition: all 0.3s ease;
    }
    
    .btn-danger:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
    }
    
    .btn-secondary {
        background: #6c757d;
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: bold;
        transition: all 0.3s ease;
    }
    
    .btn-secondary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(108, 117, 125, 0.4);
    }
    
    .action-buttons {
        text-align: center;
        margin-top: 30px;
    }
    
    .status-badge {
        padding: 5px 12px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: bold;
        display: inline-block;
        margin-top: 10px;
    }
    
    .status-active { background: #d4edda; color: #155724; }
    .status-inactive { background: #fff3cd; color: #856404; }
    .status-retired { background: #d1ecf1; color: #0c5460; }
    .status-deceased { background: #f8d7da; color: #721c24; }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="delete-container">
        <div class="delete-header">
            <h3><i class="fas fa-trash-alt"></i> {{ title }}</h3>
            <p class="mb-0">تأكيد حذف رئيس الحزب</p>
        </div>

        <div class="text-center">
            <i class="fas fa-exclamation-triangle warning-icon"></i>
        </div>

        <div class="warning-message">
            <h5><i class="fas fa-exclamation-circle"></i> تحذير مهم</h5>
            <p>هذا الإجراء لا يمكن التراجع عنه. سيتم حذف جميع بيانات رئيس الحزب نهائياً من النظام.</p>
        </div>

        <div class="party-leader-info">
            <div class="text-center">
                {% if party_leader.photo %}
                    <img src="{{ party_leader.photo.url }}" alt="{{ party_leader.name }}" class="party-leader-photo">
                {% else %}
                    <div class="party-leader-photo d-flex align-items-center justify-content-center mx-auto" style="background: #e9ecef;">
                        <i class="fas fa-user fa-2x text-muted"></i>
                    </div>
                {% endif %}
                
                <div class="party-leader-name">{{ party_leader.name }}</div>
                <div class="party-name">{{ party_leader.party_name }}</div>
                <div class="status-badge status-{{ party_leader.status }}">
                    {{ party_leader.get_status_display }}
                </div>
            </div>
            
            <hr class="my-4">
            
            <div class="info-item">
                <span class="info-label">المنصب:</span>
                <span class="info-value">{{ party_leader.get_title_display }}</span>
            </div>
            
            {% if party_leader.phone %}
            <div class="info-item">
                <span class="info-label">رقم الهاتف:</span>
                <span class="info-value">{{ party_leader.phone }}</span>
            </div>
            {% endif %}
            
            {% if party_leader.email %}
            <div class="info-item">
                <span class="info-label">البريد الإلكتروني:</span>
                <span class="info-value">{{ party_leader.email }}</span>
            </div>
            {% endif %}
            
            {% if party_leader.party_founded_year %}
            <div class="info-item">
                <span class="info-label">سنة تأسيس الحزب:</span>
                <span class="info-value">{{ party_leader.party_founded_year }}</span>
            </div>
            {% endif %}
            
            {% if party_leader.party_ideology %}
            <div class="info-item">
                <span class="info-label">الأيديولوجية السياسية:</span>
                <span class="info-value">{{ party_leader.party_ideology }}</span>
            </div>
            {% endif %}
            
            <div class="info-item">
                <span class="info-label">تاريخ الإضافة:</span>
                <span class="info-value">{{ party_leader.created_at|date:"Y-m-d H:i" }}</span>
            </div>
            
            <div class="info-item">
                <span class="info-label">آخر تحديث:</span>
                <span class="info-value">{{ party_leader.updated_at|date:"Y-m-d H:i" }}</span>
            </div>
        </div>

        <form method="post">
            {% csrf_token %}
            <div class="action-buttons">
                <button type="submit" class="btn btn-danger me-3" onclick="return confirm('هل أنت متأكد من حذف رئيس الحزب {{ party_leader.name }}؟ هذا الإجراء لا يمكن التراجع عنه.')">
                    <i class="fas fa-trash-alt"></i> تأكيد الحذف
                </button>
                <a href="{% url 'organizations:party_leaders' %}" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> إلغاء والعودة
                </a>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // إضافة تأكيد إضافي عند الحذف
    const deleteForm = document.querySelector('form');
    const deleteButton = deleteForm.querySelector('button[type="submit"]');
    
    deleteButton.addEventListener('click', function(e) {
        e.preventDefault();
        
        // تأكيد مزدوج للحذف
        const firstConfirm = confirm('هل أنت متأكد من حذف رئيس الحزب "{{ party_leader.name }}"؟');
        if (firstConfirm) {
            const secondConfirm = confirm('تحذير أخير: سيتم حذف جميع البيانات نهائياً. هل تريد المتابعة؟');
            if (secondConfirm) {
                deleteForm.submit();
            }
        }
    });
    
    // تأثير بصري عند التمرير فوق زر الحذف
    deleteButton.addEventListener('mouseenter', function() {
        this.innerHTML = '<i class="fas fa-skull-crossbones"></i> حذف نهائي';
    });
    
    deleteButton.addEventListener('mouseleave', function() {
        this.innerHTML = '<i class="fas fa-trash-alt"></i> تأكيد الحذف';
    });
});
</script>
{% endblock %}
