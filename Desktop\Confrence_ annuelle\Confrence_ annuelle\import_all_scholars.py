#!/usr/bin/env python
import os
import django

# Configure Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'conference.settings')
django.setup()

from organizations.models import Scholar

# مسح جميع العلماء الموجودين
Scholar.objects.all().delete()
print('تم مسح جميع العلماء الموجودين')

# تشغيل أمر الاستيراد
from django.core.management import call_command

try:
    call_command('import_scholars')
    print('تم تشغيل أمر الاستيراد بنجاح')
except Exception as e:
    print(f'خطأ في تشغيل أمر الاستيراد: {e}')

# التحقق من النتيجة
scholars_count = Scholar.objects.count()
print(f'عدد العلماء بعد الاستيراد: {scholars_count}')

# عرض أول 10 علماء
print('\nأول 10 علماء:')
for scholar in Scholar.objects.all()[:10]:
    print(f'- {scholar.get_full_title_name()}')
