{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<!-- Hero Section -->
<section class="hero bg-gradient-primary text-white py-5 position-relative overflow-hidden">
    <div class="hero-pattern position-absolute w-100 h-100 opacity-10" style="background-image: url('{% static 'img/pattern.svg' %}');"></div>
    <div class="container position-relative z-index-1">
        <div class="row align-items-center">
            <div class="col-lg-8 mx-auto text-center">
                <h1 class="display-4 fw-bold mb-3">{% trans "الإشعارات" %}</h1>
                <p class="lead mb-4">{% trans "تتبع جميع التحديثات والإشعارات المتعلقة بالدعوات" %}</p>
                <div class="d-flex justify-content-center gap-2">
                    <a href="{% url 'core:notification_list' %}" class="btn btn-light px-4 py-2">{% trans "جميع الإشعارات" %}</a>
                    <a href="{% url 'core:notification_list' %}?status=unread" class="btn btn-outline-light px-4 py-2">{% trans "غير مقروءة" %} 
                        {% if unread_count %}<span class="badge bg-danger ms-2">{{ unread_count }}</span>{% endif %}
                    </a>
                    <a href="{% url 'core:notification_list' %}?status=read" class="btn btn-outline-light px-4 py-2">{% trans "مقروءة" %}</a>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Notifications Section -->
<section class="py-5">
    <div class="container">
        {% if notifications %}
            <div class="d-flex justify-content-end mb-4">
                <a href="{% url 'core:notification_list' %}?mark_all_read=1" class="btn btn-sm btn-outline-primary">{% trans "تحديد الكل كمقروء" %}</a>
            </div>
            
            <div class="card shadow-sm mb-4">
                <div class="list-group list-group-flush">
                    {% for notification in notifications %}
                        <div class="list-group-item list-group-item-action p-4 {% if not notification.is_read %}bg-light{% endif %}">
                            <div class="d-flex w-100 justify-content-between align-items-center">
                                <div>
                                    {% if notification.notification_type == 'invitation_sent' %}
                                        <div class="badge bg-success mb-2">{% trans "دعوة مرسلة" %}</div>
                                    {% elif notification.notification_type == 'invitation_cancelled' %}
                                        <div class="badge bg-warning mb-2">{% trans "دعوة ملغاة" %}</div>
                                    {% elif notification.notification_type == 'invitation_responded' %}
                                        <div class="badge bg-info mb-2">{% trans "رد على دعوة" %}</div>
                                    {% else %}
                                        <div class="badge bg-secondary mb-2">{% trans "إشعار النظام" %}</div>
                                    {% endif %}
                                    
                                    <h5 class="mb-1">{{ notification.title }}</h5>
                                    <p class="mb-1">{{ notification.message }}</p>
                                    <small class="text-muted">{{ notification.created_at|date:"j F Y, g:i a" }}</small>
                                </div>
                                <div class="d-flex gap-2">
                                    {% if not notification.is_read %}
                                        <a href="{% url 'core:mark_notification_as_read' notification.pk %}" class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-check-circle"></i> {% trans "تحديد كمقروء" %}
                                        </a>
                                    {% endif %}
                                    
                                    {% if notification.related_object_type == 'invitation' and notification.related_object_id %}
                                        <a href="{% url 'organizations:invitation_detail' notification.related_object_id %}" class="btn btn-sm btn-primary">
                                            <i class="bi bi-envelope"></i> {% trans "عرض الدعوة" %}
                                        </a>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>
        {% else %}
            <div class="card shadow-sm">
                <div class="card-body text-center py-5">
                    <i class="bi bi-bell text-muted" style="font-size: 3rem;"></i>
                    <h4 class="mt-3">{% trans "لا توجد إشعارات" %}</h4>
                    <p class="text-muted">{% trans "ستظهر هنا الإشعارات المتعلقة بالدعوات المرسلة والملغاة" %}</p>
                </div>
            </div>
        {% endif %}
    </div>
</section>
{% endblock %}