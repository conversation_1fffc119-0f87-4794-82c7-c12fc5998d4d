{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{{ title }} - {% trans "مؤتمر السيرة النبوية السنوي" %}{% endblock %}

{% block extra_css %}
<style>
    .admin-dashboard {
        padding: 2rem 0;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
    }
    
    .dashboard-header {
        background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    }
    
    .stats-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
        border-left: 4px solid #3949ab;
    }
    
    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    }
    
    .stats-number {
        font-size: 2.5rem;
        font-weight: bold;
        color: #3949ab;
        margin-bottom: 0.5rem;
    }
    
    .stats-label {
        color: #666;
        font-size: 1rem;
        margin-bottom: 1rem;
    }
    
    .quick-actions {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        margin-bottom: 2rem;
    }
    
    .action-btn {
        display: inline-block;
        padding: 1rem 2rem;
        margin: 0.5rem;
        background: linear-gradient(135deg, #3949ab 0%, #1a237e 100%);
        color: white;
        text-decoration: none;
        border-radius: 10px;
        transition: all 0.3s ease;
        box-shadow: 0 3px 10px rgba(57, 73, 171, 0.3);
    }
    
    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(57, 73, 171, 0.4);
        color: white;
        text-decoration: none;
    }
    
    .action-btn i {
        margin-right: 0.5rem;
    }
    
    .section-title {
        color: #1a237e;
        font-weight: bold;
        margin-bottom: 1.5rem;
        border-bottom: 2px solid #3949ab;
        padding-bottom: 0.5rem;
    }
    
    .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 0.5rem;
    }
    
    .status-invited { background-color: #ffc107; }
    .status-confirmed { background-color: #28a745; }
    .status-declined { background-color: #dc3545; }
    .status-attended { background-color: #17a2b8; }
</style>
{% endblock %}

{% block content %}
<div class="admin-dashboard">
    <div class="container">
        <!-- Header -->
        <div class="dashboard-header text-center">
            <h1 class="display-4 mb-3">
                <i class="fas fa-tachometer-alt me-3"></i>
                {{ title }}
            </h1>
            <p class="lead">{% trans "لوحة التحكم الشاملة لإدارة المؤسسات والدعوات" %}</p>
        </div>

        <!-- Statistics Cards -->
        <div class="row">
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <div class="stats-number">{{ total_organizations }}</div>
                    <div class="stats-label">{% trans "إجمالي المؤسسات" %}</div>
                    <i class="fas fa-building fa-2x text-primary"></i>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <div class="stats-number">{{ government_orgs }}</div>
                    <div class="stats-label">{% trans "المؤسسات الحكومية" %}</div>
                    <i class="fas fa-landmark fa-2x text-success"></i>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <div class="stats-number">{{ private_orgs }}</div>
                    <div class="stats-label">{% trans "المؤسسات الخاصة" %}</div>
                    <i class="fas fa-industry fa-2x text-warning"></i>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <div class="stats-number">{{ scholar_orgs }}</div>
                    <div class="stats-label">{% trans "العلماء" %}</div>
                    <i class="fas fa-graduation-cap fa-2x text-info"></i>
                </div>
            </div>
        </div>

        <!-- Participation Status -->
        <div class="row">
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <div class="stats-number">{{ invited_count }}</div>
                    <div class="stats-label">
                        <span class="status-indicator status-invited"></span>
                        {% trans "مدعوة" %}
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <div class="stats-number">{{ confirmed_count }}</div>
                    <div class="stats-label">
                        <span class="status-indicator status-confirmed"></span>
                        {% trans "مؤكدة" %}
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <div class="stats-number">{{ declined_count }}</div>
                    <div class="stats-label">
                        <span class="status-indicator status-declined"></span>
                        {% trans "رفضت" %}
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stats-card text-center">
                    <div class="stats-number">{{ attended_count }}</div>
                    <div class="stats-label">
                        <span class="status-indicator status-attended"></span>
                        {% trans "حضرت" %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Invitations Statistics -->
        <div class="row">
            <div class="col-md-4">
                <div class="stats-card text-center">
                    <div class="stats-number">{{ total_invitations }}</div>
                    <div class="stats-label">{% trans "إجمالي الدعوات" %}</div>
                    <i class="fas fa-envelope fa-2x text-primary"></i>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stats-card text-center">
                    <div class="stats-number">{{ sent_invitations }}</div>
                    <div class="stats-label">{% trans "الدعوات المرسلة" %}</div>
                    <i class="fas fa-paper-plane fa-2x text-success"></i>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stats-card text-center">
                    <div class="stats-number">{{ pending_invitations }}</div>
                    <div class="stats-label">{% trans "الدعوات المعلقة" %}</div>
                    <i class="fas fa-clock fa-2x text-warning"></i>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
            <h3 class="section-title">
                <i class="fas fa-bolt me-2"></i>
                {% trans "الإجراءات السريعة" %}
            </h3>
            <div class="text-center">
                <a href="{% url 'organizations:organization_list' %}" class="action-btn">
                    <i class="fas fa-list"></i>
                    {% trans "جميع المؤسسات" %}
                </a>
                <a href="{% url 'organizations:government_organizations' %}" class="action-btn">
                    <i class="fas fa-landmark"></i>
                    {% trans "المؤسسات الحكومية" %}
                </a>
                <a href="{% url 'organizations:consulting_organizations' %}" class="action-btn">
                    <i class="fas fa-industry"></i>
                    {% trans "مؤسسات MS-Consulting" %}
                </a>
                <a href="{% url 'organizations:scholars' %}" class="action-btn">
                    <i class="fas fa-graduation-cap"></i>
                    {% trans "العلماء" %}
                </a>
                <a href="{% url 'organizations:organization_create' %}" class="action-btn">
                    <i class="fas fa-plus"></i>
                    {% trans "إضافة مؤسسة جديدة" %}
                </a>
                <a href="{% url 'organizations:invitation_list' %}" class="action-btn">
                    <i class="fas fa-envelope-open"></i>
                    {% trans "إدارة الدعوات" %}
                </a>
                <a href="{% url 'organizations:send_bulk_invitation' %}" class="action-btn">
                    <i class="fas fa-paper-plane"></i>
                    {% trans "إرسال دعوات جماعية" %}
                </a>
                <a href="{% url 'organizations:export_organizations_excel' %}" class="action-btn">
                    <i class="fas fa-download"></i>
                    {% trans "تصدير إلى Excel" %}
                </a>
                <a href="{% url 'admin:organizations_organization_changelist' %}" class="action-btn">
                    <i class="fas fa-cogs"></i>
                    {% trans "لوحة الإدارة الرئيسية" %}
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add some animation effects
    document.addEventListener('DOMContentLoaded', function() {
        // Animate stats cards on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);

        // Observe all stats cards
        document.querySelectorAll('.stats-card').forEach(card => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(20px)';
            card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(card);
        });
    });
</script>
{% endblock %}
