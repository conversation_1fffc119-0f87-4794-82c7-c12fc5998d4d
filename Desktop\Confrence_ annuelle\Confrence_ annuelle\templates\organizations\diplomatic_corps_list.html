{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .stats-card {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        border-radius: 15px;
        color: white;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }
    
    .stats-card h3 {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .stats-card p {
        margin: 0;
        opacity: 0.9;
    }
    
    .diplomat-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        margin-bottom: 20px;
    }
    
    .diplomat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.15);
    }
    
    .diplomat-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 15px 20px;
        border-radius: 15px 15px 0 0;
    }
    
    .diplomat-name {
        font-size: 1.2rem;
        font-weight: bold;
        margin: 0;
    }
    
    .diplomat-rank {
        font-size: 0.9rem;
        opacity: 0.9;
        margin: 0;
    }
    
    .status-badge {
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
    }
    
    .status-active {
        background-color: #d4edda;
        color: #155724;
    }
    
    .status-inactive {
        background-color: #f8d7da;
        color: #721c24;
    }
    
    .status-retired {
        background-color: #e2e3e5;
        color: #383d41;
    }
    
    .status-transferred {
        background-color: #d1ecf1;
        color: #0c5460;
    }
    
    .mission-badge {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        padding: 8px 15px;
        border-radius: 20px;
        font-size: 0.85rem;
        display: inline-block;
    }
    
    .rank-badge {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        color: white;
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 0.75rem;
        font-weight: bold;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="text-center mb-4">
                <h1 class="display-4 mb-3">{{ title }}</h1>
                <p class="lead text-muted">إدارة وعرض بيانات السلك الدبلوماسي الموريتاني</p>
                <div class="mt-4">
                    <a href="{% url 'organizations:add_diplomatic_corps' %}" class="btn btn-primary me-2">
                        <i class="fas fa-plus"></i> إضافة دبلوماسي جديد
                    </a>
                    <a href="{% url 'organizations:export_diplomatic_corps' %}" class="btn btn-success me-2">
                        <i class="fas fa-file-excel"></i> تصدير Excel
                    </a>

                    <!-- WhatsApp Invitation Buttons -->
                    <div class="btn-group me-2" role="group">
                        <button type="button" class="btn btn-outline-success dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fab fa-whatsapp"></i> دعوات WhatsApp
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#whatsappInvitationModal">
                                <i class="fab fa-whatsapp text-success"></i> دعوة فردية
                            </a></li>
                            <li><a class="dropdown-item" href="#" id="send-bulk-whatsapp-invitations">
                                <i class="fab fa-whatsapp text-success"></i> دعوات جماعية
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" id="send-selected-whatsapp-invitations">
                                <i class="fas fa-check-circle text-success"></i> دعوة المحددين (<span id="selected-whatsapp-count">0</span>)
                            </a></li>
                        </ul>
                    </div>

                    <!-- Email Invitation Buttons -->
                    <div class="btn-group me-2" role="group">
                        <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-envelope"></i> دعوات البريد الإلكتروني
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#emailInvitationModal">
                                <i class="fas fa-envelope text-primary"></i> دعوة فردية
                            </a></li>
                            <li><a class="dropdown-item" href="#" id="send-bulk-email-invitations">
                                <i class="fas fa-envelope text-primary"></i> دعوات جماعية
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" id="send-selected-email-invitations">
                                <i class="fas fa-envelope text-primary"></i> دعوة المحددين (<span id="selected-email-count">0</span>)
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الإحصائيات -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="stats-card text-center">
                <h3>{{ stats.total }}</h3>
                <p>إجمالي الدبلوماسيين</p>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card text-center">
                <h3>{{ stats.active }}</h3>
                <p>نشط</p>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card text-center">
                <h3>{{ stats.inactive }}</h3>
                <p>غير نشط</p>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card text-center">
                <h3>{{ stats.retired }}</h3>
                <p>متقاعد</p>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card text-center">
                <h3>{{ stats.ambassadors }}</h3>
                <p>سفراء</p>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-2">
                    <label class="form-label">البحث</label>
                    <input type="text" name="search" class="form-control" value="{{ search_query }}" placeholder="البحث بالاسم أو البلد">
                </div>
                <div class="col-md-2">
                    <label class="form-label">الرتبة</label>
                    <select name="rank" class="form-select">
                        <option value="">جميع الرتب</option>
                        {% for value, label in rank_choices %}
                            <option value="{{ value }}" {% if rank_filter == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">نوع المهمة</label>
                    <select name="mission_type" class="form-select">
                        <option value="">جميع المهام</option>
                        {% for value, label in mission_type_choices %}
                            <option value="{{ value }}" {% if mission_type_filter == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">البلد</label>
                    <select name="country" class="form-select">
                        <option value="">جميع البلدان</option>
                        {% for country in countries %}
                            <option value="{{ country }}" {% if country_filter == country %}selected{% endif %}>{{ country }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">الحالة</label>
                    <select name="status" class="form-select">
                        <option value="">جميع الحالات</option>
                        {% for value, label in status_choices %}
                            <option value="{{ value }}" {% if status_filter == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">بحث</button>
                    <a href="{% url 'organizations:diplomatic_corps_list' %}" class="btn btn-secondary">إعادة تعيين</a>
                </div>
            </form>
        </div>
    </div>

    <!-- قائمة السلك الدبلوماسي -->
    <div class="row">
        {% if diplomats %}
            {% for diplomat in diplomats %}
            <div class="col-lg-6 col-xl-4">
                <div class="card diplomat-card">
                    <div class="diplomat-header">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 me-2">
                                <input type="checkbox" class="form-check-input diplomat-checkbox" name="selected_diplomats" value="{{ diplomat.id }}" id="diplomat-{{ diplomat.id }}">
                            </div>
                            <div class="flex-grow-1">
                                <h5 class="diplomat-name mb-1">{{ diplomat.name }}</h5>
                                <p class="diplomat-rank mb-0">{{ diplomat.get_rank_display }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="mb-2">
                            <span class="rank-badge">{{ diplomat.get_rank_display }}</span>
                        </div>
                        
                        <p class="mb-2">
                            <i class="fas fa-globe text-muted me-2"></i>
                            <strong>البلد/المهمة:</strong> {{ diplomat.country }}
                        </p>
                        
                        <p class="mb-3">
                            <i class="fas fa-building text-muted me-2"></i>
                            <span class="mission-badge">{{ diplomat.get_mission_type_display }}</span>
                        </p>
                        
                        <p class="mb-2">
                            <i class="fas fa-calendar text-muted me-2"></i>
                            <strong>فترة الخدمة:</strong> 
                            {{ diplomat.start_date|date:"Y-m-d" }}
                            {% if diplomat.end_date %} - {{ diplomat.end_date|date:"Y-m-d" }}{% endif %}
                        </p>
                        
                        <p class="mb-2">
                            <i class="fas fa-clock text-muted me-2"></i>
                            <strong>مدة الخدمة:</strong> {{ diplomat.get_service_duration }}
                        </p>
                        
                        {% if diplomat.languages %}
                        <p class="mb-2">
                            <i class="fas fa-language text-muted me-2"></i>
                            <strong>اللغات:</strong> {{ diplomat.languages }}
                        </p>
                        {% endif %}
                        
                        {% if diplomat.current_position %}
                        <p class="mb-2">
                            <i class="fas fa-briefcase text-muted me-2"></i>
                            <strong>المنصب الحالي:</strong> {{ diplomat.current_position }}
                        </p>
                        {% endif %}
                        
                        <div class="mb-3">
                            <span class="status-badge status-{{ diplomat.status }}">
                                {{ diplomat.get_status_display }}
                            </span>
                        </div>
                        
                        <!-- Invitation Buttons -->
                        <div class="mb-3">
                            <div class="d-flex gap-2 flex-wrap">
                                {% if diplomat.phone %}
                                <button class="btn btn-sm btn-success send-whatsapp-btn"
                                        data-diplomat-id="{{ diplomat.id }}"
                                        data-diplomat-name="{{ diplomat.name }}"
                                        data-diplomat-phone="{{ diplomat.phone }}"
                                        data-bs-toggle="modal"
                                        data-bs-target="#whatsappInvitationModal">
                                    <i class="fab fa-whatsapp"></i> دعوة WhatsApp
                                </button>
                                {% endif %}

                                {% if diplomat.email %}
                                <button class="btn btn-sm btn-primary send-email-btn"
                                        data-diplomat-id="{{ diplomat.id }}"
                                        data-diplomat-name="{{ diplomat.name }}"
                                        data-diplomat-email="{{ diplomat.email }}"
                                        data-bs-toggle="modal"
                                        data-bs-target="#emailInvitationModal">
                                    <i class="fas fa-envelope"></i> دعوة بريد إلكتروني
                                </button>
                                {% endif %}
                            </div>
                        </div>

                        <div class="d-flex gap-2">
                            <a href="{% url 'organizations:edit_diplomatic_corps' diplomat.pk %}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-edit"></i> تعديل
                            </a>
                            <a href="{% url 'organizations:delete_diplomatic_corps' diplomat.pk %}" class="btn btn-sm btn-outline-danger">
                                <i class="fas fa-trash"></i> حذف
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-globe fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد بيانات للسلك الدبلوماسي</h5>
                    <p class="text-muted">ابدأ بإضافة أعضاء السلك الدبلوماسي</p>
                    <a href="{% url 'organizations:add_diplomatic_corps' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة دبلوماسي جديد
                    </a>
                </div>
            </div>
        {% endif %}
    </div>
</div>

<!-- WhatsApp Invitation Modal -->
<div class="modal fade" id="whatsappInvitationModal" tabindex="-1" aria-labelledby="whatsappInvitationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="whatsappInvitationModalLabel">
                    <i class="fab fa-whatsapp me-2"></i>إرسال دعوة WhatsApp
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'organizations:send_diplomat_whatsapp_invitation' %}">
                {% csrf_token %}
                <input type="hidden" id="whatsapp-diplomat-id" name="diplomat_id">
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fab fa-whatsapp me-2"></i>
                        سيتم إرسال دعوة WhatsApp إلى: <strong id="whatsapp-diplomat-name"></strong>
                        <br>رقم الهاتف: <strong id="whatsapp-diplomat-phone"></strong>
                    </div>

                    <div class="mb-3">
                        <label for="whatsapp-subject" class="form-label">موضوع الرسالة</label>
                        <input type="text" class="form-control" id="whatsapp-subject" name="subject"
                               value="دعوة للمشاركة في مؤتمر السيرة النبوية السنوي" required>
                    </div>

                    <div class="mb-3">
                        <label for="whatsapp-message" class="form-label">نص الرسالة</label>
                        <textarea class="form-control" id="whatsapp-message" name="message" rows="8" required>بسم الله الرحمن الرحيم

سعادة السفير المحترم

السلام عليكم ورحمة الله وبركاته

يسعدنا أن ندعوكم للمشاركة في مؤتمر السيرة النبوية السنوي الذي يهدف إلى نشر العلم والمعرفة حول سيرة النبي محمد صلى الله عليه وسلم.

نتطلع إلى مشاركتكم القيمة وإثراء المؤتمر بخبرتكم الدبلوماسية والدولية.

وفقكم الله وبارك فيكم

مع أطيب التحيات
لجنة تنظيم المؤتمر</textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fab fa-whatsapp me-2"></i>إرسال الدعوة
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Email Invitation Modal -->
<div class="modal fade" id="emailInvitationModal" tabindex="-1" aria-labelledby="emailInvitationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="emailInvitationModalLabel">
                    <i class="fas fa-envelope me-2"></i>إرسال دعوة بريد إلكتروني
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'organizations:send_diplomat_email_invitation' %}">
                {% csrf_token %}
                <input type="hidden" id="email-diplomat-id" name="diplomat_id">
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-envelope me-2"></i>
                        سيتم إرسال دعوة بريد إلكتروني إلى: <strong id="email-diplomat-name"></strong>
                        <br>البريد الإلكتروني: <strong id="email-diplomat-email"></strong>
                    </div>

                    <div class="mb-3">
                        <label for="email-subject" class="form-label">موضوع الرسالة</label>
                        <input type="text" class="form-control" id="email-subject" name="subject"
                               value="دعوة للمشاركة في مؤتمر السيرة النبوية السنوي" required>
                    </div>

                    <div class="mb-3">
                        <label for="email-message" class="form-label">نص الرسالة</label>
                        <textarea class="form-control" id="email-message" name="message" rows="8" required>بسم الله الرحمن الرحيم

سعادة السفير المحترم

السلام عليكم ورحمة الله وبركاته

يسعدنا أن ندعوكم للمشاركة في مؤتمر السيرة النبوية السنوي الذي يهدف إلى نشر العلم والمعرفة حول سيرة النبي محمد صلى الله عليه وسلم.

نتطلع إلى مشاركتكم القيمة وإثراء المؤتمر بخبرتكم الدبلوماسية والدولية.

وفقكم الله وبارك فيكم

مع أطيب التحيات
لجنة تنظيم المؤتمر</textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-envelope me-2"></i>إرسال الدعوة
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Checkbox functionality
    const diplomatCheckboxes = document.querySelectorAll('.diplomat-checkbox');
    const selectedCountSpan = document.getElementById('selected-whatsapp-count');
    const selectedEmailCountSpan = document.getElementById('selected-email-count');

    function updateSelectedCount() {
        const selectedCheckboxes = document.querySelectorAll('.diplomat-checkbox:checked');
        const count = selectedCheckboxes.length;

        if (selectedCountSpan) selectedCountSpan.textContent = count;
        if (selectedEmailCountSpan) selectedEmailCountSpan.textContent = count;
    }

    // Add event listeners to all checkboxes
    diplomatCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedCount);
    });

    // Add "Select All" functionality
    const selectAllBtn = document.createElement('button');
    selectAllBtn.className = 'btn btn-sm btn-outline-secondary me-2';
    selectAllBtn.innerHTML = '<i class="fas fa-check-square"></i> تحديد الكل';
    selectAllBtn.type = 'button';

    // Insert select all button before the invitation dropdown
    const invitationDropdown = document.querySelector('.btn-group');
    if (invitationDropdown && diplomatCheckboxes.length > 0) {
        invitationDropdown.parentNode.insertBefore(selectAllBtn, invitationDropdown);
    }

    let allSelected = false;
    selectAllBtn.addEventListener('click', function() {
        allSelected = !allSelected;

        diplomatCheckboxes.forEach(checkbox => {
            checkbox.checked = allSelected;
        });

        if (allSelected) {
            this.innerHTML = '<i class="fas fa-square"></i> إلغاء التحديد';
            this.classList.remove('btn-outline-secondary');
            this.classList.add('btn-secondary');
        } else {
            this.innerHTML = '<i class="fas fa-check-square"></i> تحديد الكل';
            this.classList.remove('btn-secondary');
            this.classList.add('btn-outline-secondary');
        }

        updateSelectedCount();
    });

    // Initial count update
    updateSelectedCount();

    // معالجة أزرار WhatsApp
    $('.send-whatsapp-btn').on('click', function() {
        const diplomatId = $(this).data('diplomat-id');
        const diplomatName = $(this).data('diplomat-name');
        const diplomatPhone = $(this).data('diplomat-phone');

        $('#whatsapp-diplomat-id').val(diplomatId);
        $('#whatsapp-diplomat-name').text(diplomatName);
        $('#whatsapp-diplomat-phone').text(diplomatPhone);
    });

    // معالجة أزرار البريد الإلكتروني
    $('.send-email-btn').on('click', function() {
        const diplomatId = $(this).data('diplomat-id');
        const diplomatName = $(this).data('diplomat-name');
        const diplomatEmail = $(this).data('diplomat-email');

        $('#email-diplomat-id').val(diplomatId);
        $('#email-diplomat-name').text(diplomatName);
        $('#email-diplomat-email').text(diplomatEmail);
    });

    // إرسال دعوات WhatsApp للمحددين
    $('#send-selected-whatsapp-invitations').on('click', function(e) {
        e.preventDefault();

        const selectedDiplomats = $('.diplomat-checkbox:checked');

        if (selectedDiplomats.length === 0) {
            alert('يرجى تحديد دبلوماسي واحد على الأقل');
            return;
        }

        const diplomatIds = selectedDiplomats.map(function() {
            return this.value;
        }).get();

        // فتح نموذج الدعوات الجماعية (سيتم إضافته لاحقاً)
        alert('سيتم إضافة نموذج الدعوات الجماعية قريباً');
    });

    // إرسال دعوات WhatsApp جماعية
    $('#send-bulk-whatsapp-invitations').on('click', function(e) {
        e.preventDefault();
        alert('سيتم إضافة نموذج الدعوات الجماعية قريباً');
    });

    // إرسال دعوات بريد إلكتروني للمحددين
    $('#send-selected-email-invitations').on('click', function(e) {
        e.preventDefault();

        const selectedDiplomats = $('.diplomat-checkbox:checked');

        if (selectedDiplomats.length === 0) {
            alert('يرجى تحديد دبلوماسي واحد على الأقل');
            return;
        }

        const diplomatIds = selectedDiplomats.map(function() {
            return this.value;
        }).get();

        // فتح نموذج الدعوات الجماعية (سيتم إضافته لاحقاً)
        alert('سيتم إضافة نموذج الدعوات الجماعية قريباً');
    });

    // إرسال دعوات بريد إلكتروني جماعية
    $('#send-bulk-email-invitations').on('click', function(e) {
        e.preventDefault();
        alert('سيتم إضافة نموذج الدعوات الجماعية قريباً');
    });
});
</script>
{% endblock %}
