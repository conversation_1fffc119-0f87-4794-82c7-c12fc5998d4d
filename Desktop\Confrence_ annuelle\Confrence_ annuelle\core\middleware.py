"""
Middleware للتحكم في صلاحيات المسؤولين
"""

from django.shortcuts import redirect
from django.contrib import messages
from django.urls import reverse
from django.utils.translation import gettext as _


class AdminPermissionMiddleware:
    """
    Middleware للتحكم في صلاحيات المسؤولين
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        
        # URLs التي تتطلب صلاحيات المسؤول الكبير فقط
        self.super_admin_urls = [
            '/admin-management/',
            '/admin-management/create/',
        ]
        
        # URLs التي تتطلب صلاحيات المسؤول العادي أو الكبير
        self.admin_urls = [
            '/organizations/',
            '/admin-dashboard/',
        ]

    def __call__(self, request):
        # التحقق من الصلاحيات قبل معالجة الطلب
        if not self.check_permissions(request):
            return redirect('core:home')
        
        response = self.get_response(request)
        return response

    def check_permissions(self, request):
        """التحقق من صلاحيات المستخدم للوصول للصفحة المطلوبة"""

        # تجاهل الطلبات للملفات الثابتة والـ API
        if (request.path.startswith('/static/') or
            request.path.startswith('/media/') or
            request.path.startswith('/api/')):
            return True

        # السماح بالوصول للصفحات العامة
        public_paths = ['/', '/accounts/', '/about/', '/contact/']
        if any(request.path.startswith(path) for path in public_paths):
            return True

        # السماح للمستخدمين غير المسجلين بالوصول للصفحات العامة
        if not request.user.is_authenticated:
            return True
        
        # التحقق من وجود ملف المسؤول للمستخدمين المسجلين
        if request.user.is_staff and not hasattr(request.user, 'admin_profile'):
            messages.error(request, _('حسابك غير مكتمل. يرجى التواصل مع الإدارة'))
            return False
        
        # التحقق من صلاحيات المسؤول الكبير
        if any(request.path.startswith(url) for url in self.super_admin_urls):
            if not (hasattr(request.user, 'admin_profile') and 
                    request.user.admin_profile.is_super_admin()):
                messages.error(request, _('هذه الصفحة مخصصة للمسؤولين الكبار فقط'))
                return False
        
        # التحقق من صلاحيات المسؤول العادي
        elif any(request.path.startswith(url) for url in self.admin_urls):
            if not (hasattr(request.user, 'admin_profile') and 
                    request.user.admin_profile.is_active):
                messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه الصفحة'))
                return False
        
        return True


class DataFilterMiddleware:
    """
    Middleware لفلترة البيانات حسب المسؤول
    """
    
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # إضافة معلومات المسؤول للـ request
        if (request.user.is_authenticated and 
            hasattr(request.user, 'admin_profile')):
            request.admin_profile = request.user.admin_profile
            request.is_super_admin = request.user.admin_profile.is_super_admin()
            request.is_regular_admin = request.user.admin_profile.is_regular_admin()
        else:
            request.admin_profile = None
            request.is_super_admin = False
            request.is_regular_admin = False
        
        response = self.get_response(request)
        return response


class SecurityMiddleware:
    """
    Middleware إضافي للأمان
    """
    
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # منع الوصول المباشر لملفات النظام
        forbidden_paths = [
            '/admin/',  # منع الوصول لـ Django admin إلا للمسؤول الكبير
        ]
        
        if request.path in forbidden_paths:
            if not (request.user.is_authenticated and 
                    hasattr(request.user, 'admin_profile') and
                    request.user.admin_profile.is_super_admin()):
                messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه الصفحة'))
                return redirect('core:home')
        
        response = self.get_response(request)
        return response
