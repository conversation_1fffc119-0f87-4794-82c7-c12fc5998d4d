# Generated by Django 5.1.2 on 2025-05-27 07:45

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='conference',
            options={'verbose_name': 'Conference', 'verbose_name_plural': 'Conferences'},
        ),
        migrations.AlterModelOptions(
            name='conferenceedition',
            options={'ordering': ['-year'], 'verbose_name': 'Conference edition', 'verbose_name_plural': 'Conference editions'},
        ),
        migrations.AlterUniqueTogether(
            name='conferenceedition',
            unique_together=set(),
        ),
        migrations.RemoveField(
            model_name='conference',
            name='banner_image',
        ),
        migrations.RemoveField(
            model_name='conference',
            name='end_date',
        ),
        migrations.RemoveField(
            model_name='conference',
            name='location',
        ),
        migrations.RemoveField(
            model_name='conference',
            name='start_date',
        ),
        migrations.AddField(
            model_name='conference',
            name='address',
            field=models.TextField(blank=True, verbose_name='Address'),
        ),
        migrations.AddField(
            model_name='conference',
            name='banner',
            field=models.ImageField(blank=True, null=True, upload_to='conferences/banners/', verbose_name='Banner'),
        ),
        migrations.AddField(
            model_name='conference',
            name='email',
            field=models.EmailField(blank=True, max_length=254, verbose_name='Email'),
        ),
        migrations.AddField(
            model_name='conference',
            name='logo',
            field=models.ImageField(blank=True, null=True, upload_to='conferences/logos/', verbose_name='Logo'),
        ),
        migrations.AddField(
            model_name='conference',
            name='phone',
            field=models.CharField(blank=True, max_length=20, verbose_name='Phone'),
        ),
        migrations.AddField(
            model_name='conference',
            name='website',
            field=models.URLField(blank=True, verbose_name='Website'),
        ),
        migrations.AddField(
            model_name='conferenceedition',
            name='banner',
            field=models.ImageField(blank=True, null=True, upload_to='editions/banners/', verbose_name='Banner'),
        ),
        migrations.AddField(
            model_name='conferenceedition',
            name='location',
            field=models.CharField(blank=True, max_length=255, verbose_name='Location'),
        ),
        migrations.AlterField(
            model_name='conference',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, verbose_name='Created at'),
        ),
        migrations.AlterField(
            model_name='conference',
            name='description',
            field=models.TextField(blank=True, verbose_name='Description'),
        ),
        migrations.AlterField(
            model_name='conference',
            name='is_active',
            field=models.BooleanField(default=True, verbose_name='Is active'),
        ),
        migrations.AlterField(
            model_name='conference',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='Updated at'),
        ),
        migrations.AlterField(
            model_name='conferenceedition',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, verbose_name='Created at'),
        ),
        migrations.AlterField(
            model_name='conferenceedition',
            name='description',
            field=models.TextField(blank=True, verbose_name='Description'),
        ),
        migrations.AlterField(
            model_name='conferenceedition',
            name='end_date',
            field=models.DateField(verbose_name='End date'),
        ),
        migrations.AlterField(
            model_name='conferenceedition',
            name='is_active',
            field=models.BooleanField(default=True, verbose_name='Is active'),
        ),
        migrations.AlterField(
            model_name='conferenceedition',
            name='start_date',
            field=models.DateField(verbose_name='Start date'),
        ),
        migrations.AlterField(
            model_name='conferenceedition',
            name='theme',
            field=models.CharField(blank=True, max_length=255, verbose_name='Theme'),
        ),
        migrations.AlterField(
            model_name='conferenceedition',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='Updated at'),
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255, verbose_name='Title')),
                ('message', models.TextField(verbose_name='Message')),
                ('notification_type', models.CharField(choices=[('invitation_sent', 'Invitation Sent'), ('invitation_cancelled', 'Invitation Cancelled'), ('invitation_responded', 'Invitation Responded'), ('system', 'System Notification')], default='system', max_length=50, verbose_name='Type')),
                ('is_read', models.BooleanField(default=False, verbose_name='Is read')),
                ('related_object_id', models.PositiveIntegerField(blank=True, null=True, verbose_name='Related object ID')),
                ('related_object_type', models.CharField(blank=True, max_length=100, null=True, verbose_name='Related object type')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created at')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='notifications', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Notification',
                'verbose_name_plural': 'Notifications',
                'ordering': ['-created_at'],
            },
        ),
        migrations.RemoveField(
            model_name='conferenceedition',
            name='banner_image',
        ),
    ]
