{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "خريطة المنظمات" %}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=" crossorigin="anonymous" />
<style>
    /* Hero Mini Styles */
    .hero-mini {
        background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
        color: white;
        padding: 3rem 0;
        text-align: center;
        position: relative;
        overflow: hidden;
    }
    
    .hero-mini::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url('{% static "img/pattern.png" %}');
        opacity: 0.1;
    }
    
    #map {
        height: 600px;
        width: 100%;
        border-radius: 0.5rem;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
    }
    
    .map-container {
        position: relative;
    }
    
    .map-filters {
        position: absolute;
        top: 20px;
        right: 20px;
        z-index: 1000;
        background: white;
        padding: 1rem;
        border-radius: 0.5rem;
        box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
    }
    
    .status-badge {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 5px;
    }
    
    .status-invited { background-color: #ffc107; }
    .status-confirmed { background-color: #28a745; }
    .status-declined { background-color: #dc3545; }
    .status-attended { background-color: #007bff; }
</style>
{% endblock %}

{% block extra_js %}
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js" integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo=" crossorigin="anonymous"></script>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="hero-mini text-white py-4 mb-5">
    <div class="container">
        <div class="row">
            <div class="col-md-8 mx-auto text-center">
                <h1 class="display-5 fw-bold mb-3"><i class="fas fa-map-marker-alt me-2"></i>{% trans "خريطة المنظمات" %}</h1>
                <p class="lead">{% trans "استعرض مواقع المنظمات المشاركة في المؤتمر" %}</p>
            </div>
        </div>
    </div>
</div>

<div class="container mb-5">
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h5 class="card-title mb-0"><i class="fas fa-filter me-2"></i>{% trans "تصفية حسب الحالة" %}</h5>
                        <a href="{% url 'organizations:organization_list' %}" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-list me-1"></i> {% trans "العودة إلى القائمة" %}
                        </a>
                    </div>
                    
                    <div class="btn-group" role="group">
                        <a href="{% url 'organizations:organization_map' %}" class="btn btn-outline-primary {% if not current_status %}active{% endif %}">
                            {% trans "الكل" %}
                        </a>
                        <a href="{% url 'organizations:organization_map' %}?status=invited" class="btn btn-outline-warning {% if current_status == 'invited' %}active{% endif %}">
                            <span class="status-badge status-invited"></span> {% trans "مدعوة" %}
                        </a>
                        <a href="{% url 'organizations:organization_map' %}?status=confirmed" class="btn btn-outline-success {% if current_status == 'confirmed' %}active{% endif %}">
                            <span class="status-badge status-confirmed"></span> {% trans "مؤكدة" %}
                        </a>
                        <a href="{% url 'organizations:organization_map' %}?status=declined" class="btn btn-outline-danger {% if current_status == 'declined' %}active{% endif %}">
                            <span class="status-badge status-declined"></span> {% trans "معتذرة" %}
                        </a>
                        <a href="{% url 'organizations:organization_map' %}?status=attended" class="btn btn-outline-info {% if current_status == 'attended' %}active{% endif %}">
                            <span class="status-badge status-attended"></span> {% trans "حضرت" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-12">
            <div class="map-container mb-4">
                <div id="map"></div>
            </div>
            
            <div class="card mt-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>{% trans "إحصائيات المنظمات" %}</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3 col-6 mb-3 mb-md-0">
                            <div class="p-3 border rounded">
                                <h3 class="text-warning">{{ organizations|length }}</h3>
                                <p class="mb-0">{% trans "إجمالي المنظمات" %}</p>
                            </div>
                        </div>
                        <div class="col-md-3 col-6 mb-3 mb-md-0">
                            <div class="p-3 border rounded">
                                <h3 class="text-success">{{ organizations|dictsortbykey:"participation_status"|dictsort:"confirmed"|length }}</h3>
                                <p class="mb-0">{% trans "مؤكدة الحضور" %}</p>
                            </div>
                        </div>
                        <div class="col-md-3 col-6">
                            <div class="p-3 border rounded">
                                <h3 class="text-danger">{{ organizations|dictsortbykey:"participation_status"|dictsort:"declined"|length }}</h3>
                                <p class="mb-0">{% trans "معتذرة عن الحضور" %}</p>
                            </div>
                        </div>
                        <div class="col-md-3 col-6">
                            <div class="p-3 border rounded">
                                <h3 class="text-info">{{ organizations|dictsortbykey:"participation_status"|dictsort:"attended"|length }}</h3>
                                <p class="mb-0">{% trans "حضرت بالفعل" %}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize the map
        var map = L.map('map').setView([34.0479, -6.8145], 6); // Morocco centered
        
        // Add OpenStreetMap tile layer
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(map);
        
        // Define marker icons for different statuses
        var icons = {
            'invited': L.icon({
                iconUrl: '{% static "img/marker-yellow.png" %}',
                iconSize: [25, 41],
                iconAnchor: [12, 41],
                popupAnchor: [1, -34]
            }),
            'confirmed': L.icon({
                iconUrl: '{% static "img/marker-green.png" %}',
                iconSize: [25, 41],
                iconAnchor: [12, 41],
                popupAnchor: [1, -34]
            }),
            'declined': L.icon({
                iconUrl: '{% static "img/marker-red.png" %}',
                iconSize: [25, 41],
                iconAnchor: [12, 41],
                popupAnchor: [1, -34]
            }),
            'attended': L.icon({
                iconUrl: '{% static "img/marker-blue.png" %}',
                iconSize: [25, 41],
                iconAnchor: [12, 41],
                popupAnchor: [1, -34]
            }),
            'default': L.icon({
                iconUrl: '{% static "img/marker-gray.png" %}',
                iconSize: [25, 41],
                iconAnchor: [12, 41],
                popupAnchor: [1, -34]
            })
        };
        
        // Add markers for organizations
        var markers = [];
        {% for org in organizations %}
            {% if org.latitude and org.longitude %}
                var marker = L.marker([{{ org.latitude }}, {{ org.longitude }}], {
                    icon: icons['{{ org.participation_status }}'] || icons['default']
                }).addTo(map);
                
                marker.bindPopup(`
                    <div class="text-center mb-2">
                        {% if org.logo %}
                            <img src="{{ org.logo.url }}" alt="{{ org.name }}" style="max-width: 80px; max-height: 80px; border-radius: 50%; margin-bottom: 10px;">
                        {% else %}
                            <div style="width: 80px; height: 80px; border-radius: 50%; background-color: #f8f9fa; display: flex; align-items: center; justify-content: center; margin: 0 auto 10px;">
                                <i class="fas fa-building fa-2x text-primary"></i>
                            </div>
                        {% endif %}
                        <h5>{% if org.name_fr %}{{ org.name_fr }}{% else %}{{ org.name }}{% endif %}</h5>
                    </div>
                    <div class="mb-2">
                        <span class="badge bg-{{ org.get_status_color() }}">{{ org.get_participation_status_display() }}</span>
                    </div>
                    {% if org.address %}
                        <div class="mb-2">
                            <i class="fas fa-map-marker-alt text-danger"></i> {{ org.address }}
                        </div>
                    {% endif %}
                    <div class="text-center mt-3">
                        <a href="{% url 'organizations:organization_detail' org.pk %}" class="btn btn-sm btn-primary">
                            <i class="fas fa-info-circle"></i> {% trans "التفاصيل" %}
                        </a>
                    </div>
                `);
                
                markers.push(marker);
            {% endif %}
        {% endfor %}
        
        // If we have markers, fit the map to show all of them
        if (markers.length > 0) {
            var group = new L.featureGroup(markers);
            map.fitBounds(group.getBounds().pad(0.1));
        }
    });
</script>
{% endblock %}