from django.core.management.base import BaseCommand
from organizations.models import Scholar
import re

class Command(BaseCommand):
    help = 'استيراد بيانات العلماء من البيانات المحددة'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear',
            action='store_true',
            help='مسح جميع العلماء الموجودين قبل الاستيراد',
        )

    def handle(self, *args, **options):
        if options['clear']:
            Scholar.objects.all().delete()
            self.stdout.write(
                self.style.WARNING('تم مسح جميع العلماء الموجودين')
            )

        # البيانات من الصور المرسلة
        scholars_data = [
            {
                'name': 'شيخ محمد فال (ابن) بن عبد الله',
                'title': 'sheikh',
                'position': 'شيخ العلامة محمد الحسين أحمد الخديم',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ محمد يحي ابن فدي',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'خليفة الشيخ المجلل الشيخ التجاني بن الشيخ الهادي',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'خليفة الشيخ المجلل الشيخ الخليل بن الشيخان',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'خليفة الشيخ المجلل الشيخ أحمد بن الشيخان',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'خليفة الشيخ المجلل الشيخ محمد بن الشيخ عبد الله',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'خليفة الشيخ المجلل الشيخ محمد عبد الله ولد السيد',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'خليفة الشيخ المجلل الشيخ باي ولد السيد',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'خليفة الشيخ الحاج عبد الله بن المشري',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'خليفة الشيخ المجلل الشيخ إبراهيم ولد خيري',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ محمد يحي ولد خيري',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'خليفة المجلل الشيخ محمد الأمين ابن بدي',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'خليفة الشيخ المجلل الشيخ أحمد ابن العابد',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'خليفة الشيخ المجلل الشيخ مكي بن الشيخ التلميذي',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'العلامة المجلل الشيخ محمد محمود ولد الطلبة',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'العلامة المجلل الشيخ محمد الكبير ابن دهاه',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ محمد عبد الله ولد آل',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'خليفة الشيخ المجلل الشيخ أحمد الشيخ محمدن الطلبه',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ يحي بن الشيخ محمدن الطلبه',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ محمد قال ولد الشيخ سيدي محمد',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ محمد ولد الشيخ أبو المعالي',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ عثمان ولد الشيخ أبو المعالي',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ الولي محمدن ابن محمودا',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'خليفة الشيخ المجلل الشيخ أحمد عبد الله جاه',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ الطالب اخيار ولد الشيخ هاشم',
                'title': 'sheikh',
                'position': 'رئيس المجلس الأعلى للزكاة',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ الطالب بوي ولد الشيخ إباه',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ محمد الزين بن القاسم',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ عبد الله ابن بيه',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ محمد محمود ولد الغوثاني',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ علي الرضا بن محمد ناجي',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ غيث بن اعل الشيخ',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ محمد فاضل بن اعل الشيخ',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ سيدي محمد (الفخامة) ولد الشيخ سيديا',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'المنسق العام والمرجع الروحي للاتحاد العام للوزراء والهيئات ذات السند الكوني في القارة',
                'title': 'sheikh',
                'position': 'الشيخ سيدي أعمر سيديا الكوني',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ سيدي بوي الشيخ محمد فاضل',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ بشراي ولد الشيخ ماء العينين',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ أحمد بعب صبار',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ كان مام',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ خاجيل عبد الرحمن',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ أمين ولد الشواف',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'تيزيز عبد الله يا',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'خليفة الشيخ المجلل الشيخ أمين ولد الصوفي',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الدكتور محمد الحافظ الحكمي',
                'title': 'doctor',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ الباي ولد جلاس',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ عبد الفتاح بياه',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ الكوري أداعه',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ محمد سالم أمون',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ عبد العزيز الشيخ إباه',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ المحفوظ ولد الشيخ القاضي',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ محمد قال العليه',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ محمد محمود ولد أحمد بورة الرباني',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ محضض باب امين',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ بتوك محمودن',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ عبد الله ولد اعل سالم',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ محمد المختار ولد امراله',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ أحمد الحاج',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'معالي الوزير الدكتور أبو بكر أحمد',
                'title': 'doctor',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ محمد الأعظف سيدي',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ أحمدو سالم ولد محمد بحظيه',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ محمد بحظيه المختار الحسين',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ محمد فاضل محمد الأمين',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'خليفة ما لمرابط محمد الأمين',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ محمد عبد الرحمن فدي',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'خليفة الداحمد وحبيب الرحمن',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ محمد بتار',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ محمد بتار',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ إبراهيم الشيخ سيدي المختار الشيخ سيديا',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ باب طال',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ أحمد سالم الشيخ المستعين',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ محمد محمود ولد محمد لول',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ الأمانة ولد الداه',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ أحمد ولد محمد مسكه',
                'title': 'sheikh',
                'position': '',
                'participation_status': 'invited'
            },
            {
                'name': 'الشيخ بن صباح',
                'title': 'sheikh',
                'position': 'الأمين العام لهيئة العلماء الموريتانيين',
                'participation_status': 'invited'
            }
        ]

        created_count = 0
        for scholar_data in scholars_data:
            # تنظيف الاسم
            name = scholar_data['name'].strip()
            
            # استخراج اللقب من الاسم إذا كان موجوداً
            title = scholar_data.get('title', 'sheikh')
            if name.startswith('الدكتور'):
                title = 'doctor'
                name = name.replace('الدكتور', '').strip()
            elif name.startswith('الأستاذ'):
                title = 'professor'
                name = name.replace('الأستاذ', '').strip()
            elif name.startswith('الشيخ'):
                title = 'sheikh'
                name = name.replace('الشيخ', '').strip()
            elif name.startswith('معالي'):
                name = name.replace('معالي الوزير', '').strip()
                if 'الدكتور' in name:
                    title = 'doctor'
                    name = name.replace('الدكتور', '').strip()

            # إنشاء أو تحديث العالم
            scholar, created = Scholar.objects.get_or_create(
                name=name,
                defaults={
                    'title': title,
                    'full_name': scholar_data['name'],
                    'position': scholar_data.get('position', ''),
                    'participation_status': scholar_data.get('participation_status', 'invited'),
                    'country': 'موريتانيا'  # افتراضياً
                }
            )
            
            if created:
                created_count += 1
                self.stdout.write(f'تم إنشاء: {scholar.get_full_title_name()}')
            else:
                self.stdout.write(f'موجود مسبقاً: {scholar.get_full_title_name()}')

        self.stdout.write(
            self.style.SUCCESS(f'تم استيراد {created_count} عالم بنجاح')
        )
