{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_modify %}

{% block extrahead %}
{{ block.super }}
<style>
    .form-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        padding: 2rem;
        margin: 20px 0;
    }

    .form-row {
        margin-bottom: 1.5rem;
        display: flex;
        flex-direction: column;
    }

    .form-row label {
        font-weight: 600;
        color: var(--secondary-color);
        margin-bottom: 0.5rem;
        display: block;
        font-size: 1rem;
    }

    .form-row input,
    .form-row select,
    .form-row textarea {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 12px 15px;
        transition: all 0.3s ease;
        font-size: 1rem;
        background: white;
        color: var(--dark-color);
    }

    .form-row input:focus,
    .form-row select:focus,
    .form-row textarea:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(44, 90, 160, 0.1);
        outline: none;
    }

    .form-row textarea {
        min-height: 120px;
        resize: vertical;
    }

    .form-row .help {
        font-size: 0.9rem;
        color: #6c757d;
        margin-top: 0.5rem;
        font-style: italic;
    }

    .fieldset {
        border: none;
        margin: 0 0 2rem 0;
        padding: 0;
    }

    .fieldset h2 {
        background: linear-gradient(135deg, var(--primary-color), var(--info-color));
        color: white;
        padding: 1rem 1.5rem;
        margin: 0 0 1.5rem 0;
        border-radius: 10px;
        font-weight: 600;
        font-size: 1.1rem;
        display: flex;
        align-items: center;
    }

    .fieldset h2:before {
        content: '📝';
        margin-left: 10px;
        font-size: 1.2rem;
    }

    .fieldset.collapse h2 {
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .fieldset.collapse h2:hover {
        background: linear-gradient(135deg, var(--info-color), var(--primary-color));
        transform: translateY(-2px);
    }

    .fieldset.collapse h2:after {
        content: '▼';
        margin-right: auto;
        transition: transform 0.3s ease;
    }

    .fieldset.collapse.collapsed h2:after {
        transform: rotate(-90deg);
    }

    .fieldset.collapse .fieldset-content {
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .fieldset.collapse.collapsed .fieldset-content {
        max-height: 0;
        opacity: 0;
        margin: 0;
    }

    .submit-row {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 10px;
        padding: 1.5rem;
        margin: 2rem 0;
        display: flex;
        gap: 1rem;
        align-items: center;
        justify-content: flex-end;
    }

    .submit-row input[type="submit"],
    .submit-row .button {
        background: linear-gradient(135deg, var(--primary-color), var(--info-color));
        color: white;
        border: none;
        border-radius: 8px;
        padding: 12px 24px;
        font-weight: 600;
        font-size: 1rem;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    .submit-row input[type="submit"]:hover,
    .submit-row .button:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(44, 90, 160, 0.4);
    }

    .submit-row .default {
        background: linear-gradient(135deg, var(--success-color), #2ecc71);
    }

    .submit-row .default:hover {
        box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4);
    }

    .submit-row .deletelink {
        background: linear-gradient(135deg, var(--danger-color), #c0392b);
    }

    .submit-row .deletelink:hover {
        box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
    }

    .submit-row .deletelink:before {
        content: '🗑️';
        margin-left: 5px;
    }

    .submit-row input[type="submit"]:before {
        content: '💾';
        margin-left: 5px;
    }

    .errorlist {
        background: rgba(231, 76, 60, 0.1);
        border: 2px solid var(--danger-color);
        border-radius: 8px;
        padding: 1rem 1.5rem;
        margin: 1rem 0;
        color: var(--danger-color);
        list-style: none;
    }

    .errorlist li {
        margin: 0.5rem 0;
        font-weight: 500;
    }

    .errorlist:before {
        content: '⚠️';
        margin-left: 10px;
        font-size: 1.2rem;
    }

    .errors {
        background: rgba(231, 76, 60, 0.1);
        border: 2px solid var(--danger-color);
        border-radius: 8px;
        padding: 1rem 1.5rem;
        margin: 1rem 0;
        color: var(--danger-color);
    }

    .errors h3 {
        margin: 0 0 1rem 0;
        font-weight: 600;
        display: flex;
        align-items: center;
    }

    .errors h3:before {
        content: '❌';
        margin-left: 10px;
    }

    .inline-group {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        margin: 1.5rem 0;
        border: 2px solid #e9ecef;
    }

    .inline-group h2 {
        background: linear-gradient(135deg, var(--secondary-color), #2c3e50);
        color: white;
        padding: 1rem 1.5rem;
        margin: -1.5rem -1.5rem 1.5rem -1.5rem;
        border-radius: 8px 8px 0 0;
        font-weight: 600;
        display: flex;
        align-items: center;
    }

    .inline-group h2:before {
        content: '📋';
        margin-left: 10px;
    }

    .inline-related {
        background: white;
        border-radius: 8px;
        padding: 1rem;
        margin: 1rem 0;
        border: 1px solid #dee2e6;
        transition: all 0.3s ease;
    }

    .inline-related:hover {
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        transform: translateY(-2px);
    }

    .add-row {
        text-align: center;
        margin: 1rem 0;
    }

    .add-row a {
        background: linear-gradient(135deg, var(--success-color), #2ecc71);
        color: white;
        border-radius: 8px;
        padding: 10px 20px;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    .add-row a:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4);
    }

    .add-row a:before {
        content: '+';
        font-weight: bold;
        font-size: 1.2rem;
    }

    .file-upload {
        border: 2px dashed #dee2e6;
        border-radius: 8px;
        padding: 2rem;
        text-align: center;
        transition: all 0.3s ease;
        background: #f8f9fa;
    }

    .file-upload:hover {
        border-color: var(--primary-color);
        background: rgba(44, 90, 160, 0.05);
    }

    .file-upload input[type="file"] {
        border: none;
        background: none;
        padding: 0;
    }

    .checkbox-row {
        display: flex;
        align-items: center;
        gap: 10px;
        margin: 1rem 0;
    }

    .checkbox-row input[type="checkbox"] {
        transform: scale(1.2);
        accent-color: var(--primary-color);
    }

    .checkbox-row label {
        margin: 0;
        font-weight: 500;
        cursor: pointer;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .form-container {
            padding: 1rem;
            margin: 10px;
        }
        
        .submit-row {
            flex-direction: column;
            align-items: stretch;
        }
        
        .submit-row input[type="submit"],
        .submit-row .button {
            width: 100%;
            justify-content: center;
        }
        
        .fieldset h2 {
            font-size: 1rem;
            padding: 0.8rem 1rem;
        }
        
        .inline-group {
            padding: 1rem;
        }
        
        .inline-group h2 {
            margin: -1rem -1rem 1rem -1rem;
            padding: 0.8rem 1rem;
        }
    }
</style>
{% endblock %}

{% block content_title %}
<h1 style="color: var(--primary-color); font-weight: 600; margin-bottom: 1rem; display: flex; align-items: center;">
    {% if add %}
        <i class="fas fa-plus" style="margin-left: 10px;"></i>
        {% blocktrans with name=opts.verbose_name %}إضافة {{ name }}{% endblocktrans %}
    {% else %}
        <i class="fas fa-edit" style="margin-left: 10px;"></i>
        {% blocktrans with name=opts.verbose_name %}تعديل {{ name }}{% endblocktrans %}
    {% endif %}
</h1>
{% endblock %}

{% block content %}
<div id="content-main">
    {% block object-tools %}
        {% if change %}{% if not is_popup %}
            <ul class="object-tools">
                {% block object-tools-items %}
                    <li>
                        <a href="{% url opts|admin_urlname:'history' original.pk|admin_urlquote %}" class="historylink">تاريخ التعديلات</a>
                    </li>
                    {% if has_absolute_url %}
                        <li>
                            <a href="{% url 'admin:view_on_site' content_type_id original.pk %}" class="viewsitelink">عرض في الموقع</a>
                        </li>
                    {% endif %}
                {% endblock %}
            </ul>
        {% endif %}{% endif %}
    {% endblock %}

    <div class="form-container">
        <form {% if has_file_field %}enctype="multipart/form-data" {% endif %}action="{{ form_url }}" method="post" id="{{ opts.model_name }}_form" novalidate>{% csrf_token %}
            <div>
                {% if is_popup %}<input type="hidden" name="{{ is_popup_var }}" value="1">{% endif %}
                {% if to_field %}<input type="hidden" name="{{ to_field_var }}" value="{{ to_field }}">{% endif %}
                {% if save_on_top %}{% block submit_buttons_top %}{% submit_row %}{% endblock %}{% endif %}
                
                {% if errors %}
                    <div class="errors">
                        <h3>{% if errors|length == 1 %}يرجى تصحيح الخطأ أدناه.{% else %}يرجى تصحيح الأخطاء أدناه.{% endif %}</h3>
                        {{ adminform.form.non_field_errors }}
                    </div>
                {% endif %}

                {% block field_sets %}
                    {% for fieldset in adminform %}
                        {% include "admin/includes/fieldset.html" %}
                    {% endfor %}
                {% endblock %}

                {% block after_field_sets %}{% endblock %}

                {% block inline_field_sets %}
                    {% for inline_admin_formset in inline_admin_formsets %}
                        {% include inline_admin_formset.opts.template %}
                    {% endfor %}
                {% endblock %}

                {% block after_related_objects %}{% endblock %}

                {% block submit_buttons_bottom %}{% submit_row %}{% endblock %}

                {% block admin_change_form_document_ready %}
                    <script type="text/javascript"
                            id="django-admin-form-add-constants"
                            src="{% static "admin/js/change_form.js" %}"
                            {% if adminform and add %}
                                data-model-name="{{ opts.model_name }}"
                            {% endif %}
                            async>
                    </script>
                {% endblock %}

                {# JavaScript for prepopulated fields #}
                {% prepopulated_fields_js %}
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // إضافة وظائف تفاعلية للنماذج
    
    // طي وفتح المجموعات القابلة للطي
    const collapsibleFieldsets = document.querySelectorAll('.fieldset.collapse h2');
    collapsibleFieldsets.forEach(function(header) {
        header.addEventListener('click', function() {
            const fieldset = this.parentElement;
            fieldset.classList.toggle('collapsed');
        });
    });
    
    // تحسين رفع الملفات
    const fileInputs = document.querySelectorAll('input[type="file"]');
    fileInputs.forEach(function(input) {
        const container = input.closest('.form-row');
        if (container) {
            container.classList.add('file-upload');
            
            input.addEventListener('change', function() {
                if (this.files.length > 0) {
                    container.style.borderColor = 'var(--success-color)';
                    container.style.backgroundColor = 'rgba(39, 174, 96, 0.05)';
                }
            });
        }
    });
    
    // تحسين checkboxes
    const checkboxes = document.querySelectorAll('input[type="checkbox"]');
    checkboxes.forEach(function(checkbox) {
        const container = checkbox.closest('.form-row');
        if (container && !container.classList.contains('checkbox-row')) {
            container.classList.add('checkbox-row');
        }
    });
});
</script>
{% endblock %}
