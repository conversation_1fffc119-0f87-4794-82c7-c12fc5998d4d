"""
Views خاصة بإدارة المسؤولين - للمسؤول الكبير فقط
"""

from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.models import User
from django.contrib import messages
from django.utils.translation import gettext as _
from django.db.models import Q, Count
from django.core.paginator import Paginator
from django.http import JsonResponse, HttpResponse
from django.views.decorators.http import require_http_methods
from django.utils import timezone
from datetime import datetime, timedelta
import json
import csv

from .models import AdminProfile
from .decorators import super_admin_required, get_admin_context
from .forms import AdminProfileForm, CreateAdminForm


@super_admin_required
def admin_list(request):
    """عرض قائمة جميع المسؤولين - للمسؤول الكبير فقط"""
    
    # البحث والفلترة
    search_query = request.GET.get('search', '')
    admin_type_filter = request.GET.get('admin_type', '')
    status_filter = request.GET.get('status', '')
    
    # الحصول على جميع المسؤولين
    admins = AdminProfile.objects.select_related('user').all()
    
    # تطبيق الفلاتر
    if search_query:
        admins = admins.filter(
            Q(user__username__icontains=search_query) |
            Q(user__first_name__icontains=search_query) |
            Q(user__last_name__icontains=search_query) |
            Q(full_name__icontains=search_query) |
            Q(user__email__icontains=search_query)
        )
    
    if admin_type_filter:
        admins = admins.filter(admin_type=admin_type_filter)
    
    if status_filter == 'active':
        admins = admins.filter(is_active=True)
    elif status_filter == 'inactive':
        admins = admins.filter(is_active=False)
    
    # ترتيب النتائج
    admins = admins.order_by('-created_at')
    
    # التصفح
    paginator = Paginator(admins, 20)
    page_number = request.GET.get('page')
    page_obj = paginator.get_page(page_number)
    
    # إحصائيات محسنة
    total_admins = AdminProfile.objects.count()
    super_admins = AdminProfile.objects.filter(admin_type='super_admin').count()
    regular_admins = AdminProfile.objects.filter(admin_type='regular_admin').count()
    active_admins = AdminProfile.objects.filter(is_active=True).count()
    inactive_admins = AdminProfile.objects.filter(is_active=False).count()

    # إحصائيات إضافية
    today = timezone.now().date()
    week_ago = today - timedelta(days=7)
    month_ago = today - timedelta(days=30)

    new_admins_today = AdminProfile.objects.filter(created_at__date=today).count()
    new_admins_week = AdminProfile.objects.filter(created_at__date__gte=week_ago).count()
    new_admins_month = AdminProfile.objects.filter(created_at__date__gte=month_ago).count()

    stats = {
        'total_admins': total_admins,
        'super_admins': super_admins,
        'regular_admins': regular_admins,
        'active_admins': active_admins,
        'inactive_admins': inactive_admins,
        'new_admins_today': new_admins_today,
        'new_admins_week': new_admins_week,
        'new_admins_month': new_admins_month,
        'active_percentage': round((active_admins / total_admins * 100) if total_admins > 0 else 0, 1),
        'super_admin_percentage': round((super_admins / total_admins * 100) if total_admins > 0 else 0, 1),
    }
    
    context = {
        'title': _('إدارة المسؤولين'),
        'page_obj': page_obj,
        'search_query': search_query,
        'admin_type_filter': admin_type_filter,
        'status_filter': status_filter,
        'stats': stats,
        'admin_type_choices': AdminProfile.ADMIN_TYPE_CHOICES,
    }
    
    # إضافة معلومات المسؤول الحالي
    context.update(get_admin_context(request.user))
    
    return render(request, 'core/admin_list.html', context)


@super_admin_required
def admin_detail(request, pk):
    """عرض تفاصيل مسؤول معين"""
    admin_profile = get_object_or_404(AdminProfile, pk=pk)
    
    context = {
        'title': f'تفاصيل المسؤول - {admin_profile.get_display_name()}',
        'admin_profile': admin_profile,
    }
    
    context.update(get_admin_context(request.user))
    
    return render(request, 'core/admin_detail.html', context)


@super_admin_required
def admin_create(request):
    """إنشاء مسؤول جديد"""
    if request.method == 'POST':
        form = CreateAdminForm(request.POST, request.FILES)
        if form.is_valid():
            # إنشاء المستخدم
            user = User.objects.create_user(
                username=form.cleaned_data['username'],
                email=form.cleaned_data['email'],
                password=form.cleaned_data['password'],
                first_name=form.cleaned_data['first_name'],
                last_name=form.cleaned_data['last_name'],
                is_staff=True,
                is_active=True
            )
            
            # إنشاء ملف المسؤول
            admin_profile = AdminProfile.objects.create(
                user=user,
                admin_type=form.cleaned_data['admin_type'],
                full_name=form.cleaned_data['full_name'],
                phone=form.cleaned_data['phone'],
                department=form.cleaned_data['department'],
                profile_image=form.cleaned_data.get('profile_image'),
                notes=form.cleaned_data['notes'],
                is_active=True
            )
            
            messages.success(request, f'تم إنشاء حساب المسؤول "{user.username}" بنجاح')
            return redirect('core:admin_detail', pk=admin_profile.pk)
    else:
        form = CreateAdminForm()
    
    context = {
        'title': _('إنشاء مسؤول جديد'),
        'form': form,
        'submit_text': 'إنشاء الحساب',
    }
    
    context.update(get_admin_context(request.user))
    
    return render(request, 'core/admin_form.html', context)


@super_admin_required
def admin_edit(request, pk):
    """تعديل بيانات مسؤول"""
    admin_profile = get_object_or_404(AdminProfile, pk=pk)
    
    if request.method == 'POST':
        form = AdminProfileForm(request.POST, request.FILES, instance=admin_profile)
        if form.is_valid():
            # تحديث بيانات المستخدم
            user = admin_profile.user
            user.first_name = form.cleaned_data.get('first_name', user.first_name)
            user.last_name = form.cleaned_data.get('last_name', user.last_name)
            user.email = form.cleaned_data.get('email', user.email)
            user.save()
            
            # حفظ ملف المسؤول
            form.save()
            
            messages.success(request, f'تم تحديث بيانات المسؤول "{admin_profile.get_display_name()}" بنجاح')
            return redirect('core:admin_detail', pk=admin_profile.pk)
    else:
        # تحضير البيانات الأولية
        initial_data = {
            'first_name': admin_profile.user.first_name,
            'last_name': admin_profile.user.last_name,
            'email': admin_profile.user.email,
        }
        form = AdminProfileForm(instance=admin_profile, initial=initial_data)
    
    context = {
        'title': f'تعديل المسؤول - {admin_profile.get_display_name()}',
        'form': form,
        'admin_profile': admin_profile,
        'submit_text': 'حفظ التغييرات',
    }
    
    context.update(get_admin_context(request.user))
    
    return render(request, 'core/admin_form.html', context)


@super_admin_required
@require_http_methods(["POST"])
def admin_delete(request, pk):
    """حذف مسؤول"""
    admin_profile = get_object_or_404(AdminProfile, pk=pk)
    
    # منع حذف المسؤول الكبير الأساسي
    if admin_profile.user.username == 'Saad':
        return JsonResponse({
            'success': False,
            'message': 'لا يمكن حذف المسؤول الكبير الأساسي'
        })
    
    # منع المسؤول من حذف نفسه
    if admin_profile.user == request.user:
        return JsonResponse({
            'success': False,
            'message': 'لا يمكنك حذف حسابك الخاص'
        })
    
    try:
        username = admin_profile.user.username
        admin_profile.user.delete()  # سيحذف AdminProfile تلقائياً
        
        return JsonResponse({
            'success': True,
            'message': f'تم حذف المسؤول "{username}" بنجاح'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'حدث خطأ أثناء الحذف: {str(e)}'
        })


@super_admin_required
@require_http_methods(["POST"])
def admin_toggle_status(request, pk):
    """تفعيل/إلغاء تفعيل مسؤول"""
    admin_profile = get_object_or_404(AdminProfile, pk=pk)
    
    # منع إلغاء تفعيل المسؤول الكبير الأساسي
    if admin_profile.user.username == 'Saad':
        return JsonResponse({
            'success': False,
            'message': 'لا يمكن إلغاء تفعيل المسؤول الكبير الأساسي'
        })
    
    # منع المسؤول من إلغاء تفعيل نفسه
    if admin_profile.user == request.user:
        return JsonResponse({
            'success': False,
            'message': 'لا يمكنك إلغاء تفعيل حسابك الخاص'
        })
    
    try:
        admin_profile.is_active = not admin_profile.is_active
        admin_profile.user.is_active = admin_profile.is_active
        admin_profile.save()
        admin_profile.user.save()
        
        status_text = 'تم تفعيل' if admin_profile.is_active else 'تم إلغاء تفعيل'
        
        return JsonResponse({
            'success': True,
            'message': f'{status_text} المسؤول "{admin_profile.get_display_name()}" بنجاح',
            'new_status': admin_profile.is_active
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'حدث خطأ أثناء تغيير الحالة: {str(e)}'
        })


@super_admin_required
def export_admins_csv(request):
    """تصدير قائمة المسؤولين إلى CSV"""
    response = HttpResponse(content_type='text/csv; charset=utf-8')
    response['Content-Disposition'] = 'attachment; filename="admins_export.csv"'

    # إضافة BOM للدعم العربي
    response.write('\ufeff')

    writer = csv.writer(response)

    # كتابة العناوين
    writer.writerow([
        'اسم المستخدم',
        'الاسم الكامل',
        'البريد الإلكتروني',
        'رقم الهاتف',
        'القسم',
        'نوع المسؤول',
        'الحالة',
        'تاريخ الإنشاء',
        'آخر تسجيل دخول'
    ])

    # كتابة البيانات
    admins = AdminProfile.objects.select_related('user').all()
    for admin in admins:
        writer.writerow([
            admin.user.username,
            admin.get_display_name(),
            admin.user.email or 'غير محدد',
            admin.phone or 'غير محدد',
            admin.department or 'غير محدد',
            admin.get_admin_type_display(),
            'نشط' if admin.is_active else 'غير نشط',
            admin.created_at.strftime('%Y-%m-%d %H:%M'),
            admin.user.last_login.strftime('%Y-%m-%d %H:%M') if admin.user.last_login else 'لم يسجل دخول'
        ])

    return response


@super_admin_required
def admin_statistics(request):
    """إحصائيات تفصيلية للمسؤولين"""

    # إحصائيات عامة
    total_admins = AdminProfile.objects.count()
    active_admins = AdminProfile.objects.filter(is_active=True).count()

    # إحصائيات حسب النوع
    admin_types = AdminProfile.objects.values('admin_type').annotate(count=Count('admin_type'))

    # إحصائيات حسب التاريخ
    today = timezone.now().date()
    dates = []
    counts = []

    for i in range(30):  # آخر 30 يوم
        date = today - timedelta(days=i)
        count = AdminProfile.objects.filter(created_at__date=date).count()
        dates.append(date.strftime('%Y-%m-%d'))
        counts.append(count)

    dates.reverse()
    counts.reverse()

    context = {
        'title': 'إحصائيات المسؤولين',
        'total_admins': total_admins,
        'active_admins': active_admins,
        'admin_types': admin_types,
        'chart_dates': dates,
        'chart_counts': counts,
    }

    context.update(get_admin_context(request.user))

    return render(request, 'core/admin_statistics.html', context)
