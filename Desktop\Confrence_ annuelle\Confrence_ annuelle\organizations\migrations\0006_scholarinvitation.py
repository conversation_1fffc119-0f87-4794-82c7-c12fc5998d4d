# Generated by Django 5.1.2 on 2025-07-02 12:52

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('organizations', '0005_scholar_alter_organization_organization_type'),
    ]

    operations = [
        migrations.CreateModel(
            name='ScholarInvitation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('subject', models.CharField(max_length=255, verbose_name='الموضوع')),
                ('message', models.TextField(verbose_name='الرسالة')),
                ('sent_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإرسال')),
                ('is_sent', models.BooleanField(default=False, verbose_name='تم الإرسال')),
                ('status', models.CharField(choices=[('draft', 'مسودة'), ('sent', 'مرسلة'), ('cancelled', 'ملغية'), ('responded', 'تم الرد')], default='draft', max_length=20, verbose_name='الحالة')),
                ('response_status', models.CharField(blank=True, max_length=20, null=True, verbose_name='حالة الرد')),
                ('response_date', models.DateTimeField(blank=True, null=True, verbose_name='تاريخ الرد')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('scholar', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='invitations', to='organizations.scholar', verbose_name='العالم')),
            ],
            options={
                'verbose_name': 'دعوة عالم',
                'verbose_name_plural': 'دعوات العلماء',
                'ordering': ['-sent_at'],
            },
        ),
    ]
