# Generated by Django 5.1.2 on 2025-05-23 08:01

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Conference',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=255, verbose_name='Title')),
                ('description', models.TextField(verbose_name='Description')),
                ('start_date', models.DateField(verbose_name='Start Date')),
                ('end_date', models.DateField(verbose_name='End Date')),
                ('location', models.CharField(max_length=255, verbose_name='Location')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('banner_image', models.ImageField(blank=True, null=True, upload_to='conference/banners/', verbose_name='Banner Image')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
            ],
            options={
                'verbose_name': 'Conference',
                'verbose_name_plural': 'Conferences',
                'ordering': ['-start_date'],
            },
        ),
        migrations.CreateModel(
            name='ConferenceEdition',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year', models.PositiveIntegerField(verbose_name='Year')),
                ('theme', models.CharField(max_length=255, verbose_name='Theme')),
                ('description', models.TextField(verbose_name='Description')),
                ('start_date', models.DateField(verbose_name='Start Date')),
                ('end_date', models.DateField(verbose_name='End Date')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('banner_image', models.ImageField(blank=True, null=True, upload_to='conference/editions/', verbose_name='Banner Image')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('conference', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='editions', to='core.conference')),
            ],
            options={
                'verbose_name': 'Conference Edition',
                'verbose_name_plural': 'Conference Editions',
                'ordering': ['-year'],
                'unique_together': {('conference', 'year')},
            },
        ),
    ]
