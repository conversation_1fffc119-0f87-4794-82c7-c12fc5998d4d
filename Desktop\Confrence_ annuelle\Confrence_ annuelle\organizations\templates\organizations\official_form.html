{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .form-container {
        max-width: 800px;
        margin: 0 auto;
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        padding: 30px;
        margin-top: 20px;
    }
    
    .form-header {
        text-align: center;
        margin-bottom: 30px;
        padding-bottom: 20px;
        border-bottom: 2px solid #e9ecef;
    }
    
    .form-header h2 {
        color: #2c3e50;
        font-weight: bold;
        margin-bottom: 10px;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-label {
        font-weight: bold;
        color: #495057;
        margin-bottom: 8px;
        display: block;
    }
    
    .form-control, .form-select {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 12px 15px;
        font-size: 14px;
        transition: all 0.3s ease;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #007bff;
        box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
        outline: none;
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        padding: 12px 30px;
        border-radius: 8px;
        font-weight: bold;
        transition: all 0.3s ease;
    }
    
    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    }
    
    .btn-secondary {
        background: #6c757d;
        border: none;
        padding: 12px 30px;
        border-radius: 8px;
        font-weight: bold;
        margin-right: 10px;
    }
    
    .required-field {
        color: #dc3545;
    }
    
    .help-text {
        font-size: 12px;
        color: #6c757d;
        margin-top: 5px;
    }
    
    .alert {
        border-radius: 8px;
        margin-bottom: 20px;
    }
    
    .form-actions {
        text-align: center;
        margin-top: 30px;
        padding-top: 20px;
        border-top: 1px solid #e9ecef;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="form-container">
        <div class="form-header">
            <h2>{{ title }}</h2>
            {% if official %}
                <p class="text-muted">تعديل بيانات: {{ official.name }}</p>
            {% endif %}
        </div>

        {% if messages %}
            {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            {% endfor %}
        {% endif %}

        <form method="post" novalidate>
            {% csrf_token %}
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="{{ form.name.id_for_label }}" class="form-label">
                            {{ form.name.label }} <span class="required-field">*</span>
                        </label>
                        {{ form.name }}
                        {% if form.name.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.name.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="{{ form.title.id_for_label }}" class="form-label">
                            {{ form.title.label }}
                        </label>
                        {{ form.title }}
                        <div class="help-text">مثل: صاحب المعالي، حضرة الأستاذ الفاضل</div>
                        {% if form.title.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.title.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="{{ form.position.id_for_label }}" class="form-label">
                            {{ form.position.label }} <span class="required-field">*</span>
                        </label>
                        {{ form.position }}
                        {% if form.position.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.position.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="{{ form.category.id_for_label }}" class="form-label">
                            {{ form.category.label }} <span class="required-field">*</span>
                        </label>
                        {{ form.category }}
                        {% if form.category.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.category.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="{{ form.phone.id_for_label }}" class="form-label">
                            {{ form.phone.label }}
                        </label>
                        {{ form.phone }}
                        {% if form.phone.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.phone.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="{{ form.email.id_for_label }}" class="form-label">
                            {{ form.email.label }}
                        </label>
                        {{ form.email }}
                        {% if form.email.errors %}
                            <div class="text-danger small mt-1">
                                {% for error in form.email.errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>



            <div class="form-actions">
                <a href="{% url 'organizations:officials_list' %}" class="btn btn-secondary">
                    <i class="fas fa-times"></i> إلغاء
                </a>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i> {{ submit_text }}
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تحسين تجربة المستخدم
    document.addEventListener('DOMContentLoaded', function() {
        // التركيز على أول حقل
        const firstInput = document.querySelector('input[type="text"]');
        if (firstInput) {
            firstInput.focus();
        }
        
        // تأكيد قبل الإلغاء إذا كانت هناك تغييرات
        const form = document.querySelector('form');
        const cancelBtn = document.querySelector('.btn-secondary');
        let formChanged = false;
        
        form.addEventListener('input', function() {
            formChanged = true;
        });
        
        cancelBtn.addEventListener('click', function(e) {
            if (formChanged) {
                if (!confirm('هل أنت متأكد من الإلغاء؟ ستفقد جميع التغييرات غير المحفوظة.')) {
                    e.preventDefault();
                }
            }
        });
    });
</script>
{% endblock %}
