<svg xmlns="http://www.w3.org/2000/svg" width="200" height="200" viewBox="0 0 200 200">
  <!-- Background circle -->
  <circle cx="100" cy="100" r="100" fill="#667eea"/>
  
  <!-- Gradient overlay -->
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  <circle cx="100" cy="100" r="100" fill="url(#grad1)"/>
  
  <!-- User icon -->
  <g fill="white" opacity="0.8">
    <!-- Head -->
    <circle cx="100" cy="75" r="25"/>
    <!-- Body -->
    <path d="M100 110 C75 110, 55 130, 55 155 L55 180 L145 180 L145 155 C145 130, 125 110, 100 110 Z"/>
  </g>
  
  <!-- Decorative elements -->
  <circle cx="100" cy="100" r="90" fill="none" stroke="rgba(255,255,255,0.2)" stroke-width="2"/>
  <circle cx="100" cy="100" r="80" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="1"/>
</svg>
