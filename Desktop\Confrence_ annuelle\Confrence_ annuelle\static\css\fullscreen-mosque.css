/* تنسيقات خاصة بخلفية المسجد النبوي المتحركة */

/* تنسيق الخلفية الرئيسية */
.fullscreen-mosque-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background-image: url('/static/img/conference_cover_2023.png?v=123456789');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    z-index: -1;
    overflow: hidden;
}

/* تأثير الحركة البطيئة للخلفية */
.fullscreen-mosque-bg::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
    z-index: 1;
}

/* تأثير التموج الخفيف */
.mosque-wave {
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, transparent 75%, rgba(255, 255, 255, 0.05) 100%);
    animation: wave 15s infinite linear;
    z-index: 2;
}

@keyframes wave {
    0% { transform: translateY(0) scale(1.02); }
    50% { transform: translateY(-10px) scale(1); }
    100% { transform: translateY(0) scale(1.02); }
}

/* تأثير الضوء المتحرك */
.mosque-light {
    position: absolute;
    top: 0;
    left: -100%;
    width: 200%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: light-move 20s infinite linear;
    z-index: 3;
}

@keyframes light-move {
    0% { transform: translateX(-100%) rotate(25deg); }
    100% { transform: translateX(100%) rotate(25deg); }
}

/* تنسيق المحتوى فوق الخلفية */
.mosque-content {
    height: auto;
    min-height: 80vh;
    max-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    padding: 2rem 2rem 1rem;
    position: relative;
    z-index: 2;
    margin-bottom: 0;
}

.mosque-content h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    text-shadow: 2px 2px 8px rgba(0, 0, 0, 0.6);
    animation: fade-in-up 1.5s ease-out;
}

.mosque-content p {
    font-size: 1.8rem;
    max-width: 800px;
    margin-bottom: 2rem;
    text-shadow: 1px 1px 4px rgba(0, 0, 0, 0.6);
    animation: fade-in-up 1.5s ease-out 0.3s both;
}

.mosque-content .btn {
    font-size: 1.2rem;
    padding: 0.8rem 2rem;
    margin: 0.5rem;
    border-radius: 50px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    animation: fade-in-up 1.5s ease-out 0.6s both;
}

.mosque-content .btn:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.3);
}

.mosque-content .btn-primary {
    background: linear-gradient(45deg, #1a6e9d, #2ecc71);
    border: none;
}

.mosque-content .btn-outline-light {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* تأثيرات الظهور */
@keyframes fade-in-up {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تنسيق الشريط العلوي */
.navbar-mosque {
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(10px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.navbar-mosque .navbar-brand,
.navbar-mosque .nav-link {
    color: #fff !important;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.navbar-mosque .dropdown-menu {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.navbar-mosque .dropdown-item {
    color: #fff;
}

.navbar-mosque .dropdown-item:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* تنسيق الأقسام الأخرى */
.mosque-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 2rem;
    margin-top: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

/* تنسيقات متوافقة مع الجوال */
@media (max-width: 768px) {
    .mosque-content h1 {
        font-size: 2.5rem;
    }
    
    .mosque-content p {
        font-size: 1.4rem;
    }
}