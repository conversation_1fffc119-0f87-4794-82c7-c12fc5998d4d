{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة إدارة المؤتمر السنوي</title>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Noto Sans Arabic', sans-serif;
        }

        body {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            direction: rtl;
            min-height: 100vh;
            color: #333;
        }

        .header {
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3a8a 100%);
            color: white;
            padding: 1rem 0;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .header-content {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            font-size: 1.8rem;
            font-weight: 600;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header h1 i {
            margin-left: 10px;
        }

        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        .dashboard-welcome {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 2rem;
            border-radius: 15px;
            margin-bottom: 2rem;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .dashboard-welcome h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .dashboard-welcome p {
            font-size: 1.2rem;
            opacity: 0.9;
            margin: 0;
        }

        .dashboard-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .stat-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: none;
            position: relative;
            overflow: hidden;
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .stat-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1rem;
            font-size: 1.5rem;
            color: white;
        }

        .stat-icon.primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
        }

        .stat-icon.success {
            background: linear-gradient(135deg, #56ab2f, #a8e6cf);
        }

        .stat-icon.warning {
            background: linear-gradient(135deg, #f093fb, #f5576c);
        }

        .stat-icon.danger {
            background: linear-gradient(135deg, #4facfe, #00f2fe);
        }

        .stat-title {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .stat-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: #333;
            margin-bottom: 0.5rem;
        }

        .stat-description {
            font-size: 0.8rem;
            color: #999;
        }

        .dashboard-modules {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 2rem;
            margin-bottom: 2rem;
        }

        .dashboard-module {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .dashboard-module:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(0,0,0,0.15);
        }

        .dashboard-module h2 {
            background: linear-gradient(135deg, #2c5aa0, #3498db);
            color: white;
            padding: 1.5rem;
            margin: 0;
            font-size: 1.3rem;
            font-weight: 600;
            display: flex;
            align-items: center;
        }

        .dashboard-module h2 i {
            margin-left: 10px;
            font-size: 1.2rem;
        }

        .module-content {
            padding: 1.5rem;
        }

        .module-actions {
            display: grid;
            gap: 0.5rem;
        }

        .module-action {
            display: flex;
            align-items: center;
            padding: 1rem;
            background: #f8f9fa;
            border-radius: 10px;
            text-decoration: none;
            color: #333;
            transition: all 0.3s ease;
            border-right: 4px solid transparent;
        }

        .module-action:hover {
            background: #e9ecef;
            border-right-color: #2c5aa0;
            transform: translateX(-5px);
            color: #2c5aa0;
            text-decoration: none;
        }

        .module-action i {
            margin-left: 10px;
            width: 20px;
            text-align: center;
        }

        .action-count {
            margin-right: auto;
            background: #2c5aa0;
            color: white;
            padding: 0.2rem 0.6rem;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .quick-actions {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .quick-actions h3 {
            color: #333;
            margin-bottom: 1.5rem;
            font-size: 1.5rem;
            font-weight: 600;
            display: flex;
            align-items: center;
        }

        .quick-actions h3 i {
            margin-left: 10px;
            color: #f39c12;
        }

        .quick-action-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .quick-action-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
            background: linear-gradient(135deg, #2c5aa0, #3498db);
            color: white;
            text-decoration: none;
            border-radius: 10px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-align: center;
        }

        .quick-action-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(44, 90, 160, 0.4);
            color: white;
            text-decoration: none;
        }

        .quick-action-btn i {
            margin-left: 8px;
            font-size: 1.1rem;
        }

        @media (max-width: 768px) {
            .main-container {
                padding: 1rem;
            }

            .dashboard-stats {
                grid-template-columns: 1fr;
            }

            .dashboard-modules {
                grid-template-columns: 1fr;
            }

            .quick-action-buttons {
                grid-template-columns: 1fr;
            }

            .dashboard-welcome h1 {
                font-size: 2rem;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="header-content">
            <h1>
                <i class="fas fa-graduation-cap"></i>
                لوحة إدارة المؤتمر السنوي
            </h1>
            <div>
                <form method="post" action="/admin/logout/" style="display: inline;">
                    <input type="hidden" name="csrfmiddlewaretoken" value="{{ csrf_token }}">
                    <button type="submit" style="background: none; border: none; color: white; cursor: pointer; font-size: inherit; font-family: inherit;">
                        <i class="fas fa-sign-out-alt"></i> تسجيل الخروج
                    </button>
                </form>
            </div>
        </div>
    </div>

    <div class="main-container">
        <div class="dashboard-welcome">
            <h1>مرحباً بك في لوحة إدارة المؤتمر</h1>
            <p>إدارة شاملة لمؤتمر السيرة النبوية السنوي</p>
        </div>

        <div class="dashboard-stats">
            <div class="stat-card">
                <div class="stat-icon primary">
                    <i class="fas fa-building"></i>
                </div>
                <div class="stat-title">إجمالي المؤسسات</div>
                <div class="stat-value">{{ total_organizations|default:0 }}</div>
                <div class="stat-description">المؤسسات المسجلة في النظام</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon success">
                    <i class="fas fa-user-graduate"></i>
                </div>
                <div class="stat-title">إجمالي العلماء</div>
                <div class="stat-value">{{ total_scholars|default:0 }}</div>
                <div class="stat-description">العلماء المسجلون في النظام</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon warning">
                    <i class="fas fa-envelope"></i>
                </div>
                <div class="stat-title">الدعوات المرسلة</div>
                <div class="stat-value">{{ total_invitations|default:0 }}</div>
                <div class="stat-description">دعوات المؤسسات والعلماء</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon danger">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stat-title">المشاركات المؤكدة</div>
                <div class="stat-value">{{ confirmed_participants|default:0 }}</div>
                <div class="stat-description">المؤسسات والعلماء المؤكدون</div>
            </div>
        </div>

        <div class="dashboard-modules">
            <div class="dashboard-module">
                <h2>
                    <i class="fas fa-building"></i>
                    إدارة المؤسسات
                </h2>
                <div class="module-content">
                    <div class="module-actions">
                        <a href="/admin/organizations/organization/" class="module-action">
                            <i class="fas fa-building"></i>
                            المؤسسات
                            <span class="action-count">{{ total_organizations|default:0 }}</span>
                        </a>
                        <a href="/admin/organizations/invitation/" class="module-action">
                            <i class="fas fa-envelope"></i>
                            دعوات المؤسسات
                        </a>
                    </div>
                </div>
            </div>

            <div class="dashboard-module">
                <h2>
                    <i class="fas fa-user-graduate"></i>
                    إدارة العلماء
                </h2>
                <div class="module-content">
                    <div class="module-actions">
                        <a href="/admin/organizations/scholar/" class="module-action">
                            <i class="fas fa-user-graduate"></i>
                            العلماء
                            <span class="action-count">{{ total_scholars|default:0 }}</span>
                        </a>
                        <a href="/admin/organizations/scholarinvitation/" class="module-action">
                            <i class="fas fa-envelope"></i>
                            دعوات العلماء
                        </a>
                    </div>
                </div>
            </div>

            <div class="dashboard-module">
                <h2>
                    <i class="fas fa-users-cog"></i>
                    إدارة المستخدمين
                </h2>
                <div class="module-content">
                    <div class="module-actions">
                        <a href="/admin/auth/user/" class="module-action">
                            <i class="fas fa-user"></i>
                            المستخدمون
                        </a>
                        <a href="/admin/auth/group/" class="module-action">
                            <i class="fas fa-users"></i>
                            المجموعات
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <div class="quick-actions">
            <h3>
                <i class="fas fa-bolt"></i>
                الإجراءات السريعة
            </h3>
            <div class="quick-action-buttons">
                <a href="/admin/organizations/organization/add/" class="quick-action-btn">
                    <i class="fas fa-plus"></i>
                    إضافة مؤسسة جديدة
                </a>
                <a href="/admin/organizations/scholar/add/" class="quick-action-btn">
                    <i class="fas fa-user-plus"></i>
                    إضافة عالم جديد
                </a>
                <a href="{% url 'organizations:send_bulk_invitation' %}" class="quick-action-btn">
                    <i class="fas fa-paper-plane"></i>
                    إرسال دعوات جماعية
                </a>
                <a href="{% url 'organizations:scholar_export_excel' %}" class="quick-action-btn">
                    <i class="fas fa-file-excel"></i>
                    تصدير بيانات العلماء
                </a>
                <a href="{% url 'organizations:organization_list' %}" class="quick-action-btn">
                    <i class="fas fa-map"></i>
                    خريطة المؤسسات
                </a>
                <a href="/admin/auth/user/" class="quick-action-btn">
                    <i class="fas fa-users-cog"></i>
                    إدارة المستخدمين
                </a>
            </div>
        </div>
    </div>
</body>
</html>
