from django.contrib.auth.models import User
from .models import Notification

def create_notification(user, title, message, notification_type, related_object_id=None, related_object_type=None):
    """
    Create a notification for a user
    
    Args:
        user: The user to create the notification for
        title: The notification title
        message: The notification message
        notification_type: The type of notification (from Notification.NOTIFICATION_TYPES)
        related_object_id: Optional ID of the related object
        related_object_type: Optional type of the related object
        
    Returns:
        The created notification object
    """
    notification = Notification.objects.create(
        user=user,
        title=title,
        message=message,
        notification_type=notification_type,
        related_object_id=related_object_id,
        related_object_type=related_object_type
    )
    return notification


def create_invitation_notification(user, invitation, notification_type):
    """
    Create a notification for an invitation
    
    Args:
        user: The user to create the notification for
        invitation: The invitation object
        notification_type: The type of notification (invitation_sent, invitation_cancelled, etc.)
        
    Returns:
        The created notification object
    """
    # Set title and message based on notification type
    if notification_type == 'invitation_sent':
        title = f"دعوة مرسلة إلى {invitation.organization.name}"
        message = f"تم إرسال دعوة بنجاح إلى {invitation.organization.name} بعنوان '{invitation.subject}'"
    elif notification_type == 'invitation_cancelled':
        title = f"دعوة ملغاة لـ {invitation.organization.name}"
        message = f"تم إلغاء الدعوة المرسلة إلى {invitation.organization.name} بعنوان '{invitation.subject}'"
    elif notification_type == 'invitation_responded':
        title = f"رد على دعوة من {invitation.organization.name}"
        message = f"تم الرد على الدعوة المرسلة إلى {invitation.organization.name} بعنوان '{invitation.subject}'"
    else:
        title = f"إشعار متعلق بدعوة لـ {invitation.organization.name}"
        message = f"هناك تحديث على الدعوة المرسلة إلى {invitation.organization.name}"
    
    return create_notification(
        user=user,
        title=title,
        message=message,
        notification_type=notification_type,
        related_object_id=invitation.id,
        related_object_type='invitation'
    )