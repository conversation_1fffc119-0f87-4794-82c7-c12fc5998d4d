{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .form-container {
        background: white;
        border-radius: 20px;
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        padding: 30px;
        margin-top: 20px;
    }
    
    .form-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 15px;
        margin-bottom: 30px;
        text-align: center;
    }
    
    .form-header h2 {
        margin: 0;
        font-size: 1.8rem;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-label {
        font-weight: 600;
        color: #333;
        margin-bottom: 8px;
    }
    
    .form-control, .form-select {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 12px 15px;
        transition: all 0.3s ease;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .btn-submit {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-submit:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        color: white;
    }
    
    .btn-cancel {
        background: #6c757d;
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        color: white;
        font-weight: 600;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
    }
    
    .btn-cancel:hover {
        background: #5a6268;
        transform: translateY(-2px);
        color: white;
        text-decoration: none;
    }
    
    .required-field {
        color: #dc3545;
    }
    
    .form-text {
        color: #6c757d;
        font-size: 0.875rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="form-container">
                <div class="form-header">
                    <h2>{{ title }}</h2>
                </div>
                
                <form method="post" novalidate>
                    {% csrf_token %}
                    
                    <div class="row">
                        <!-- الاسم الكامل -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.name.id_for_label }}" class="form-label">
                                    {{ form.name.label }} <span class="required-field">*</span>
                                </label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.name.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <!-- اللقب/المنصب -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.title.id_for_label }}" class="form-label">
                                    {{ form.title.label }}
                                </label>
                                {{ form.title }}
                                <div class="form-text">مثال: الشيخ، الأمير، الحاج</div>
                                {% if form.title.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.title.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- الفئة -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.category.id_for_label }}" class="form-label">
                                    {{ form.category.label }} <span class="required-field">*</span>
                                </label>
                                {{ form.category }}
                                {% if form.category.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.category.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <!-- المنطقة -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.region.id_for_label }}" class="form-label">
                                    {{ form.region.label }} <span class="required-field">*</span>
                                </label>
                                {{ form.region }}
                                {% if form.region.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.region.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- القبيلة -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.tribe.id_for_label }}" class="form-label">
                                    {{ form.tribe.label }}
                                </label>
                                {{ form.tribe }}
                                {% if form.tribe.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.tribe.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <!-- سنة الميلاد -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.birth_year.id_for_label }}" class="form-label">
                                    {{ form.birth_year.label }}
                                </label>
                                {{ form.birth_year }}
                                <div class="form-text">لحساب العمر التقريبي</div>
                                {% if form.birth_year.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.birth_year.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- رقم الهاتف -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.phone.id_for_label }}" class="form-label">
                                    {{ form.phone.label }}
                                </label>
                                {{ form.phone }}
                                {% if form.phone.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.phone.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <!-- البريد الإلكتروني -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.email.id_for_label }}" class="form-label">
                                    {{ form.email.label }}
                                </label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.email.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <!-- المنصب الحالي -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.current_position.id_for_label }}" class="form-label">
                                    {{ form.current_position.label }}
                                </label>
                                {{ form.current_position }}
                                {% if form.current_position.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.current_position.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        
                        <!-- الحالة -->
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="{{ form.status.id_for_label }}" class="form-label">
                                    {{ form.status.label }}
                                </label>
                                {{ form.status }}
                                {% if form.status.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.status.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <!-- العنوان -->
                    <div class="form-group">
                        <label for="{{ form.address.id_for_label }}" class="form-label">
                            {{ form.address.label }}
                        </label>
                        {{ form.address }}
                        {% if form.address.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.address.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- التعليم -->
                    <div class="form-group">
                        <label for="{{ form.education.id_for_label }}" class="form-label">
                            {{ form.education.label }}
                        </label>
                        {{ form.education }}
                        {% if form.education.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.education.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- الإنجازات والمساهمات -->
                    <div class="form-group">
                        <label for="{{ form.achievements.id_for_label }}" class="form-label">
                            {{ form.achievements.label }}
                        </label>
                        {{ form.achievements }}
                        {% if form.achievements.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.achievements.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- التأثير الاجتماعي -->
                    <div class="form-group">
                        <label for="{{ form.social_influence.id_for_label }}" class="form-label">
                            {{ form.social_influence.label }}
                        </label>
                        {{ form.social_influence }}
                        {% if form.social_influence.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.social_influence.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- معلومات عائلية -->
                    <div class="form-group">
                        <label for="{{ form.family_info.id_for_label }}" class="form-label">
                            {{ form.family_info.label }}
                        </label>
                        {{ form.family_info }}
                        {% if form.family_info.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.family_info.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- ملاحظات -->
                    <div class="form-group">
                        <label for="{{ form.notes.id_for_label }}" class="form-label">
                            {{ form.notes.label }}
                        </label>
                        {{ form.notes }}
                        {% if form.notes.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.notes.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <!-- أزرار الإجراءات -->
                    <div class="text-center mt-4">
                        <button type="submit" class="btn btn-submit me-3">
                            <i class="fas fa-save me-2"></i>{{ submit_text }}
                        </button>
                        <a href="{% url 'organizations:notables_list' %}" class="btn btn-cancel">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                    </div>
                    
                    {% if form.non_field_errors %}
                        <div class="alert alert-danger mt-3">
                            {% for error in form.non_field_errors %}
                                <div>{{ error }}</div>
                            {% endfor %}
                        </div>
                    {% endif %}
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
