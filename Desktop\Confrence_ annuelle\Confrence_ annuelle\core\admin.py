from django.contrib import admin
from .models import Conference, ConferenceEdition, Notification

@admin.register(Conference)
class ConferenceAdmin(admin.ModelAdmin):
    list_display = ('title', 'is_active', 'created_at', 'updated_at')
    list_filter = ('is_active',)
    search_fields = ('title', 'description')
    date_hierarchy = 'created_at'

@admin.register(ConferenceEdition)
class ConferenceEditionAdmin(admin.ModelAdmin):
    list_display = ('conference', 'year', 'theme', 'start_date', 'end_date', 'is_active')
    list_filter = ('is_active', 'year')
    search_fields = ('theme', 'description')
    date_hierarchy = 'start_date'

@admin.register(Notification)
class NotificationAdmin(admin.ModelAdmin):
    list_display = ('title', 'user', 'notification_type', 'is_read', 'created_at')
    list_filter = ('notification_type', 'is_read', 'created_at')
    search_fields = ('title', 'message', 'user__username')
    date_hierarchy = 'created_at'
    readonly_fields = ('created_at',)
    list_select_related = ('user',)
