{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .party-leaders-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 2rem 0;
    }

    .page-header {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .page-title {
        color: #2c3e50;
        font-size: 2.5rem;
        font-weight: 700;
        text-align: center;
        margin-bottom: 1rem;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    }

    .page-subtitle {
        color: #7f8c8d;
        font-size: 1.2rem;
        text-align: center;
        margin-bottom: 0;
    }

    .stats-card {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        margin-bottom: 2rem;
    }

    .stats-number {
        font-size: 3rem;
        font-weight: 700;
        color: #3498db;
        margin-bottom: 0.5rem;
    }

    .stats-label {
        color: #7f8c8d;
        font-size: 1.1rem;
        font-weight: 500;
    }

    .leader-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .leader-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
    }

    .leader-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #3498db, #2ecc71, #f39c12, #e74c3c);
    }

    .leader-name {
        color: #2c3e50;
        font-size: 1.3rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        line-height: 1.4;
    }

    .leader-party {
        color: #3498db;
        font-size: 1.1rem;
        font-weight: 500;
        margin-bottom: 0.5rem;
        line-height: 1.3;
    }

    .leader-title {
        color: #7f8c8d;
        font-size: 1rem;
        font-weight: 400;
        margin-bottom: 0;
    }

    .leader-icon {
        position: absolute;
        top: 1rem;
        right: 1rem;
        color: #3498db;
        font-size: 1.5rem;
        opacity: 0.3;
    }

    .party-badge {
        display: inline-block;
        background: linear-gradient(45deg, #3498db, #2ecc71);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
        margin-top: 0.5rem;
    }

    .leader-number {
        position: absolute;
        top: -10px;
        left: -10px;
        background: linear-gradient(45deg, #e74c3c, #f39c12);
        color: white;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.8rem;
        font-weight: 600;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    }

    .search-container {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .search-input {
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 0.75rem 1rem;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: rgba(255, 255, 255, 0.9);
    }

    .search-input:focus {
        border-color: #3498db;
        box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        background: white;
    }

    .no-results {
        text-align: center;
        padding: 3rem;
        color: #7f8c8d;
        font-size: 1.2rem;
    }

    .back-btn {
        background: linear-gradient(45deg, #3498db, #2ecc71);
        border: none;
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 10px;
        font-weight: 500;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-block;
        margin-bottom: 1rem;
    }

    .back-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        color: white;
        text-decoration: none;
    }

    @media (max-width: 768px) {
        .page-title {
            font-size: 2rem;
        }
        
        .leader-card {
            padding: 1rem;
        }
        
        .leader-name {
            font-size: 1.1rem;
        }
        
        .leader-party {
            font-size: 1rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="party-leaders-container">
    <div class="container">
        <!-- العودة للخلف -->
        <a href="{% url 'organizations:admin_organizations_dashboard' %}" class="back-btn">
            <i class="fas fa-arrow-right me-2"></i>العودة لإدارة المؤسسات
        </a>

        <!-- رأس الصفحة -->
        <div class="page-header">
            <h1 class="page-title">
                <i class="fas fa-users-cog me-3"></i>
                {{ title }}
            </h1>
            <p class="page-subtitle">
                قائمة شاملة برؤساء الأحزاب السياسية في موريتانيا
            </p>
        </div>

        <!-- إحصائيات -->
        <div class="row mb-4">
            <div class="col-md-12">
                <div class="stats-card">
                    <div class="stats-number">{{ total_count }}</div>
                    <div class="stats-label">رئيس حزب</div>
                </div>
            </div>
        </div>

        <!-- البحث والفلترة -->
        <div class="search-container">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <input type="text" id="searchInput" class="form-control search-input"
                           placeholder="البحث في أسماء رؤساء الأحزاب أو أسماء الأحزاب...">
                </div>
                <div class="col-md-3 mb-3">
                    <select id="sortSelect" class="form-control search-input">
                        <option value="name">ترتيب حسب الاسم</option>
                        <option value="party">ترتيب حسب الحزب</option>
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="btn-group w-100" role="group">
                        <button id="clearBtn" class="btn btn-outline-secondary" style="height: 48px;">
                            <i class="fas fa-times"></i>
                        </button>
                        <button id="printBtn" class="btn btn-outline-primary" style="height: 48px;" title="طباعة القائمة">
                            <i class="fas fa-print"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- قائمة رؤساء الأحزاب -->
        <div class="row" id="leadersContainer">
            {% for leader in party_leaders %}
            <div class="col-lg-6 col-xl-4 leader-item"
                 data-name="{{ leader.name|lower }}"
                 data-party="{{ leader.party_name|lower }}">
                <div class="leader-card">
                    <div class="leader-number">{{ forloop.counter }}</div>
                    <div class="leader-icon">
                        <i class="fas fa-user-tie"></i>
                    </div>
                    <div class="leader-name">{{ leader.name }}</div>
                    <div class="leader-party">{{ leader.party_name }}</div>
                    <div class="leader-title">
                        {% if leader.title == 'president' %}
                            رئيس الحزب
                        {% elif leader.title == 'secretary_general' %}
                            الأمين العام
                        {% elif leader.title == 'deputy_president' %}
                            نائب الرئيس
                        {% else %}
                            {{ leader.get_title_display }}
                        {% endif %}
                    </div>
                    <div class="party-badge">
                        <i class="fas fa-flag me-1"></i>{{ leader.party_ideology|default:"حزب سياسي" }}
                    </div>
                    {% if leader.phone %}
                    <div class="mt-2">
                        <small class="text-muted">
                            <i class="fas fa-phone me-1"></i>{{ leader.phone }}
                        </small>
                    </div>
                    {% endif %}
                    {% if leader.email %}
                    <div class="mt-1">
                        <small class="text-muted">
                            <i class="fas fa-envelope me-1"></i>{{ leader.email }}
                        </small>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% empty %}
            <div class="col-12">
                <div class="no-results">
                    <i class="fas fa-users fa-3x mb-3"></i>
                    <p>لا توجد بيانات رؤساء أحزاب متاحة حالياً</p>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- رسالة عدم وجود نتائج -->
        <div id="noResults" class="no-results" style="display: none;">
            <i class="fas fa-search fa-3x mb-3"></i>
            <p>لم يتم العثور على نتائج مطابقة لبحثك</p>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('searchInput');
    const sortSelect = document.getElementById('sortSelect');
    const clearBtn = document.getElementById('clearBtn');
    const leadersContainer = document.getElementById('leadersContainer');
    const noResults = document.getElementById('noResults');
    const leaderItems = Array.from(document.querySelectorAll('.leader-item'));

    // وظيفة البحث
    function filterLeaders() {
        const searchTerm = searchInput.value.toLowerCase().trim();
        let visibleCount = 0;

        leaderItems.forEach(function(item) {
            const name = item.getAttribute('data-name');
            const party = item.getAttribute('data-party');

            if (name.includes(searchTerm) || party.includes(searchTerm)) {
                item.style.display = 'block';
                visibleCount++;
            } else {
                item.style.display = 'none';
            }
        });

        if (visibleCount === 0 && searchTerm !== '') {
            noResults.style.display = 'block';
            leadersContainer.style.display = 'none';
        } else {
            noResults.style.display = 'none';
            leadersContainer.style.display = 'flex';
        }
    }

    // وظيفة الترتيب
    function sortLeaders() {
        const sortBy = sortSelect.value;

        leaderItems.sort(function(a, b) {
            let aValue, bValue;

            if (sortBy === 'name') {
                aValue = a.getAttribute('data-name');
                bValue = b.getAttribute('data-name');
            } else if (sortBy === 'party') {
                aValue = a.getAttribute('data-party');
                bValue = b.getAttribute('data-party');
            }

            return aValue.localeCompare(bValue, 'ar');
        });

        // إعادة ترتيب العناصر في DOM
        leaderItems.forEach(function(item) {
            leadersContainer.appendChild(item);
        });
    }

    // وظيفة مسح البحث
    function clearSearch() {
        searchInput.value = '';
        sortSelect.value = 'name';
        filterLeaders();
        sortLeaders();
    }

    // وظيفة الطباعة
    function printList() {
        const printWindow = window.open('', '_blank');
        const printContent = `
            <!DOCTYPE html>
            <html dir="rtl" lang="ar">
            <head>
                <meta charset="UTF-8">
                <title>رؤساء الأحزاب الموريتانية</title>
                <style>
                    body { font-family: 'Tajawal', Arial, sans-serif; direction: rtl; margin: 20px; }
                    h1 { text-align: center; color: #2c3e50; margin-bottom: 30px; }
                    .leader-item { margin-bottom: 15px; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
                    .leader-name { font-weight: bold; font-size: 1.1em; color: #2c3e50; margin-bottom: 5px; }
                    .leader-party { color: #3498db; margin-bottom: 5px; }
                    .leader-title { color: #7f8c8d; font-size: 0.9em; }
                    .print-date { text-align: center; margin-top: 30px; color: #7f8c8d; font-size: 0.9em; }
                </style>
            </head>
            <body>
                <h1>رؤساء الأحزاب الموريتانية</h1>
        `;

        let content = printContent;
        leaderItems.forEach((item, index) => {
            if (item.style.display !== 'none') {
                const name = item.querySelector('.leader-name').textContent;
                const party = item.querySelector('.leader-party').textContent;
                const title = item.querySelector('.leader-title').textContent;

                content += `
                    <div class="leader-item">
                        <div class="leader-name">${index + 1}. ${name}</div>
                        <div class="leader-party">${party}</div>
                        <div class="leader-title">${title}</div>
                    </div>
                `;
            }
        });

        content += `
                <div class="print-date">تم طباعة هذه القائمة في: ${new Date().toLocaleDateString('ar-SA')}</div>
            </body>
            </html>
        `;

        printWindow.document.write(content);
        printWindow.document.close();
        printWindow.print();
    }

    // ربط الأحداث
    searchInput.addEventListener('input', filterLeaders);
    sortSelect.addEventListener('change', sortLeaders);
    clearBtn.addEventListener('click', clearSearch);
    document.getElementById('printBtn').addEventListener('click', printList);

    // ترتيب أولي
    sortLeaders();
});
</script>
{% endblock %}
