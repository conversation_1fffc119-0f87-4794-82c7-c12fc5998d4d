{% extends "admin/base.html" %}
{% load static %}

{% block title %}{{ title }} | لوحة إدارة المؤتمر{% endblock %}

{% block extrahead %}
{{ block.super }}
<link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
<style>
    :root {
        --primary-color: #2c5aa0;
        --secondary-color: #34495e;
        --success-color: #27ae60;
        --warning-color: #f39c12;
        --danger-color: #e74c3c;
        --info-color: #3498db;
    }

    * {
        font-family: 'Noto Sans Arabic', sans-serif !important;
    }

    body {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%) !important;
        direction: rtl !important;
    }

    #header {
        background: linear-gradient(135deg, var(--primary-color) 0%, #1e3a8a 100%) !important;
        color: white !important;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1) !important;
    }

    #branding h1 {
        color: white !important;
        font-weight: 600 !important;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3) !important;
    }

    #branding h1 a:link, #branding h1 a:visited {
        color: white !important;
    }

    .module {
        background: white !important;
        border-radius: 15px !important;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1) !important;
        border: none !important;
        margin-bottom: 20px !important;
        overflow: hidden !important;
    }

    .module h2 {
        background: linear-gradient(135deg, var(--primary-color), var(--info-color)) !important;
        color: white !important;
        font-weight: 600 !important;
        padding: 15px 20px !important;
        margin: 0 !important;
        border-radius: 0 !important;
    }

    .button, input[type=submit], input[type=button], .submit-row input, a.button {
        background: linear-gradient(135deg, var(--primary-color), var(--info-color)) !important;
        border: none !important;
        border-radius: 8px !important;
        padding: 10px 20px !important;
        color: white !important;
        font-weight: 500 !important;
        transition: all 0.3s ease !important;
        text-decoration: none !important;
    }

    .button:hover, input[type=submit]:hover, input[type=button]:hover, .submit-row input:hover, a.button:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 5px 15px rgba(44, 90, 160, 0.4) !important;
        color: white !important;
    }

    .default {
        background: linear-gradient(135deg, var(--success-color), #2ecc71) !important;
    }

    .deletelink {
        background: linear-gradient(135deg, var(--danger-color), #c0392b) !important;
    }

    .addlink {
        background: linear-gradient(135deg, var(--success-color), #2ecc71) !important;
    }

    #changelist-filter {
        background: white !important;
        border-radius: 15px !important;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1) !important;
        border: none !important;
    }

    #changelist-filter h2 {
        background: linear-gradient(135deg, var(--secondary-color), #2c3e50) !important;
        color: white !important;
        padding: 15px 20px !important;
        margin: 0 !important;
        font-weight: 600 !important;
    }

    .breadcrumbs {
        background: rgba(255,255,255,0.9) !important;
        border-radius: 10px !important;
        margin: 10px 0 !important;
        padding: 10px 20px !important;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1) !important;
    }

    .breadcrumbs a {
        color: var(--primary-color) !important;
        text-decoration: none !important;
        font-weight: 500 !important;
    }

    .breadcrumbs a:hover {
        color: var(--info-color) !important;
    }

    #content {
        padding: 20px !important;
    }

    .dashboard #content {
        width: auto !important;
    }

    .dashboard .module table th {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef) !important;
        color: var(--dark-color) !important;
        font-weight: 600 !important;
        padding: 15px !important;
    }

    .dashboard .module table td {
        padding: 12px 15px !important;
        border-bottom: 1px solid #eee !important;
    }

    .dashboard .module table tr:hover {
        background: rgba(44, 90, 160, 0.05) !important;
    }

    .object-tools {
        margin-bottom: 20px !important;
    }

    .object-tools a {
        background: linear-gradient(135deg, var(--success-color), #2ecc71) !important;
        border-radius: 8px !important;
        padding: 10px 20px !important;
        color: white !important;
        text-decoration: none !important;
        font-weight: 500 !important;
        transition: all 0.3s ease !important;
        margin-left: 10px !important;
    }

    .object-tools a:hover {
        transform: translateY(-2px) !important;
        box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4) !important;
    }

    /* Custom Dashboard Link */
    .custom-dashboard-link {
        position: fixed;
        top: 20px;
        left: 20px;
        background: linear-gradient(135deg, var(--success-color), #2ecc71);
        color: white;
        padding: 12px 20px;
        border-radius: 25px;
        text-decoration: none;
        font-weight: 600;
        box-shadow: 0 5px 15px rgba(39, 174, 96, 0.3);
        transition: all 0.3s ease;
        z-index: 1000;
    }

    .custom-dashboard-link:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(39, 174, 96, 0.4);
        color: white;
    }

    .custom-dashboard-link i {
        margin-left: 8px;
    }
</style>
{% endblock %}

{% block branding %}
<h1 id="site-name">
    <a href="{% url 'admin:index' %}">
        <i class="fas fa-graduation-cap" style="margin-left: 10px;"></i>
        لوحة إدارة المؤتمر السنوي
    </a>
</h1>
{% endblock %}

{% block nav-global %}
<a href="{% url 'organizations:custom_admin_dashboard' %}" class="custom-dashboard-link">
    <i class="fas fa-tachometer-alt"></i>
    لوحة التحكم المخصصة
</a>
{% endblock %}