{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}تأكيد حذف المؤسسة{% endblock %}

{% block extra_css %}
<style>
    .hero-mini {
        background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
        color: white;
        padding: 3rem 0;
        text-align: center;
        position: relative;
        overflow: hidden;
        margin-bottom: 2rem;
    }
    
    .hero-mini::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url('{% static "img/pattern.png" %}');
        opacity: 0.1;
    }
    
    .hero-mini .container {
        position: relative;
        z-index: 1;
    }
    
    .delete-card {
        border: none;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
        border-radius: 0.5rem;
        overflow: hidden;
    }
    
    .delete-card .card-header {
        background: linear-gradient(135deg, #d32f2f 0%, #f44336 100%);
        padding: 1.25rem 1.5rem;
    }
    
    .delete-card .card-body {
        padding: 2rem;
    }
    
    .organization-info {
        background-color: #f8f9fa;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
    
    .organization-name {
        font-size: 1.5rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 0.5rem;
    }
    
    .organization-meta {
        color: #6c757d;
        font-size: 0.9rem;
    }
    
    .warning-icon {
        font-size: 4rem;
        color: #f44336;
        margin-bottom: 1rem;
    }
    
    .btn-danger {
        background: linear-gradient(135deg, #d32f2f 0%, #f44336 100%);
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-danger:hover {
        background: linear-gradient(135deg, #b71c1c 0%, #e53935 100%);
        transform: translateY(-2px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }
    
    .btn-secondary {
        background: #f8f9fa;
        color: #333;
        border: 1px solid #dee2e6;
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-secondary:hover {
        background: #e9ecef;
        transform: translateY(-2px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="hero-mini text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-md-8 mx-auto text-center">
                <h1 class="display-5 fw-bold mb-3">تأكيد حذف المؤسسة</h1>
                <p class="lead">يرجى التأكد من رغبتك في حذف هذه المؤسسة</p>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card delete-card">
                <div class="card-header text-white">
                    <h4 class="mb-0"><i class="fas fa-exclamation-triangle me-2"></i>تأكيد الحذف</h4>
                </div>
                <div class="card-body text-center">
                    <div class="warning-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    
                    <h3 class="mb-4">هل أنت متأكد من رغبتك في حذف هذه المؤسسة؟</h3>
                    
                    <div class="organization-info mb-4">
                        <div class="organization-name">{{ organization.name }}</div>
                        {% if organization.email %}
                        <div class="organization-meta"><i class="fas fa-envelope me-2"></i>{{ organization.email }}</div>
                        {% endif %}
                        {% if organization.phone %}
                        <div class="organization-meta"><i class="fas fa-phone me-2"></i>{{ organization.phone }}</div>
                        {% endif %}
                    </div>
                    
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <strong>تحذير:</strong> هذا الإجراء لا يمكن التراجع عنه. سيتم حذف جميع البيانات المرتبطة بهذه المؤسسة بشكل نهائي.
                    </div>
                    
                    <form method="post" class="mt-4">
                        {% csrf_token %}
                        <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                            <a href="{% url 'organizations:organization_detail' organization.pk %}" class="btn btn-secondary me-md-2">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash me-2"></i>تأكيد الحذف
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}