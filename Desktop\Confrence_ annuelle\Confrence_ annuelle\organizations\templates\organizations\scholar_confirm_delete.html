{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .delete-warning {
        background: linear-gradient(135deg, #dc3545, #c82333);
        color: white;
        padding: 30px;
        border-radius: 10px;
        margin-bottom: 30px;
        text-align: center;
    }
    
    .scholar-info {
        background: white;
        border-radius: 10px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border-left: 4px solid #dc3545;
    }
    
    .scholar-photo {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid #dc3545;
    }
    
    .scholar-name {
        font-size: 1.3em;
        font-weight: bold;
        color: #333;
        margin-bottom: 5px;
    }
    
    .scholar-position {
        color: #666;
        margin-bottom: 10px;
    }
    
    .warning-text {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 8px;
        padding: 20px;
        margin: 20px 0;
        color: #856404;
    }
    
    .action-buttons {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 10px;
        text-align: center;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <!-- Warning Header -->
            <div class="delete-warning">
                <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
                <h1 class="h3 mb-0">{{ title }}</h1>
                <p class="mb-0 mt-2">هذا الإجراء لا يمكن التراجع عنه</p>
            </div>

            <!-- Scholar Information -->
            <div class="scholar-info">
                <h5 class="text-danger mb-3">
                    <i class="fas fa-user"></i> معلومات العالم المراد حذفه
                </h5>
                
                <div class="row align-items-center">
                    <div class="col-md-2 text-center">
                        {% if scholar.photo %}
                            <img src="{{ scholar.photo.url }}" alt="{{ scholar.name }}" class="scholar-photo">
                        {% else %}
                            <div class="scholar-photo d-flex align-items-center justify-content-center bg-light text-danger mx-auto">
                                <i class="fas fa-user fa-2x"></i>
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="col-md-10">
                        <div class="scholar-name">
                            {{ scholar.get_title_display }} {{ scholar.name }}
                        </div>
                        
                        {% if scholar.position %}
                            <div class="scholar-position">{{ scholar.position }}</div>
                        {% endif %}
                        
                        {% if scholar.organization %}
                            <div class="text-muted">
                                <i class="fas fa-building"></i> {{ scholar.organization }}
                            </div>
                        {% endif %}
                        
                        {% if scholar.country %}
                            <div class="text-muted">
                                <i class="fas fa-map-marker-alt"></i> {{ scholar.country }}
                                {% if scholar.city %}, {{ scholar.city }}{% endif %}
                            </div>
                        {% endif %}
                        
                        <div class="mt-2">
                            <span class="badge bg-info">{{ scholar.get_participation_status_display }}</span>
                            <span class="badge bg-secondary">أضيف في {{ scholar.created_at|date:"d/m/Y" }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Warning Message -->
            <div class="warning-text">
                <h6><i class="fas fa-exclamation-triangle"></i> تحذير مهم:</h6>
                <ul class="mb-0">
                    <li>سيتم حذف جميع بيانات العالم نهائياً</li>
                    <li>سيتم حذف الصورة الشخصية إن وجدت</li>
                    <li>لا يمكن استرداد هذه البيانات بعد الحذف</li>
                    <li>تأكد من أن هذا هو العالم الصحيح المراد حذفه</li>
                </ul>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons">
                <form method="post" class="d-inline">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger btn-lg me-3">
                        <i class="fas fa-trash"></i> نعم، احذف العالم نهائياً
                    </button>
                </form>
                
                <a href="{% url 'organizations:scholar_detail' scholar.pk %}" class="btn btn-secondary btn-lg me-3">
                    <i class="fas fa-arrow-right"></i> لا، العودة لصفحة العالم
                </a>
                
                <a href="{% url 'organizations:scholar_list' %}" class="btn btn-outline-secondary btn-lg">
                    <i class="fas fa-list"></i> العودة للقائمة
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}
