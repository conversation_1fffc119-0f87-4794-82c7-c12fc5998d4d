{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        color: white;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }
    
    .stats-card h3 {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .stats-card p {
        margin: 0;
        opacity: 0.9;
    }
    
    .notable-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        margin-bottom: 20px;
    }
    
    .notable-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.15);
    }
    
    .notable-header {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        padding: 15px 20px;
        border-radius: 15px 15px 0 0;
    }
    
    .notable-name {
        font-size: 1.2rem;
        font-weight: bold;
        margin: 0;
    }
    
    .notable-title {
        font-size: 0.9rem;
        opacity: 0.9;
        margin: 0;
    }
    
    .status-badge {
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
    }
    
    .status-active {
        background-color: #d4edda;
        color: #155724;
    }
    
    .status-inactive {
        background-color: #f8d7da;
        color: #721c24;
    }
    
    .status-deceased {
        background-color: #e2e3e5;
        color: #383d41;
    }
    
    .category-badge {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 8px 15px;
        border-radius: 20px;
        font-size: 0.85rem;
        display: inline-block;
    }
    
    .region-badge {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 0.75rem;
        font-weight: bold;
    }
    
    .tribe-badge {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        color: white;
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 0.75rem;
        font-weight: bold;
    }
    
    .age-badge {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        color: #333;
        padding: 5px 10px;
        border-radius: 15px;
        font-size: 0.75rem;
        font-weight: bold;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="text-center mb-4">
                <h1 class="display-4 mb-3">{{ title }}</h1>
                <p class="lead text-muted">إدارة وعرض بيانات أعيان موريتانيا</p>
                <div class="mt-4">
                    <a href="{% url 'organizations:add_notable' %}" class="btn btn-primary me-2">
                        <i class="fas fa-plus"></i> إضافة عين جديد
                    </a>
                    <a href="{% url 'organizations:export_notables' %}" class="btn btn-success me-2">
                        <i class="fas fa-file-excel"></i> تصدير Excel
                    </a>

                    <!-- WhatsApp Invitation Buttons -->
                    <div class="btn-group me-2" role="group">
                        <button type="button" class="btn btn-outline-success dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fab fa-whatsapp"></i> دعوات WhatsApp
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#whatsappInvitationModal">
                                <i class="fab fa-whatsapp text-success"></i> دعوة فردية
                            </a></li>
                            <li><a class="dropdown-item" href="#" id="send-bulk-whatsapp-invitations">
                                <i class="fab fa-whatsapp text-success"></i> دعوات جماعية
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" id="send-selected-whatsapp-invitations">
                                <i class="fas fa-check-circle text-success"></i> دعوة المحددين (<span id="selected-whatsapp-count">0</span>)
                            </a></li>
                        </ul>
                    </div>

                    <!-- Email Invitation Buttons -->
                    <div class="btn-group me-2" role="group">
                        <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-envelope"></i> دعوات البريد الإلكتروني
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#emailInvitationModal">
                                <i class="fas fa-envelope text-primary"></i> دعوة فردية
                            </a></li>
                            <li><a class="dropdown-item" href="#" id="send-bulk-email-invitations">
                                <i class="fas fa-envelope text-primary"></i> دعوات جماعية
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" id="send-selected-email-invitations">
                                <i class="fas fa-envelope text-primary"></i> دعوة المحددين (<span id="selected-email-count">0</span>)
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الإحصائيات -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="stats-card text-center">
                <h3>{{ stats.total }}</h3>
                <p>إجمالي الأعيان</p>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card text-center">
                <h3>{{ stats.active }}</h3>
                <p>نشط</p>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card text-center">
                <h3>{{ stats.inactive }}</h3>
                <p>غير نشط</p>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card text-center">
                <h3>{{ stats.deceased }}</h3>
                <p>متوفى</p>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card text-center">
                <h3>{{ stats.tribal_leaders }}</h3>
                <p>زعماء قبليون</p>
            </div>
        </div>
        <div class="col-md-2">
            <div class="stats-card text-center">
                <h3>{{ stats.religious_leaders }}</h3>
                <p>زعماء دينيون</p>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-2">
                    <label class="form-label">البحث</label>
                    <input type="text" name="search" class="form-control" value="{{ search_query }}" placeholder="البحث بالاسم أو القبيلة">
                </div>
                <div class="col-md-2">
                    <label class="form-label">الفئة</label>
                    <select name="category" class="form-select">
                        <option value="">جميع الفئات</option>
                        {% for value, label in category_choices %}
                            <option value="{{ value }}" {% if category_filter == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">المنطقة</label>
                    <select name="region" class="form-select">
                        <option value="">جميع المناطق</option>
                        {% for value, label in region_choices %}
                            <option value="{{ value }}" {% if region_filter == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">القبيلة</label>
                    <select name="tribe" class="form-select">
                        <option value="">جميع القبائل</option>
                        {% for tribe in tribes %}
                            <option value="{{ tribe }}" {% if tribe_filter == tribe %}selected{% endif %}>{{ tribe }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">الحالة</label>
                    <select name="status" class="form-select">
                        <option value="">جميع الحالات</option>
                        {% for value, label in status_choices %}
                            <option value="{{ value }}" {% if status_filter == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">بحث</button>
                    <a href="{% url 'organizations:notables_list' %}" class="btn btn-secondary">إعادة تعيين</a>
                </div>
            </form>
        </div>
    </div>

    <!-- قائمة الأعيان -->
    <div class="row">
        {% if notables %}
            {% for notable in notables %}
            <div class="col-lg-6 col-xl-4">
                <div class="card notable-card">
                    <div class="notable-header">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 me-2">
                                <input type="checkbox" class="form-check-input notable-checkbox" name="selected_notables" value="{{ notable.id }}" id="notable-{{ notable.id }}">
                            </div>
                            <div class="flex-grow-1">
                                <h5 class="notable-name mb-1">{{ notable.name }}</h5>
                                {% if notable.title %}
                                    <p class="notable-title mb-0">{{ notable.title }}</p>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="mb-2">
                            <span class="category-badge">{{ notable.get_category_display }}</span>
                        </div>
                        
                        <p class="mb-2">
                            <i class="fas fa-map-marker-alt text-muted me-2"></i>
                            <span class="region-badge">{{ notable.get_region_display }}</span>
                        </p>
                        
                        {% if notable.tribe %}
                        <p class="mb-2">
                            <i class="fas fa-users text-muted me-2"></i>
                            <span class="tribe-badge">{{ notable.tribe }}</span>
                        </p>
                        {% endif %}
                        
                        {% if notable.get_age %}
                        <p class="mb-2">
                            <i class="fas fa-birthday-cake text-muted me-2"></i>
                            <span class="age-badge">{{ notable.get_age }} سنة</span>
                        </p>
                        {% endif %}
                        
                        {% if notable.current_position %}
                        <p class="mb-2">
                            <i class="fas fa-briefcase text-muted me-2"></i>
                            <strong>المنصب الحالي:</strong> {{ notable.current_position }}
                        </p>
                        {% endif %}
                        
                        {% if notable.phone %}
                        <p class="mb-2">
                            <i class="fas fa-phone text-muted me-2"></i>
                            <strong>الهاتف:</strong> {{ notable.phone }}
                        </p>
                        {% endif %}
                        
                        {% if notable.achievements %}
                        <p class="mb-2">
                            <i class="fas fa-trophy text-muted me-2"></i>
                            <strong>الإنجازات:</strong> {{ notable.achievements|truncatewords:10 }}
                        </p>
                        {% endif %}
                        
                        <div class="mb-3">
                            <span class="status-badge status-{{ notable.status }}">
                                {{ notable.get_status_display }}
                            </span>
                        </div>
                        
                        <!-- Invitation Buttons -->
                        <div class="mb-3">
                            <div class="d-flex gap-2 flex-wrap">
                                {% if notable.phone %}
                                <button class="btn btn-sm btn-success send-whatsapp-btn"
                                        data-notable-id="{{ notable.id }}"
                                        data-notable-name="{{ notable.name }}"
                                        data-notable-phone="{{ notable.phone }}"
                                        data-bs-toggle="modal"
                                        data-bs-target="#whatsappInvitationModal">
                                    <i class="fab fa-whatsapp"></i> دعوة WhatsApp
                                </button>
                                {% endif %}

                                {% if notable.email %}
                                <button class="btn btn-sm btn-primary send-email-btn"
                                        data-notable-id="{{ notable.id }}"
                                        data-notable-name="{{ notable.name }}"
                                        data-notable-email="{{ notable.email }}"
                                        data-bs-toggle="modal"
                                        data-bs-target="#emailInvitationModal">
                                    <i class="fas fa-envelope"></i> دعوة بريد إلكتروني
                                </button>
                                {% endif %}
                            </div>
                        </div>

                        <div class="d-flex gap-2">
                            <a href="{% url 'organizations:edit_notable' notable.pk %}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-edit"></i> تعديل
                            </a>
                            <a href="{% url 'organizations:delete_notable' notable.pk %}" class="btn btn-sm btn-outline-danger">
                                <i class="fas fa-trash"></i> حذف
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-crown fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد بيانات للأعيان</h5>
                    <p class="text-muted">ابدأ بإضافة أعيان موريتانيا</p>
                    <a href="{% url 'organizations:add_notable' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة عين جديد
                    </a>
                </div>
            </div>
        {% endif %}
    </div>
</div>

<!-- WhatsApp Invitation Modal -->
<div class="modal fade" id="whatsappInvitationModal" tabindex="-1" aria-labelledby="whatsappInvitationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="whatsappInvitationModalLabel">
                    <i class="fab fa-whatsapp me-2"></i>إرسال دعوة WhatsApp
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'organizations:send_notable_whatsapp_invitation' %}">
                {% csrf_token %}
                <input type="hidden" id="whatsapp-notable-id" name="notable_id">
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fab fa-whatsapp me-2"></i>
                        سيتم إرسال دعوة WhatsApp إلى: <strong id="whatsapp-notable-name"></strong>
                        <br>رقم الهاتف: <strong id="whatsapp-notable-phone"></strong>
                    </div>

                    <div class="mb-3">
                        <label for="whatsapp-subject" class="form-label">موضوع الرسالة</label>
                        <input type="text" class="form-control" id="whatsapp-subject" name="subject"
                               value="دعوة للمشاركة في مؤتمر السيرة النبوية السنوي" required>
                    </div>

                    <div class="mb-3">
                        <label for="whatsapp-message" class="form-label">نص الرسالة</label>
                        <textarea class="form-control" id="whatsapp-message" name="message" rows="8" required>بسم الله الرحمن الرحيم

فضيلة العين المحترم

السلام عليكم ورحمة الله وبركاته

يسعدنا أن ندعوكم للمشاركة في مؤتمر السيرة النبوية السنوي الذي يهدف إلى نشر العلم والمعرفة حول سيرة النبي محمد صلى الله عليه وسلم.

نتطلع إلى مشاركتكم القيمة وإثراء المؤتمر بحضوركم الكريم ومكانتكم المرموقة.

وفقكم الله وبارك فيكم

مع أطيب التحيات
لجنة تنظيم المؤتمر</textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fab fa-whatsapp me-2"></i>إرسال الدعوة
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Email Invitation Modal -->
<div class="modal fade" id="emailInvitationModal" tabindex="-1" aria-labelledby="emailInvitationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="emailInvitationModalLabel">
                    <i class="fas fa-envelope me-2"></i>إرسال دعوة بريد إلكتروني
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'organizations:send_notable_email_invitation' %}">
                {% csrf_token %}
                <input type="hidden" id="email-notable-id" name="notable_id">
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-envelope me-2"></i>
                        سيتم إرسال دعوة بريد إلكتروني إلى: <strong id="email-notable-name"></strong>
                        <br>البريد الإلكتروني: <strong id="email-notable-email"></strong>
                    </div>

                    <div class="mb-3">
                        <label for="email-subject" class="form-label">موضوع الرسالة</label>
                        <input type="text" class="form-control" id="email-subject" name="subject"
                               value="دعوة للمشاركة في مؤتمر السيرة النبوية السنوي" required>
                    </div>

                    <div class="mb-3">
                        <label for="email-message" class="form-label">نص الرسالة</label>
                        <textarea class="form-control" id="email-message" name="message" rows="8" required>بسم الله الرحمن الرحيم

فضيلة العين المحترم

السلام عليكم ورحمة الله وبركاته

يسعدنا أن ندعوكم للمشاركة في مؤتمر السيرة النبوية السنوي الذي يهدف إلى نشر العلم والمعرفة حول سيرة النبي محمد صلى الله عليه وسلم.

نتطلع إلى مشاركتكم القيمة وإثراء المؤتمر بحضوركم الكريم ومكانتكم المرموقة.

وفقكم الله وبارك فيكم

مع أطيب التحيات
لجنة تنظيم المؤتمر</textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-envelope me-2"></i>إرسال الدعوة
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Checkbox functionality
    const notableCheckboxes = document.querySelectorAll('.notable-checkbox');
    const selectedCountSpan = document.getElementById('selected-whatsapp-count');
    const selectedEmailCountSpan = document.getElementById('selected-email-count');

    function updateSelectedCount() {
        const selectedCheckboxes = document.querySelectorAll('.notable-checkbox:checked');
        const count = selectedCheckboxes.length;

        if (selectedCountSpan) selectedCountSpan.textContent = count;
        if (selectedEmailCountSpan) selectedEmailCountSpan.textContent = count;
    }

    // Add event listeners to all checkboxes
    notableCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedCount);
    });

    // Add "Select All" functionality
    const selectAllBtn = document.createElement('button');
    selectAllBtn.className = 'btn btn-sm btn-outline-secondary me-2';
    selectAllBtn.innerHTML = '<i class="fas fa-check-square"></i> تحديد الكل';
    selectAllBtn.type = 'button';

    // Insert select all button before the invitation dropdown
    const invitationDropdown = document.querySelector('.btn-group');
    if (invitationDropdown && notableCheckboxes.length > 0) {
        invitationDropdown.parentNode.insertBefore(selectAllBtn, invitationDropdown);
    }

    let allSelected = false;
    selectAllBtn.addEventListener('click', function() {
        allSelected = !allSelected;

        notableCheckboxes.forEach(checkbox => {
            checkbox.checked = allSelected;
        });

        if (allSelected) {
            this.innerHTML = '<i class="fas fa-square"></i> إلغاء التحديد';
            this.classList.remove('btn-outline-secondary');
            this.classList.add('btn-secondary');
        } else {
            this.innerHTML = '<i class="fas fa-check-square"></i> تحديد الكل';
            this.classList.remove('btn-secondary');
            this.classList.add('btn-outline-secondary');
        }

        updateSelectedCount();
    });

    // Initial count update
    updateSelectedCount();

    // معالجة أزرار WhatsApp
    $('.send-whatsapp-btn').on('click', function() {
        const notableId = $(this).data('notable-id');
        const notableName = $(this).data('notable-name');
        const notablePhone = $(this).data('notable-phone');

        $('#whatsapp-notable-id').val(notableId);
        $('#whatsapp-notable-name').text(notableName);
        $('#whatsapp-notable-phone').text(notablePhone);
    });

    // معالجة أزرار البريد الإلكتروني
    $('.send-email-btn').on('click', function() {
        const notableId = $(this).data('notable-id');
        const notableName = $(this).data('notable-name');
        const notableEmail = $(this).data('notable-email');

        $('#email-notable-id').val(notableId);
        $('#email-notable-name').text(notableName);
        $('#email-notable-email').text(notableEmail);
    });

    // إرسال دعوات WhatsApp للمحددين
    $('#send-selected-whatsapp-invitations').on('click', function(e) {
        e.preventDefault();

        const selectedNotables = $('.notable-checkbox:checked');

        if (selectedNotables.length === 0) {
            alert('يرجى تحديد عين واحد على الأقل');
            return;
        }

        const notableIds = selectedNotables.map(function() {
            return this.value;
        }).get();

        // فتح نموذج الدعوات الجماعية (سيتم إضافته لاحقاً)
        alert('سيتم إضافة نموذج الدعوات الجماعية قريباً');
    });

    // إرسال دعوات WhatsApp جماعية
    $('#send-bulk-whatsapp-invitations').on('click', function(e) {
        e.preventDefault();
        alert('سيتم إضافة نموذج الدعوات الجماعية قريباً');
    });

    // إرسال دعوات بريد إلكتروني للمحددين
    $('#send-selected-email-invitations').on('click', function(e) {
        e.preventDefault();

        const selectedNotables = $('.notable-checkbox:checked');

        if (selectedNotables.length === 0) {
            alert('يرجى تحديد عين واحد على الأقل');
            return;
        }

        const notableIds = selectedNotables.map(function() {
            return this.value;
        }).get();

        // فتح نموذج الدعوات الجماعية (سيتم إضافته لاحقاً)
        alert('سيتم إضافة نموذج الدعوات الجماعية قريباً');
    });

    // إرسال دعوات بريد إلكتروني جماعية
    $('#send-bulk-email-invitations').on('click', function(e) {
        e.preventDefault();
        alert('سيتم إضافة نموذج الدعوات الجماعية قريباً');
    });
});
</script>
{% endblock %}
