{% extends 'base.html' %}
{% load static %}

{% block title %}أزرار المسؤولين الرسميين{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row">
        <div class="col-12">
            <h1 class="text-center mb-4" style="color: #dc3545; font-size: 48px; font-weight: bold;">
                🚨 أزرار إدارة المسؤولين الرسميين 🚨
            </h1>
            
            <div class="alert alert-danger text-center" style="font-size: 24px; font-weight: bold; margin: 30px 0;">
                هذه هي الأزرار التي يجب أن تظهر في صفحة المسؤولين الرسميين
            </div>

            <!-- الأزرار الكبيرة والواضحة -->
            <div style="background: #f8f9fa; padding: 50px; border-radius: 20px; border: 5px solid #dc3545; margin: 30px 0;">
                
                <!-- زر إضافة مسؤول -->
                <div class="mb-4">
                    <a href="{% url 'organizations:add_official' %}" 
                       class="btn btn-success btn-lg d-block mx-auto" 
                       style="font-size: 24px; padding: 20px 40px; max-width: 500px; border-radius: 15px;">
                        ➕ إضافة مسؤول جديد
                    </a>
                </div>

                <!-- زر تصدير -->
                <div class="mb-4">
                    <a href="{% url 'organizations:export_officials' %}" 
                       class="btn btn-primary btn-lg d-block mx-auto" 
                       style="font-size: 24px; padding: 20px 40px; max-width: 500px; border-radius: 15px;">
                        📊 تصدير إلى Excel
                    </a>
                </div>

                <!-- زر تحميل قالب -->
                <div class="mb-4">
                    <a href="{% url 'organizations:download_officials_template' %}" 
                       class="btn btn-info btn-lg d-block mx-auto" 
                       style="font-size: 24px; padding: 20px 40px; max-width: 500px; border-radius: 15px;">
                        📥 تحميل قالب Excel
                    </a>
                </div>

                <!-- زر استيراد -->
                <div class="mb-4">
                    <button type="button" 
                            onclick="document.getElementById('importFileInput').click()" 
                            class="btn btn-warning btn-lg d-block mx-auto" 
                            style="font-size: 24px; padding: 20px 40px; max-width: 500px; border-radius: 15px;">
                        📤 استيراد من Excel
                    </button>
                </div>

                <!-- مدخل ملف مخفي -->
                <input type="file" id="importFileInput" accept=".xlsx,.xls" style="display: none;">

                <!-- أزرار الحذف الجماعي -->
                <hr style="margin: 40px 0; border: 3px solid #dc3545;">
                
                <div class="mb-4">
                    <button type="button" 
                            class="btn btn-secondary btn-lg d-block mx-auto" 
                            style="font-size: 20px; padding: 15px 30px; max-width: 400px; border-radius: 15px;">
                        ☑️ تحديد الكل
                    </button>
                </div>

                <div class="mb-4">
                    <button type="button" 
                            class="btn btn-danger btn-lg d-block mx-auto" 
                            style="font-size: 20px; padding: 15px 30px; max-width: 400px; border-radius: 15px;">
                        🗑️ حذف المحدد
                    </button>
                </div>
            </div>

            <div class="text-center mt-5">
                <a href="{% url 'organizations:officials_list' %}" class="btn btn-outline-primary btn-lg">
                    العودة إلى صفحة المسؤولين الرسميين
                </a>
            </div>

            <div class="alert alert-info text-center mt-4">
                <h4>إذا كانت هذه الأزرار تعمل هنا ولا تظهر في الصفحة الأصلية:</h4>
                <ul class="list-unstyled">
                    <li>1. امسح التخزين المؤقت للمتصفح</li>
                    <li>2. اضغط Ctrl+F5 لإعادة تحميل قوية</li>
                    <li>3. جرب متصفح آخر</li>
                    <li>4. تأكد من أن JavaScript مفعل</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
