{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card glass-effect fade-in-element">
            <div class="card-header">
                <h3 class="neon-text text-center">{% trans "تم إعادة تعيين كلمة المرور" %}</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-success glass-effect animate__animated animate__fadeInDown">
                    <i class="fas fa-check-circle icon-float"></i> {% trans "تم تغيير كلمة المرور الخاصة بك بنجاح" %}
                </div>
                
                <p class="card-text">{% trans "تم إعادة تعيين كلمة المرور الخاصة بك. يمكنك الآن تسجيل الدخول باستخدام كلمة المرور الجديدة." %}</p>
                
                <div class="d-grid gap-2 mt-4">
                    <a href="{% url 'login' %}" class="btn btn-3d btn-primary">{% trans "تسجيل الدخول" %}</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}