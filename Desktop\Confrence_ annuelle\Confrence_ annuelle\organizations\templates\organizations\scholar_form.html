{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .form-section {
        background: white;
        border-radius: 10px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border-left: 4px solid #007bff;
    }
    
    .form-section h5 {
        color: #007bff;
        margin-bottom: 20px;
        font-weight: bold;
    }
    
    .form-header {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        padding: 30px;
        border-radius: 10px;
        margin-bottom: 30px;
        text-align: center;
    }
    
    .photo-preview {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        object-fit: cover;
        border: 4px solid #007bff;
        margin-bottom: 15px;
    }
    
    .photo-upload-area {
        border: 2px dashed #007bff;
        border-radius: 10px;
        padding: 20px;
        text-align: center;
        background: #f8f9fa;
        transition: all 0.3s;
    }
    
    .photo-upload-area:hover {
        background: #e9ecef;
        border-color: #0056b3;
    }
    
    .required-field {
        color: #dc3545;
    }
    
    .form-actions {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 10px;
        margin-top: 30px;
        text-align: center;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="form-header">
                <h1 class="h3 mb-0">
                    <i class="fas fa-graduation-cap"></i>
                    {{ title }}
                </h1>
                <p class="mb-0 mt-2">يرجى ملء جميع الحقول المطلوبة بدقة</p>
            </div>

            <form method="post" enctype="multipart/form-data">
                {% csrf_token %}
                
                <!-- Basic Information -->
                <div class="form-section">
                    <h5><i class="fas fa-user"></i> المعلومات الأساسية</h5>
                    
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">اللقب <span class="required-field">*</span></label>
                                <select class="form-select" name="title" required>
                                    <option value="">اختر اللقب</option>
                                    {% for value, label in title_choices %}
                                        <option value="{{ value }}" 
                                                {% if scholar.title == value %}selected{% endif %}>
                                            {{ label }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">الاسم <span class="required-field">*</span></label>
                                <input type="text" class="form-control" name="name" 
                                       value="{% if scholar %}{{ scholar.name }}{% endif %}" required>
                            </div>
                        </div>
                        
                        <div class="col-md-5">
                            <div class="mb-3">
                                <label class="form-label">الاسم الكامل</label>
                                <input type="text" class="form-control" name="full_name" 
                                       value="{% if scholar %}{{ scholar.full_name }}{% endif %}">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">المنصب/الوظيفة</label>
                                <input type="text" class="form-control" name="position" 
                                       value="{% if scholar %}{{ scholar.position }}{% endif %}">
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">التخصص</label>
                                <input type="text" class="form-control" name="specialization" 
                                       value="{% if scholar %}{{ scholar.specialization }}{% endif %}">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Professional Information -->
                <div class="form-section">
                    <h5><i class="fas fa-briefcase"></i> المعلومات المهنية</h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">المؤسسة/الجامعة</label>
                                <input type="text" class="form-control" name="organization" 
                                       value="{% if scholar %}{{ scholar.organization }}{% endif %}">
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">البلد</label>
                                <input type="text" class="form-control" name="country" 
                                       value="{% if scholar %}{{ scholar.country }}{% else %}موريتانيا{% endif %}">
                            </div>
                        </div>
                        
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label class="form-label">المدينة</label>
                                <input type="text" class="form-control" name="city" 
                                       value="{% if scholar %}{{ scholar.city }}{% endif %}">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">حالة المشاركة</label>
                                <select class="form-select" name="participation_status">
                                    {% for value, label in status_choices %}
                                        <option value="{{ value }}" 
                                                {% if scholar.participation_status == value %}selected{% endif %}>
                                            {{ label }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Contact Information -->
                <div class="form-section">
                    <h5><i class="fas fa-address-book"></i> معلومات الاتصال</h5>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" name="email" 
                                       value="{% if scholar %}{{ scholar.email }}{% endif %}">
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" name="phone" 
                                       value="{% if scholar %}{{ scholar.phone }}{% endif %}">
                            </div>
                        </div>
                        
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">الموقع الإلكتروني</label>
                                <input type="url" class="form-control" name="website" 
                                       value="{% if scholar %}{{ scholar.website }}{% endif %}">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Photo and Biography -->
                <div class="form-section">
                    <h5><i class="fas fa-image"></i> الصورة والسيرة الذاتية</h5>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">الصورة الشخصية</label>
                                {% if scholar and scholar.photo %}
                                    <div class="text-center mb-3">
                                        <img src="{{ scholar.photo.url }}" alt="{{ scholar.name }}" class="photo-preview">
                                        <div class="text-muted">الصورة الحالية</div>
                                    </div>
                                {% endif %}
                                <div class="photo-upload-area">
                                    <i class="fas fa-camera fa-2x text-primary mb-2"></i>
                                    <div class="mb-2">اختر صورة جديدة</div>
                                    <input type="file" class="form-control" name="photo" accept="image/*">
                                    <small class="text-muted">يفضل صورة مربعة بحجم لا يزيد عن 2MB</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label class="form-label">السيرة الذاتية</label>
                                <textarea class="form-control" name="biography" rows="8" 
                                          placeholder="نبذة مختصرة عن العالم وإنجازاته...">{% if scholar %}{{ scholar.biography }}{% endif %}</textarea>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary btn-lg me-3">
                        <i class="fas fa-save"></i> 
                        {% if scholar %}تحديث البيانات{% else %}إضافة العالم{% endif %}
                    </button>
                    <a href="{% if scholar %}{% url 'organizations:scholar_detail' scholar.pk %}{% else %}{% url 'organizations:scholar_list' %}{% endif %}" 
                       class="btn btn-secondary btn-lg">
                        <i class="fas fa-times"></i> إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Preview uploaded image
document.querySelector('input[name="photo"]').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        const reader = new FileReader();
        reader.onload = function(e) {
            // Create or update preview image
            let preview = document.querySelector('.photo-preview');
            if (!preview) {
                preview = document.createElement('img');
                preview.className = 'photo-preview';
                e.target.parentNode.insertBefore(preview, e.target);
            }
            preview.src = e.target.result;
        };
        reader.readAsDataURL(file);
    }
});
</script>
{% endblock %}
