/* Custom CSS for Conference Website */

/* General Styles */
body {
    font-family: '<PERSON><PERSON><PERSON>', 'Segoe UI', sans-serif;
    background-color: #f8f9fa;
    position: relative;
    overflow-x: hidden;
}

/* ضمان أن يكون الفوتر في الأسفل */
html, body {
    height: 100%;
}

body.d-flex {
    min-height: 100vh;
}

.flex-grow-1 {
    flex: 1 0 auto;
}

/* Add Arabic font */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

/* RTL Support for Arabic */
html[dir="rtl"], body[dir="rtl"] {
    text-align: right;
    direction: rtl;
}

/* إزالة القاعدة العامة التي تؤثر على الأيقونات */
[dir="rtl"] p, [dir="rtl"] h1, [dir="rtl"] h2, [dir="rtl"] h3, [dir="rtl"] h4, [dir="rtl"] h5, [dir="rtl"] h6,
[dir="rtl"] .text-content, [dir="rtl"] .navbar-nav, [dir="rtl"] .dropdown-menu {
    direction: rtl;
}

/* إعدادات خاصة للأيقونات في اللغة العربية */
[dir="rtl"] .icon-float {
    margin-right: 0;
    margin-left: 5px;
    direction: ltr !important;
    display: inline-block !important;
}

/* ضمان ظهور جميع الأيقونات بشكل صحيح */
[dir="rtl"] .fas, [dir="rtl"] .far, [dir="rtl"] .fab, [dir="rtl"] .fal {
    direction: ltr !important;
    display: inline-block !important;
    text-align: center !important;
}

/* إعدادات خاصة لأيقونات الأزرار */
[dir="rtl"] .btn .fas, [dir="rtl"] .btn .far, [dir="rtl"] .btn .fab {
    margin-left: 5px;
    margin-right: 0;
    direction: ltr !important;
    display: inline-block !important;
}

/* إعدادات إضافية لضمان ظهور الأيقونات */
.rtl-layout i, .rtl-layout .fas, .rtl-layout .far, .rtl-layout .fab {
    direction: ltr !important;
    display: inline-block !important;
    unicode-bidi: bidi-override !important;
}

/* إعدادات خاصة لأيقونات Font Awesome */
.fa, .fas, .far, .fab, .fal {
    font-family: "Font Awesome 5 Free", "Font Awesome 5 Brands" !important;
    font-weight: 900 !important;
    display: inline-block !important;
    direction: ltr !important;
    unicode-bidi: bidi-override !important;
}

/* إصلاح خاص لأيقونات الشبكات الاجتماعية */
[dir="rtl"] .social-icon i,
[dir="rtl"] .fab {
    direction: ltr !important;
    display: inline-block !important;
    font-family: "Font Awesome 5 Brands" !important;
    font-weight: 400 !important;
    unicode-bidi: bidi-override !important;
    text-align: center !important;
}

/* إصلاح عام لجميع الأيقونات في RTL */
html[dir="rtl"] i,
html[dir="rtl"] .fas,
html[dir="rtl"] .far,
html[dir="rtl"] .fab,
html[dir="rtl"] .fal {
    direction: ltr !important;
    display: inline-block !important;
    unicode-bidi: bidi-override !important;
}

/* إعدادات أساسية لFont Awesome */
@font-face {
    font-family: 'Font Awesome 5 Free';
    font-style: normal;
    font-weight: 900;
    font-display: block;
}

@font-face {
    font-family: 'Font Awesome 5 Brands';
    font-style: normal;
    font-weight: 400;
    font-display: block;
}

/* تأكيد عرض الأيقونات */
i[class^="fa-"]::before,
i[class*=" fa-"]::before {
    font-family: "Font Awesome 5 Free", "Font Awesome 5 Brands" !important;
    font-weight: 900;
    display: inline-block;
    font-style: normal;
    font-variant: normal;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
}

.fab::before {
    font-family: "Font Awesome 5 Brands" !important;
    font-weight: 400 !important;
}

/* حل طارئ لمشكلة اختفاء الأيقونات */
.rtl-layout i:before,
.rtl-layout .fas:before,
.rtl-layout .far:before,
.rtl-layout .fab:before,
.rtl-layout .fal:before {
    content: attr(data-icon) !important;
    font-family: "Font Awesome 5 Free", "Font Awesome 5 Brands", "FontAwesome" !important;
    font-weight: 900;
    display: inline-block !important;
    direction: ltr !important;
    unicode-bidi: bidi-override !important;
}

/* إعدادات خاصة لأيقونات العلامات التجارية */
.rtl-layout .fab:before {
    font-family: "Font Awesome 5 Brands", "FontAwesome" !important;
    font-weight: 400 !important;
}

/* إصلاح نهائي للأيقونات */
[dir="rtl"] .social-icon {
    display: inline-block !important;
    text-decoration: none !important;
}

[dir="rtl"] .social-icon i {
    visibility: visible !important;
    opacity: 1 !important;
    font-size: inherit !important;
    display: inline-block !important;
    width: auto !important;
    height: auto !important;
    line-height: 1 !important;
}

/* تحسين عرض أيقونات الفوتر */
.footer-modern .social-icon {
    display: inline-block !important;
    margin: 0 0.5rem;
    transition: all 0.3s ease;
}

.footer-modern .social-icon:hover {
    transform: translateY(-3px);
    color: #fff !important;
}

.footer-modern .social-icon i {
    font-size: 1.5rem !important;
    display: inline-block !important;
    direction: ltr !important;
}

[dir="rtl"] .footer-link {
    padding-left: 0;
    padding-right: 5px;
}

[dir="rtl"] .footer-link::before {
    content: '←';
    left: auto;
    right: -15px;
}

[dir="rtl"] .footer-link:hover {
    padding-left: 0;
    padding-right: 15px;
}

[dir="rtl"] .footer-link:hover::before {
    left: auto;
    right: 0;
}

/* تحسين دعم اللغة العربية للنصوص */
[dir="rtl"] p,
[dir="rtl"] h1,
[dir="rtl"] h2,
[dir="rtl"] h3,
[dir="rtl"] h4,
[dir="rtl"] h5,
[dir="rtl"] h6,
[dir="rtl"] li,
[dir="rtl"] span,
[dir="rtl"] div,
[dir="rtl"] ul,
[dir="rtl"] ol,
[dir="rtl"] table,
[dir="rtl"] .card,
[dir="rtl"] .card-body,
[dir="rtl"] .list-group,
[dir="rtl"] .list-group-item,
[dir="rtl"] .nav,
[dir="rtl"] .navbar-nav,
[dir="rtl"] .dropdown-menu {
    direction: rtl;
    text-align: right;
}

/* تحسين اتجاه النص في البطاقات */
[dir="rtl"] .card-title,
[dir="rtl"] .card-text {
    direction: rtl;
    text-align: right;
}

/* تصحيح اتجاه النص في الجداول */
[dir="rtl"] th,
[dir="rtl"] td {
    text-align: right;
}

/* تصحيح اتجاه النص في القوائم */
[dir="rtl"] .dropdown-item {
    text-align: right;
}

/* تحسين دعم RTL للصفحة بالكامل */
html.rtl-layout,
body.rtl-layout {
    direction: rtl;
    text-align: right;
}

.rtl-layout * {
    direction: rtl;
}

/* تصحيح اتجاه النص في عناصر المؤتمر */
[dir="rtl"] .conference-section .section-content,
[dir="rtl"] .conference-section .section-header,
[dir="rtl"] .objectives-list,
[dir="rtl"] .info-list,
[dir="rtl"] .timeline-item,
[dir="rtl"] .table-custom {
    direction: rtl;
    text-align: right;
}

/* Header Styles - تحسينات جديدة */
.navbar {
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    z-index: 1050;
    padding: 15px 0;
    background: linear-gradient(to right, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.98));
    position: relative;
}

.navbar-brand {
    font-weight: 700;
    display: flex;
    align-items: center;
}

.logo-animated img {
    transition: transform 0.5s ease;
}

.logo-animated:hover img {
    transform: rotate(10deg);
}

/* تحسين مظهر روابط القائمة */
.nav-link-hover {
    position: relative;
    transition: all 0.3s ease;
    overflow: hidden;
    font-weight: 500;
    margin: 0 5px;
    padding: 8px 15px !important;
    border-radius: 5px;
}

.nav-link-hover::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #3498db, #9b59b6);
    transition: all 0.3s ease;
    transform: translateX(50%);
}

.nav-link-hover:hover {
    color: #3498db !important;
    background-color: rgba(52, 152, 219, 0.05);
}

/* .nav-link-hover:hover::after {
    width: 80%;
} */

/* تحسين مظهر القائمة المنسدلة */
.dropdown-menu {
    border: none;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border-radius: 8px;
    padding: 10px;
    z-index: 1060;
    position: absolute;
}

/* تعديل خاص بقائمة المؤسسات المنسدلة */
#organizationsDropdown + .dropdown-menu {
    z-index: 10000;
    transform: translateY(0);
    pointer-events: auto;
}

/* تم تعليق هذه القاعدة للسماح لـ Bootstrap بالتحكم في سلوك القائمة المنسدلة */
/* .dropdown:hover > .dropdown-menu {
    display: block;
} */

.dropdown {
    position: relative;
}

/* تم تعليق هذه القاعدة أيضاً للسماح لـ Bootstrap بالتحكم في سلوك القائمة المنسدلة */
/* .dropdown-menu:hover {
    display: block;
} */
/* .dropdown-item {
    border-radius: 5px;
    padding: 8px 15px;
    transition: all 0.3s ease;
} */

.dropdown-item:hover {
    background-color: rgba(52, 152, 219, 0.1);
    color: #3498db;
}

/* تحسين مظهر الأزرار */
.btn-glow {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid rgba(52, 152, 219, 0.3);
    background: linear-gradient(to right, rgba(52, 152, 219, 0.05), rgba(155, 89, 182, 0.05));
}

.btn-glow:hover {
    box-shadow: 0 0 15px rgba(52, 152, 219, 0.5);
    border-color: rgba(52, 152, 219, 0.6);
    background: linear-gradient(to right, rgba(52, 152, 219, 0.1), rgba(155, 89, 182, 0.1));
}

.btn-3d {
    position: relative;
    transition: all 0.3s ease;
    transform-style: preserve-3d;
    transform: perspective(800px);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn-3d:hover {
    transform: perspective(800px) translateY(-2px);
    box-shadow: 0 7px 14px rgba(0, 0, 0, 0.15);
}

/* تحسين أيقونات الأزرار */
.icon-float {
    display: inline-block;
    transition: all 0.3s ease;
    margin-right: 5px;
}

.btn:hover .icon-float,
.nav-link:hover .icon-float {
    animation: float 1s ease infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-3px); }
}

.footer-link {
    transition: all 0.3s ease;
    position: relative;
    display: inline-block;
    padding-left: 5px;
}

.footer-link::before {
    content: '→';
    position: absolute;
    left: -15px;
    opacity: 0;
    transition: all 0.3s ease;
}

.footer-link:hover {
    padding-left: 15px;
    text-decoration: none;
}

.footer-link:hover::before {
    left: 0;
    opacity: 1;
}

.social-icon {
    transition: all 0.3s ease;
}

.social-icon:hover {
    transform: translateY(-5px);
    color: #3498db !important;
}

/* Hero Section */
.hero {
    background-size: cover;
    background-position: center;
    padding: 100px 0;
    margin-bottom: 30px;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.15);
    position: relative;
    overflow: hidden;
    border-radius: 15px;
}

.hero h1 {
    font-size: 2.5rem;
    font-weight: 700;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.hero p {
    font-size: 1.2rem;
    opacity: 0.9;
    text-shadow: 0 1px 5px rgba(0, 0, 0, 0.1);
}

/* Card Styles */
.card {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    height: 100%;
    position: relative;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.card-title {
    font-weight: 600;
    position: relative;
    z-index: 2;
}

.card-text {
    color: #6c757d;
    position: relative;
    z-index: 2;
}

.card-footer {
    background-color: rgba(0, 0, 0, 0.03);
    position: relative;
    z-index: 2;
}

.card-decoration {
    position: absolute;
    top: -50px;
    right: -50px;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(52, 152, 219, 0.3) 0%, rgba(52, 152, 219, 0) 70%);
    border-radius: 50%;
    z-index: 1;
    transition: all 0.5s ease;
}

.card:hover .card-decoration {
    transform: scale(2);
}

/* Organization List */
.organization-card {
    height: 100%;
    transition: all 0.3s ease;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.organization-card:hover {
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
    transform: translateY(-8px);
}

.organization-logo {
    height: 120px;
    object-fit: contain;
    margin: 1rem auto;
    padding: 10px;
    transition: transform 0.3s ease;
}

.organization-logo:hover {
    transform: scale(1.05);
}

.card-title {
    font-weight: 700;
    color: #0d6efd;
    font-size: 1.25rem;
    margin-bottom: 1rem;
    border-bottom: 2px solid rgba(13, 110, 253, 0.2);
    padding-bottom: 0.5rem;
}

.card-text {
    color: #555;
    font-size: 0.95rem;
    line-height: 1.5;
}

.card-footer {
    background-color: rgba(13, 110, 253, 0.05);
    border-top: 1px solid rgba(13, 110, 253, 0.1);
}

.btn-outline-primary {
    border-width: 2px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(13, 110, 253, 0.2);
}

.badge {
    font-size: 0.85rem;
    padding: 0.4em 0.6em;
    border-radius: 4px;
}

/* Organization filters */
.organization-filters {
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 1rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Forms */
.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.btn-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-primary:hover {
    background-color: #0a58ca;
    border-color: #0a58ca;
}

/* Footer */
footer {
    margin-top: auto;
    flex-shrink: 0;
}

footer a {
    text-decoration: none;
    transition: color 0.3s ease;
}

footer a:hover {
    color: #0d6efd !important;
}

/* تحسينات إضافية للفوتر */
.footer-modern {
    flex-shrink: 0;
    margin-top: auto !important;
}

/* Responsive Adjustments */
/* زخارف الخلفية */
.bg-decoration {
    position: fixed;
    border-radius: 50%;
    filter: blur(60px);
    opacity: 0.5;
    z-index: -1;
}

.bg-decoration-1 {
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(52, 152, 219, 0.4) 0%, rgba(52, 152, 219, 0) 70%);
    top: 10%;
    right: -100px;
    animation: float 15s ease-in-out infinite;
}

.bg-decoration-2 {
    width: 400px;
    height: 400px;
    background: radial-gradient(circle, rgba(155, 89, 182, 0.3) 0%, rgba(155, 89, 182, 0) 70%);
    bottom: 10%;
    left: -150px;
    animation: float 20s ease-in-out infinite reverse;
}

.bg-decoration-3 {
    width: 250px;
    height: 250px;
    background: radial-gradient(circle, rgba(46, 204, 113, 0.3) 0%, rgba(46, 204, 113, 0) 70%);
    top: 40%;
    left: 10%;
    animation: float 18s ease-in-out infinite 2s;
}

.bg-decoration-4 {
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(241, 196, 15, 0.3) 0%, rgba(241, 196, 15, 0) 70%);
    bottom: 30%;
    right: 10%;
    animation: float 12s ease-in-out infinite 1s;
}

.bg-decoration-5 {
    width: 350px;
    height: 350px;
    background: radial-gradient(circle, rgba(231, 76, 60, 0.2) 0%, rgba(231, 76, 60, 0) 70%);
    top: 60%;
    right: 30%;
    animation: float 25s ease-in-out infinite 3s;
}

@keyframes float {
    0% { transform: translate(0, 0); }
    50% { transform: translate(30px, -30px); }
    100% { transform: translate(0, 0); }
}

/* تأثير العناصر عند التحويم */
.list-item-hover {
    transition: all 0.3s ease;
    padding: 8px 15px;
    position: relative;
}

.list-item-hover:hover {
    background-color: rgba(52, 152, 219, 0.1);
    transform: translateX(5px);
}

.list-item-hover::after {
    content: '\f054'; /* رمز السهم من Font Awesome */
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    right: 10px;
    opacity: 0;
    transition: all 0.3s ease;
}

.list-item-hover:hover::after {
    opacity: 1;
    animation: bounce 1s infinite;
}

@keyframes bounce {
    0%, 100% { transform: translateX(0); }
    50% { transform: translateX(5px); }
}

/* تأثير نبض للأيقونات */
.pulse-element {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.4); }
    70% { box-shadow: 0 0 0 10px rgba(52, 152, 219, 0); }
    100% { box-shadow: 0 0 0 0 rgba(52, 152, 219, 0); }
}

@media (max-width: 768px) {
    .hero {
        padding: 60px 0;
    }

    .hero h1 {
        font-size: 2rem;
    }

    .hero p {
        font-size: 1rem;
    }
}

/* RTL specific adjustments */
.dropdown-menu-end {
    left: 0;
    right: auto;
    z-index: 1050;
}

/* Custom Utilities */
.text-primary-dark {
    color: #0a58ca;
}

.bg-light-gray {
    background-color: #f8f9fa;
}

.rounded-custom {
    border-radius: 1rem;
}

.shadow-sm {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.shadow-md {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
}

/* زخارف الخلفية */
.bg-decoration {
    position: absolute;
    border-radius: 50%;
    opacity: 0.5;
    filter: blur(40px);
    z-index: -1;
}

.bg-decoration-1 {
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(13, 110, 253, 0.3) 0%, rgba(13, 110, 253, 0) 70%);
    top: 10%;
    right: -100px;
    animation: float 15s ease-in-out infinite alternate;
}

.bg-decoration-2 {
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(46, 204, 113, 0.3) 0%, rgba(46, 204, 113, 0) 70%);
    bottom: 10%;
    left: -50px;
    animation: float 10s ease-in-out infinite alternate-reverse;
}

.bg-decoration-3 {
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, rgba(155, 89, 182, 0.3) 0%, rgba(155, 89, 182, 0) 70%);
    top: 50%;
    left: 10%;
    animation: float 12s ease-in-out infinite alternate;
}

.bg-decoration-4 {
    width: 250px;
    height: 250px;
    background: radial-gradient(circle, rgba(241, 196, 15, 0.3) 0%, rgba(241, 196, 15, 0) 70%);
    bottom: 30%;
    right: 5%;
    animation: float 18s ease-in-out infinite alternate-reverse;
}

.bg-decoration-5 {
    width: 180px;
    height: 180px;
    background: radial-gradient(circle, rgba(231, 76, 60, 0.3) 0%, rgba(231, 76, 60, 0) 70%);
    top: 70%;
    left: 40%;
    animation: float 20s ease-in-out infinite alternate;
}

/* تأثير الحركة للعناصر */
.list-item-hover {
    transition: all 0.3s ease;
    position: relative;
    padding-right: 30px;
}

.list-item-hover:hover {
    background-color: rgba(13, 110, 253, 0.05);
    transform: translateX(10px);
}

.list-item-hover::before {
    content: '\f054';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #0d6efd;
    opacity: 0;
    transition: all 0.3s ease;
}

.list-item-hover:hover::before {
    opacity: 1;
    right: 15px;
}

/* تأثير النبض للأيقونات */
.icon-container {
    display: inline-block;
    border-radius: 50%;
    padding: 15px;
    margin-bottom: 15px;
    transition: all 0.3s ease;
    background: rgba(13, 110, 253, 0.1);
}

.pulse-element {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(13, 110, 253, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(13, 110, 253, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(13, 110, 253, 0);
    }
}


/* Hero Mini Section */
.hero-mini {
    background: linear-gradient(135deg, #3498db, #9b59b6);
    padding: 40px 0;
    margin-bottom: 30px;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
    border-radius: 10px;
}

.hero-mini::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(
        from 0deg at 50% 50%,
        rgba(255, 255, 255, 0) 0%,
        rgba(255, 255, 255, 0.1) 10%,
        rgba(255, 255, 255, 0) 20%
    );
    animation: rotate 20s linear infinite;
    z-index: 1;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.hero-mini h1 {
    color: white;
    font-size: 2rem;
    font-weight: 700;
    position: relative;
    z-index: 2;
}

.hero-mini p {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.1rem;
    position: relative;
    z-index: 2;
}