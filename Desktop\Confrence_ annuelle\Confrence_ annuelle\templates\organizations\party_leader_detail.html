{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<!-- Font Awesome -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<style>
    .detail-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 20px;
        padding: 30px;
        color: white;
        box-shadow: 0 15px 35px rgba(102, 126, 234, 0.3);
        margin-bottom: 30px;
    }

    .profile-image {
        width: 200px;
        height: 200px;
        border-radius: 50%;
        object-fit: cover;
        border: 5px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    }

    .info-section {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .info-item {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 8px;
        border-left: 4px solid #667eea;
    }

    .info-icon {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        margin-left: 15px;
        font-size: 16px;
    }

    .info-content {
        flex: 1;
    }

    .info-label {
        font-weight: bold;
        color: #495057;
        font-size: 14px;
        margin-bottom: 5px;
    }

    .info-value {
        color: #212529;
        font-size: 16px;
    }

    .action-buttons {
        background: white;
        border-radius: 15px;
        padding: 20px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .btn-custom {
        border-radius: 10px;
        padding: 12px 25px;
        font-weight: 600;
        margin: 5px;
        transition: all 0.3s ease;
    }

    .btn-custom:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <!-- Header Card -->
            <div class="detail-card text-center">
                <div class="row align-items-center">
                    <div class="col-md-4">
                        {% if party_leader.photo %}
                            <img src="{{ party_leader.photo.url }}" alt="{{ party_leader.name }}" class="profile-image">
                        {% else %}
                            <div class="profile-image d-flex align-items-center justify-content-center" style="background: rgba(255,255,255,0.2);">
                                <i class="fas fa-user" style="font-size: 60px; color: rgba(255,255,255,0.7);"></i>
                            </div>
                        {% endif %}
                    </div>
                    <div class="col-md-8 text-start">
                        <h1 class="display-4 mb-3">{{ party_leader.name }}</h1>
                        <h3 class="mb-3">{{ party_leader.party_name }}</h3>
                        <p class="lead">{{ party_leader.ideology }}</p>
                    </div>
                </div>
            </div>

            <!-- Information Sections -->
            <div class="row">
                <div class="col-md-6">
                    <!-- Personal Information -->
                    <div class="info-section">
                        <h4 class="mb-4"><i class="fas fa-user text-primary"></i> المعلومات الشخصية</h4>
                        
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-user"></i>
                            </div>
                            <div class="info-content">
                                <div class="info-label">الاسم الكامل</div>
                                <div class="info-value">{{ party_leader.name }}</div>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-id-card"></i>
                            </div>
                            <div class="info-content">
                                <div class="info-label">رقم الهاتف</div>
                                <div class="info-value">{{ party_leader.phone|default:"غير محدد" }}</div>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-envelope"></i>
                            </div>
                            <div class="info-content">
                                <div class="info-label">البريد الإلكتروني</div>
                                <div class="info-value">{{ party_leader.email|default:"غير محدد" }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <!-- Party Information -->
                    <div class="info-section">
                        <h4 class="mb-4"><i class="fas fa-flag text-success"></i> معلومات الحزب</h4>
                        
                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-flag"></i>
                            </div>
                            <div class="info-content">
                                <div class="info-label">اسم الحزب</div>
                                <div class="info-value">{{ party_leader.party_name }}</div>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-lightbulb"></i>
                            </div>
                            <div class="info-content">
                                <div class="info-label">الأيديولوجية</div>
                                <div class="info-value">{{ party_leader.ideology|default:"غير محددة" }}</div>
                            </div>
                        </div>

                        <div class="info-item">
                            <div class="info-icon">
                                <i class="fas fa-calendar"></i>
                            </div>
                            <div class="info-content">
                                <div class="info-label">تاريخ التأسيس</div>
                                <div class="info-value">{{ party_leader.founding_date|default:"غير محدد" }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="action-buttons text-center">
                <a href="{% url 'organizations:edit_party_leader' party_leader.pk %}" class="btn btn-primary btn-custom">
                    <i class="fas fa-edit"></i> تعديل البيانات
                </a>
                <a href="{% url 'organizations:party_leaders_public' %}" class="btn btn-secondary btn-custom">
                    <i class="fas fa-arrow-right"></i> العودة للقائمة
                </a>
                <button type="button" class="btn btn-danger btn-custom" onclick="confirmDelete({{ party_leader.pk }}, '{{ party_leader.name }}')">
                    <i class="fas fa-trash"></i> حذف
                </button>
                <button onclick="window.print()" class="btn btn-info btn-custom">
                    <i class="fas fa-print"></i> طباعة
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من حذف <strong id="deleteItemName"></strong>؟</p>
                <p class="text-danger">هذا الإجراء لا يمكن التراجع عنه.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="post" style="display: inline;">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function confirmDelete(id, name) {
    document.getElementById('deleteItemName').textContent = name;
    document.getElementById('deleteForm').action = "{% url 'organizations:delete_party_leader' 0 %}".replace('0', id);
    new bootstrap.Modal(document.getElementById('deleteModal')).show();
}
</script>
{% endblock %}
