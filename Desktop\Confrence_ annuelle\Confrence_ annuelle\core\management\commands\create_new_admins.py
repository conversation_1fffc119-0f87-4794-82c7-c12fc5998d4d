from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from core.models import AdminProfile
from organizations.models import Scholar, Official<PERSON><PERSON>, <PERSON><PERSON><PERSON>er, Notable, DiplomaticCorps, FormerMinister


class Command(BaseCommand):
    help = 'إنشاء المسؤولين الجدد مع الأشخاص التابعين لهم'

    def handle(self, *args, **options):
        # بيانات المسؤولين والأشخاص التابعين لهم
        admins_data = {
            1: {
                'name': 'ابحيده ولد الشيخ يب',
                'followers': [
                    {'name': 'مريم منت الناجي', 'type': 'scholar'},
                    {'name': 'يوسف بن اليدالي', 'type': 'scholar'},
                    {'name': 'عيشة جوب', 'type': 'scholar'},
                    {'name': 'انجاي ابن أحمد', 'type': 'scholar'},
                ]
            },
            2: {
                'name': 'أبو العباس',
                'followers': [
                    {'name': 'محمد ولد احجور (القنصل الشرفي لجمهورية ليتوانيا)', 'type': 'diplomat', 'position': 'القنصل الشرفي لجمهورية ليتوانيا'},
                    {'name': 'الشيخ ولد انويگظ', 'type': 'scholar'},
                    {'name': 'سالكه حميمده', 'type': 'scholar'},
                    {'name': 'خديجة تاج الدين', 'type': 'scholar'},
                    {'name': 'سيدي محمد انجاي', 'type': 'scholar'},
                    {'name': 'سيدي حرمه', 'type': 'scholar'},
                    {'name': 'محمدُّ الشريف بويه', 'type': 'scholar'},
                    {'name': 'محمدو الشيخ احمياده', 'type': 'scholar'},
                    {'name': 'ابراهيم احجور', 'type': 'scholar'},
                    {'name': 'شماد الشيخ', 'type': 'scholar'},
                    {'name': 'احمد حماده', 'type': 'scholar'},
                    {'name': 'عبد الرحمن ادومو', 'type': 'scholar'},
                    {'name': 'عيسى بنيگين', 'type': 'scholar'},
                    {'name': 'يحي سيدي مولود', 'type': 'scholar'},
                    {'name': 'الشيخ ناجم', 'type': 'scholar'},
                    {'name': 'غيثي ولد عبد الحي', 'type': 'scholar'},
                    {'name': 'محمد الحسن يب', 'type': 'scholar'},
                    {'name': 'سيف المعلوم', 'type': 'scholar'},
                    {'name': 'تنجة بلال', 'type': 'scholar'},
                    {'name': 'الشيخ ولد احمياده', 'type': 'scholar'},
                    {'name': 'تغله بنت اعل بونه', 'type': 'scholar'},
                    {'name': 'قانتة محمد الحنفي', 'type': 'scholar'},
                    {'name': 'محمد المختار داهي', 'type': 'scholar'},
                    {'name': 'الدده محمد المختار داهي', 'type': 'scholar'},
                    {'name': 'حمدي احمد السالك', 'type': 'scholar'},
                    {'name': 'سيدي محمدن', 'type': 'scholar'},
                    {'name': 'ليلى الحسن', 'type': 'scholar'},
                    {'name': 'مامه بنت شريف', 'type': 'scholar'},
                    {'name': 'سناء الطالب', 'type': 'scholar'},
                    {'name': 'النية محمد', 'type': 'scholar'},
                    {'name': 'نهوى عتيه', 'type': 'scholar'},
                    {'name': 'ساجدة بنت حنفي', 'type': 'scholar'},
                    {'name': 'عثمان حنفي', 'type': 'scholar'},
                    {'name': 'نجاح محمود لفالي', 'type': 'scholar'},
                    {'name': 'فاطمة لفرك', 'type': 'scholar'},
                ]
            },
            3: {
                'name': 'أحمد انجاي جاكييتي',
                'followers': [
                    {'name': 'أحمد انجاي جاكييتي', 'type': 'scholar', 'phone': '48560333'},
                ]
            },
            4: {
                'name': 'أحمد سالم الخال',
                'followers': [
                    {'name': 'إبراهيم عبد الرحمن داداه', 'type': 'scholar'},
                    {'name': 'جداه بونعامه', 'type': 'scholar'},
                    {'name': 'د. أحمدو ولد محمد السالم', 'type': 'scholar', 'title': 'doctor'},
                ]
            },
            5: {
                'name': 'إسحاق يعقوب',
                'followers': [
                    {'name': 'إبراهيم اسلم', 'type': 'scholar', 'phone': '22227245'},
                    {'name': 'ابراهيما أحمد محمود صو', 'type': 'scholar', 'phone': '46127490'},
                    {'name': 'أحمد سيدي المختار', 'type': 'scholar', 'phone': '47633183'},
                    {'name': 'أحمد محمدن هميد', 'type': 'scholar', 'phone': '44524888'},
                    {'name': 'إسحاق يعقوب', 'type': 'scholar', 'phone': '41661239'},
                    {'name': 'الجيد محم', 'type': 'scholar', 'phone': '46499213'},
                    {'name': 'الحجي هارون سيس', 'type': 'scholar', 'phone': '41295035'},
                    {'name': 'الحسين جاه', 'type': 'scholar', 'phone': '46906715'},
                    {'name': 'الخليفه حم أكبير', 'type': 'scholar'},
                    {'name': 'السنيه منت عطاه الله', 'type': 'scholar'},
                    {'name': 'الشريف الحر', 'type': 'scholar'},
                    {'name': 'الشيخ التجاني سيدي', 'type': 'scholar', 'phone': '22035180'},
                    {'name': 'الشيخ جارو', 'type': 'scholar', 'phone': '46803436'},
                    {'name': 'الشيخ مودي', 'type': 'scholar', 'phone': '46407952'},
                    {'name': 'المختار جد ام', 'type': 'scholar', 'phone': '22668463'},
                    {'name': 'المصطفى الكوري', 'type': 'scholar', 'phone': '46495867'},
                    {'name': 'الناجي بن احبيب', 'type': 'scholar'},
                    {'name': 'أم كلثوم منت اخليفه (رئيسة المنصة العامة لنواكشوط الغربية)', 'type': 'official', 'position': 'رئيسة المنصة العامة لنواكشوط الغربية'},
                    {'name': 'امبارك اعمر', 'type': 'scholar', 'phone': '46439931'},
                    {'name': 'بال محمد البشير', 'type': 'scholar', 'phone': '46440094'},
                    {'name': 'جاجي باتلي', 'type': 'scholar', 'phone': '46706484'},
                    {'name': 'جيكى مال بتلي', 'type': 'scholar', 'phone': '46706484'},
                    {'name': 'حليمة منت أحمد طالب', 'type': 'scholar'},
                    {'name': 'سليمان جلو', 'type': 'scholar', 'phone': '22484226'},
                    {'name': 'سيدي سوخنه', 'type': 'scholar', 'phone': '22431691'},
                    {'name': 'سيدي كوريرا', 'type': 'scholar', 'phone': '31077791'},
                    {'name': 'صالح سخنه', 'type': 'scholar', 'phone': '46520848'},
                    {'name': 'طاهر عبد الله با', 'type': 'scholar', 'phone': '46460171'},
                    {'name': 'عالي مرزوك', 'type': 'scholar', 'phone': '46499652'},
                    {'name': 'عبد الله صار', 'type': 'scholar', 'phone': '22005136'},
                    {'name': 'علي المصطفى جنك', 'type': 'scholar', 'phone': '46442850'},
                    {'name': 'علين بوبكر', 'type': 'scholar', 'phone': '46992289'},
                    {'name': 'عمر بيدي سي', 'type': 'scholar', 'phone': '46538411'},
                    {'name': 'عمر سي', 'type': 'scholar', 'phone': '47607673'},
                    {'name': 'عمر سيس', 'type': 'scholar', 'phone': '22248446'},
                    {'name': 'عمر لوم', 'type': 'scholar', 'phone': '46492844'},
                    {'name': 'عيسى حيبلل', 'type': 'scholar', 'phone': '47672110'},
                    {'name': 'عيشة فرانسوا', 'type': 'scholar'},
                    {'name': 'فودي يوسف ماريكا', 'type': 'scholar', 'phone': '46418156'},
                    {'name': 'كواد ولد محمد بلخير', 'type': 'scholar'},
                    {'name': 'كوري تناي', 'type': 'scholar', 'phone': '46404560'},
                    {'name': 'ماجو ممد صار', 'type': 'scholar', 'phone': '46413553'},
                    {'name': 'محمد أحمد', 'type': 'scholar', 'phone': '48558359'},
                    {'name': 'محمد الأمين الوارد', 'type': 'scholar', 'phone': '22239526'},
                    {'name': 'محمد الأمين سالم', 'type': 'scholar'},
                    {'name': 'محمد الأمين محمد فال', 'type': 'scholar', 'phone': '22272724'},
                    {'name': 'محمد المصطفى جلو', 'type': 'scholar', 'phone': '36127490'},
                    {'name': 'محمد امهيدي', 'type': 'scholar', 'phone': '46586294'},
                    {'name': 'محمد سراج با', 'type': 'scholar', 'phone': '46449104'},
                    {'name': 'محمد صادو سيسى', 'type': 'scholar', 'phone': '36625057'},
                    {'name': 'محمد صبر انداي', 'type': 'scholar', 'phone': '44561092'},
                    {'name': 'محمد عبد الرحمن', 'type': 'scholar', 'phone': '47777545'},
                    {'name': 'محمد عبد الله لبيد', 'type': 'scholar', 'phone': '46153252'},
                    {'name': 'محمد كونو', 'type': 'scholar', 'phone': '34636928'},
                    {'name': 'محمد محمود المختار با', 'type': 'scholar', 'phone': '44048466'},
                    {'name': 'محمد محمود عبد الرحمن', 'type': 'scholar', 'phone': '46521427'},
                    {'name': 'محمد يحي القاضي', 'type': 'scholar', 'phone': '46272425'},
                    {'name': 'محمدن ديجه', 'type': 'scholar', 'phone': '32852072'},
                    {'name': 'موسى جارو', 'type': 'scholar', 'phone': '27118399'},
                    {'name': 'موسى جوكوركا', 'type': 'scholar', 'phone': '48580763'},
                    {'name': 'ورزك ولد أنمراي', 'type': 'scholar', 'phone': '46779118'},
                    {'name': 'يحي با', 'type': 'scholar', 'phone': '46591062'},
                    {'name': 'يحي عبد القادر', 'type': 'scholar', 'phone': '22415478'},
                ]
            },
            6: {
                'name': 'الإمام محمد يحي',
                'followers': [
                    {'name': 'الداه سيد محمد همر', 'type': 'scholar'},
                    {'name': 'الداه احمد محمود النفاع', 'type': 'scholar'},
                    {'name': 'عبد الرحمن الطالب سيد', 'type': 'scholar'},
                    {'name': 'بلاه ولد محمد ولد الطالب سيد', 'type': 'scholar'},
                    {'name': 'محمد ولد ألمين ولد الطالب سيد', 'type': 'scholar'},
                    {'name': 'الناجي لاديب', 'type': 'scholar'},
                    {'name': 'التراد لاديب', 'type': 'scholar'},
                    {'name': 'عبد القادر المنفعه', 'type': 'scholar'},
                    {'name': 'محمد الأمين ولد المنير', 'type': 'scholar'},
                    {'name': 'المفوض شيخن ولد سيد', 'type': 'scholar'},
                    {'name': 'العلمي ولد مولاي اعلي', 'type': 'scholar'},
                    {'name': 'أحمد محمود النفاع', 'type': 'scholar'},
                    {'name': 'احمد ولد احمدة', 'type': 'scholar'},
                    {'name': 'إسلم اعل طالب', 'type': 'scholar'},
                    {'name': 'تميم النفاع', 'type': 'scholar'},
                    {'name': 'الحسين سيدي عبد الله', 'type': 'scholar'},
                    {'name': 'الداه النفاع', 'type': 'scholar'},
                    {'name': 'الدمين ولد سيدي عبد الله', 'type': 'scholar'},
                    {'name': 'سيدي محمد ولد همر', 'type': 'scholar'},
                    {'name': 'الشيخ النفاع', 'type': 'scholar'},
                    {'name': 'عبد الرحمن سيدي عبد الله', 'type': 'scholar'},
                    {'name': 'عبد الرحمن ولد الشيخ سيديا', 'type': 'scholar'},
                    {'name': 'عبد الرحيم المنفعة', 'type': 'scholar'},
                    {'name': 'كابر ولد أحمد جدو', 'type': 'scholar'},
                    {'name': 'محمد الامين النفاع', 'type': 'scholar'},
                    {'name': 'محمد الامين دحمان النفاع', 'type': 'scholar'},
                    {'name': 'محمد السالم سيدي أحمد', 'type': 'scholar'},
                    {'name': 'محمد المصطفى أحمد سيدي عبد الله', 'type': 'scholar'},
                    {'name': 'محمد المصطفى الامام وداد', 'type': 'scholar'},
                    {'name': 'محمد المصطفى سيدي عبد الدائم', 'type': 'scholar'},
                    {'name': 'محمد المصطفي الاديب', 'type': 'scholar'},
                    {'name': 'محمد المصطفي النفاع', 'type': 'scholar'},
                    {'name': 'محمد المصطفي سيدي عبد الله', 'type': 'scholar'},
                    {'name': 'محمد الناجي المنفعه', 'type': 'scholar'},
                    {'name': 'محمد ولد الطالب سيدي', 'type': 'scholar'},
                    {'name': 'محي الدين ولد الامام مالك', 'type': 'scholar'},
                    {'name': 'يحي المنفعة', 'type': 'scholar'},
                    {'name': 'يحي النفاع', 'type': 'scholar'},
                    {'name': 'يحي سيدي عبد الله', 'type': 'scholar'},
                    {'name': 'يحي ولد احمد القاظي', 'type': 'scholar'},
                    {'name': 'يسلم الطالب سيدي', 'type': 'scholar'},
                    {'name': 'يسلم ولد اعل ولد أحمد', 'type': 'scholar'},
                    {'name': 'محمد مر ولد الاديب', 'type': 'scholar'},
                    {'name': 'محمد احمد ولد احمد القاظي', 'type': 'scholar'},
                    {'name': 'انكايدة الحسن', 'type': 'scholar'},
                ]
            },
        }

        created_count = 0
        
        for admin_id, admin_data in admins_data.items():
            # إنشاء المسؤول
            username = 'admin'
            password = f'admin{admin_id}'
            email = f'admin{admin_id}@conference.com'
            
            # التحقق من وجود المستخدم
            user, created = User.objects.get_or_create(
                username=f'admin{admin_id}',
                defaults={
                    'email': email,
                    'is_staff': True,
                    'is_superuser': False,
                    'is_active': True,
                    'first_name': admin_data['name'].split()[0],
                    'last_name': ' '.join(admin_data['name'].split()[1:]) if len(admin_data['name'].split()) > 1 else ''
                }
            )
            
            # تعيين كلمة المرور
            user.set_password(password)
            user.save()
            
            # إنشاء أو تحديث ملف المسؤول
            admin_profile, profile_created = AdminProfile.objects.get_or_create(
                user=user,
                defaults={
                    'admin_type': 'regular_admin',
                    'full_name': admin_data['name'],
                    'department': f'قسم {admin_data["name"]}',
                    'notes': f'مسؤول عادي - {admin_data["name"]}',
                    'is_active': True
                }
            )
            
            if created:
                created_count += 1
                self.stdout.write(f'تم إنشاء المسؤول: {admin_data["name"]} (admin{admin_id})')
            else:
                self.stdout.write(f'المسؤول موجود مسبقاً: {admin_data["name"]} (admin{admin_id})')
            
            # إنشاء الأشخاص التابعين
            followers_created = 0
            for follower in admin_data['followers']:
                try:
                    if follower['type'] == 'scholar':
                        scholar, scholar_created = Scholar.objects.get_or_create(
                            name=follower['name'],
                            defaults={
                                'title': follower.get('title', 'mister'),
                                'position': follower.get('position', ''),
                                'phone': follower.get('phone', ''),
                                'country': 'موريتانيا',
                                'created_by': user
                            }
                        )
                        if scholar_created:
                            followers_created += 1
                    
                    elif follower['type'] == 'official':
                        official, official_created = OfficialPerson.objects.get_or_create(
                            name=follower['name'],
                            defaults={
                                'title': 'حضرة الأستاذ الفاضل',
                                'position': follower.get('position', ''),
                                'phone': follower.get('phone', ''),
                                'category': 'مجلس الشباب الوطني',
                                'created_by': user
                            }
                        )
                        if official_created:
                            followers_created += 1
                    
                    elif follower['type'] == 'diplomat':
                        diplomat, diplomat_created = DiplomaticCorps.objects.get_or_create(
                            name=follower['name'],
                            defaults={
                                'current_position': follower.get('position', ''),
                                'phone': follower.get('phone', ''),
                                'country': 'ليتوانيا',
                                'rank': 'consul',
                                'mission_type': 'honorary_consul',
                                'created_by': user
                            }
                        )
                        if diplomat_created:
                            followers_created += 1
                            
                except Exception as e:
                    self.stdout.write(f'خطأ في إنشاء {follower["name"]}: {str(e)}')
            
            self.stdout.write(f'تم إنشاء {followers_created} شخص تابع للمسؤول {admin_data["name"]}')
        
        self.stdout.write(
            self.style.SUCCESS(f'تم إنشاء {created_count} مسؤول جديد بنجاح')
        )
