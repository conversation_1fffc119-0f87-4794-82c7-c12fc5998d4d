<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الأزرار</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .container {
            padding: 2rem 0;
        }
        .test-section {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }
        .btn {
            margin: 0.5rem;
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-section">
            <h1 class="text-center mb-4">
                <i class="fas fa-cogs me-3"></i>
                اختبار أزرار المسؤولين الرسميين
            </h1>
            
            <div class="row">
                <div class="col-md-6">
                    <h3>الإحصائيات:</h3>
                    <ul class="list-group">
                        <li class="list-group-item">
                            <i class="fas fa-users me-2"></i>
                            إجمالي المسؤولين: {{ total_count|default:"0" }}
                        </li>
                        <li class="list-group-item">
                            <i class="fas fa-layer-group me-2"></i>
                            الفئات: {{ categories_count|default:"0" }}
                        </li>
                    </ul>
                </div>
                
                <div class="col-md-6">
                    <h3>الأزرار:</h3>
                    <div class="d-grid gap-2">
                        <a href="{% url 'organizations:add_official' %}" class="btn btn-success">
                            <i class="fas fa-plus me-2"></i>إضافة مسؤول جديد
                        </a>
                        
                        <a href="{% url 'organizations:export_officials' %}" class="btn btn-primary">
                            <i class="fas fa-file-excel me-2"></i>تصدير إلى Excel
                        </a>
                        
                        <a href="{% url 'organizations:download_officials_template' %}" class="btn btn-info">
                            <i class="fas fa-download me-2"></i>تحميل قالب الاستيراد
                        </a>
                        
                        <button type="button" class="btn btn-warning" data-bs-toggle="modal" data-bs-target="#importModal">
                            <i class="fas fa-upload me-2"></i>استيراد من Excel
                        </button>
                        
                        <button type="button" class="btn btn-danger" onclick="alert('وظيفة الحذف الجماعي')">
                            <i class="fas fa-trash me-2"></i>حذف جماعي
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>اختبار الروابط:</h3>
            <div class="alert alert-info">
                <h5>الروابط المطلوبة:</h5>
                <ul>
                    <li><code>organizations:add_official</code> - إضافة مسؤول</li>
                    <li><code>organizations:export_officials</code> - تصدير</li>
                    <li><code>organizations:download_officials_template</code> - تحميل قالب</li>
                    <li><code>organizations:import_officials</code> - استيراد</li>
                    <li><code>organizations:bulk_delete_officials</code> - حذف جماعي</li>
                </ul>
            </div>
        </div>
        
        {% if categories_with_officials %}
        <div class="test-section">
            <h3>عينة من البيانات:</h3>
            {% for officials_data, category_name in categories_with_officials|slice:":2" %}
                {% with officials=officials_data.0 officials_count=officials_data.1 %}
                <div class="card mb-3">
                    <div class="card-header">
                        <h5>{{ category_name }} ({{ officials_count }})</h5>
                    </div>
                    <div class="card-body">
                        {% for official in officials|slice:":3" %}
                        <div class="border p-2 mb-2 rounded">
                            <strong>{{ official.get_full_title }}</strong>
                            <div class="mt-1">
                                <a href="{% url 'organizations:edit_official' official.id %}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-edit"></i> تعديل
                                </a>
                                <a href="{% url 'organizations:delete_official' official.id %}" class="btn btn-sm btn-outline-danger">
                                    <i class="fas fa-trash"></i> حذف
                                </a>
                            </div>
                        </div>
                        {% endfor %}
                        {% if officials_count > 3 %}
                        <small class="text-muted">... و {{ officials_count|add:"-3" }} آخرين</small>
                        {% endif %}
                    </div>
                </div>
                {% endwith %}
            {% endfor %}
        </div>
        {% endif %}
        
        <div class="text-center">
            <a href="{% url 'organizations:officials_list' %}" class="btn btn-secondary btn-lg">
                <i class="fas fa-arrow-right me-2"></i>العودة للصفحة الأصلية
            </a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
