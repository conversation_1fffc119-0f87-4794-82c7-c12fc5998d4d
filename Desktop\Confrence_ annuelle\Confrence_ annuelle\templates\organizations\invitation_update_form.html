{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}تعديل الدعوة{% endblock %}

{% block extra_css %}
<style>
    .hero-mini {
        background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
        color: white;
        padding: 3rem 0;
        text-align: center;
        position: relative;
        overflow: hidden;
        margin-bottom: 2rem;
    }
    
    .hero-mini::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url('{% static "img/pattern.png" %}');
        opacity: 0.1;
    }
    
    .hero-mini .container {
        position: relative;
        z-index: 1;
    }
    
    .form-card {
        border: none;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
        border-radius: 0.5rem;
        overflow: hidden;
        margin-bottom: 2rem;
    }
    
    .form-card .card-header {
        background: linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%);
        border-bottom: none;
        padding: 1.25rem 1.5rem;
    }
    
    .form-card .card-body {
        padding: 1.5rem;
    }
    
    .form-control:focus {
        border-color: #3949ab;
        box-shadow: 0 0 0 0.25rem rgba(57, 73, 171, 0.25);
    }
    
    .btn-primary {
        background-color: #3949ab;
        border-color: #3949ab;
    }
    
    .btn-primary:hover {
        background-color: #303f9f;
        border-color: #303f9f;
    }
    
    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 50px;
        font-weight: 600;
        font-size: 0.8rem;
        margin-right: 0.5rem;
    }
    
    .status-draft {
        background-color: #e9ecef;
        color: #495057;
    }
    
    .status-sent {
        background-color: #d4edda;
        color: #155724;
    }
    
    .status-cancelled {
        background-color: #f8d7da;
        color: #721c24;
    }
    
    .status-responded {
        background-color: #cce5ff;
        color: #004085;
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="hero-mini text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-md-8 mx-auto text-center">
                <h1 class="display-5 fw-bold mb-3">تعديل الدعوة</h1>
                <p class="lead">تعديل محتوى وحالة الدعوة المرسلة للمؤسسة</p>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Navigation Links -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'organizations:organization_list' %}">المؤسسات</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'organizations:invitation_list' %}">الدعوات</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'organizations:invitation_detail' invitation.pk %}">تفاصيل الدعوة</a></li>
                    <li class="breadcrumb-item active" aria-current="page">تعديل الدعوة</li>
                </ol>
            </nav>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card form-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">تعديل الدعوة</h4>
                    {% if invitation.status == 'draft' %}
                    <span class="status-badge status-draft">
                        <i class="fas fa-file me-1"></i> مسودة
                    </span>
                    {% elif invitation.status == 'sent' %}
                    <span class="status-badge status-sent">
                        <i class="fas fa-paper-plane me-1"></i> مرسلة
                    </span>
                    {% elif invitation.status == 'cancelled' %}
                    <span class="status-badge status-cancelled">
                        <i class="fas fa-ban me-1"></i> ملغاة
                    </span>
                    {% elif invitation.status == 'responded' %}
                    <span class="status-badge status-responded">
                        <i class="fas fa-reply me-1"></i> تم الرد
                    </span>
                    {% endif %}
                </div>
                <div class="card-body">
                    {% if invitation.status == 'sent' %}
                    <div class="alert alert-warning" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تنبيه:</strong> هذه الدعوة تم إرسالها بالفعل. يمكنك فقط تعديل العنوان والرسالة، ولكن لا يمكنك تغيير حالتها. إذا كنت ترغب في إلغاء الدعوة، يرجى استخدام زر "إلغاء الدعوة" في صفحة التفاصيل.
                    </div>
                    {% endif %}
                    
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <div class="mb-4">
                            <h5 class="mb-3"><i class="fas fa-info-circle me-2 text-primary"></i>معلومات المؤسسة</h5>
                            <div class="mb-3">
                                <strong>المؤسسة:</strong>
                                <a href="{% url 'organizations:organization_detail' invitation.organization.pk %}" class="text-decoration-none">
                                    {{ invitation.organization.name }}
                                </a>
                            </div>
                            <div class="mb-3">
                                <strong>البريد الإلكتروني:</strong>
                                <span>{{ invitation.organization.email }}</span>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <h5 class="mb-3"><i class="fas fa-envelope-open-text me-2 text-primary"></i>محتوى الدعوة</h5>
                            
                            <div class="mb-3">
                                <label for="{{ form.subject.id_for_label }}" class="form-label">عنوان الدعوة <span class="text-danger">*</span></label>
                                {{ form.subject|add_class:"form-control" }}
                                {% if form.subject.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.subject.errors }}
                                </div>
                                {% endif %}
                            </div>
                            
                            <div class="mb-3">
                                <label for="{{ form.message.id_for_label }}" class="form-label">محتوى الرسالة <span class="text-danger">*</span></label>
                                {{ form.message|add_class:"form-control" }}
                                {% if form.message.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.message.errors }}
                                </div>
                                {% endif %}
                                <div class="form-text">يمكنك استخدام النص العادي أو تنسيق HTML بسيط.</div>
                            </div>
                            
                            {% if invitation.status == 'draft' %}
                            <div class="mb-3">
                                <label for="{{ form.status.id_for_label }}" class="form-label">حالة الدعوة</label>
                                {{ form.status|add_class:"form-select" }}
                                {% if form.status.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.status.errors }}
                                </div>
                                {% endif %}
                                <div class="form-text">إذا قمت بتغيير الحالة إلى "مرسلة"، سيتم إرسال الدعوة تلقائيًا عبر البريد الإلكتروني.</div>
                            </div>
                            {% endif %}
                        </div>
                        
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'organizations:invitation_detail' invitation.pk %}" class="btn btn-secondary">
                                <i class="fas fa-times me-1"></i> إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> حفظ التغييرات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}