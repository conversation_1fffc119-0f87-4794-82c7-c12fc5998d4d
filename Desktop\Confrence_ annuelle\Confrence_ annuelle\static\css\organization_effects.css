/* Organization Effects CSS - تأثيرات بصرية جانبية للمؤسسات */

/* تأثيرات الخلفية المتحركة */
.bg-decoration {
    position: absolute;
    border-radius: 50%;
    filter: blur(40px);
    opacity: 0.5;
    z-index: -1;
    animation: float 15s ease-in-out infinite;
}

/* تأكد من أن محتوى الصفحة لا يتداخل مع القوائم المنسدلة */
.container.py-4 {
    position: relative;
    z-index: 1;
}

.bg-decoration-1 {
    width: 300px;
    height: 300px;
    background: linear-gradient(45deg, #3498db, #2ecc71);
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.bg-decoration-2 {
    width: 200px;
    height: 200px;
    background: linear-gradient(45deg, #e74c3c, #f39c12);
    top: 20%;
    right: 15%;
    animation-delay: -5s;
}

.bg-decoration-3 {
    width: 250px;
    height: 250px;
    background: linear-gradient(45deg, #9b59b6, #3498db);
    bottom: 15%;
    left: 20%;
    animation-delay: -10s;
}

.bg-decoration-4 {
    width: 180px;
    height: 180px;
    background: linear-gradient(45deg, #f1c40f, #e67e22);
    bottom: 10%;
    right: 10%;
    animation-delay: -7s;
}

/* تأثير العائم للعناصر الزخرفية */
@keyframes float {
    0% {
        transform: translateY(0) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(5deg);
    }
    100% {
        transform: translateY(0) rotate(0deg);
    }
}

/* تأثيرات بطاقات المؤسسات */
.organization-card {
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 0.8s forwards;
}

.organization-card.show {
    opacity: 1;
    transform: translateY(0);
}

/* تأثير الظهور التدريجي للبطاقات */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* تأثير التحويم على البطاقات */
.organization-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

/* تأثير الدوائر المتدرجة عند التحويم */
.organization-card::before,
.organization-card::after {
    content: '';
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(45deg, #3498db, #2ecc71);
    opacity: 0;
    transition: all 0.6s ease;
    z-index: -1;
}

.organization-card::before {
    width: 150px;
    height: 150px;
    top: -75px;
    right: -75px;
}

.organization-card::after {
    width: 100px;
    height: 100px;
    bottom: -50px;
    left: -50px;
    background: linear-gradient(45deg, #e74c3c, #f39c12);
}

.organization-card:hover::before,
.organization-card:hover::after {
    opacity: 0.3;
    transform: scale(1.2);
}

/* تأثير نبض للشارات */
.badge {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(52, 152, 219, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(52, 152, 219, 0);
    }
}

/* تأثير لمعان للشعارات */
.organization-logo {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.organization-logo::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.8) 50%, rgba(255,255,255,0) 100%);
    transform: rotate(45deg) translateY(-100%);
    transition: all 0.6s ease;
}

.organization-logo:hover::before {
    transform: rotate(45deg) translateY(100%);
}

.organization-logo:hover {
    transform: scale(1.05) rotate(2deg);
}

/* تأثيرات الأزرار */
.btn-action {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.btn-action::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background-color: white;
    transition: all 0.5s ease;
}

.btn-action:hover::before {
    left: 0;
}

/* تأثير نبض للأزرار عند النقر */
.pulse-once {
    animation: pulse-button 0.5s ease-out;
}

@keyframes pulse-button {
    0% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.7);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 0 10px rgba(52, 152, 219, 0);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(52, 152, 219, 0);
    }
}

/* تأثير قسم البطل */
.hero-mini {
    position: relative;
    overflow: hidden;
    clip-path: polygon(0 0, 100% 0, 100% 85%, 50% 100%, 0 85%);
}

/* تأثير الشفافية للبطاقات */
.card-glass {
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 8px 32px rgba(31, 38, 135, 0.2);
}

/* تأثير الخط المتموج تحت العناوين */
.wavy-underline {
    position: relative;
}

.wavy-underline::after {
    content: '';
    position: absolute;
    left: 0;
    bottom: -5px;
    width: 100%;
    height: 4px;
    background: linear-gradient(45deg, #3498db, #2ecc71, #3498db);
    background-size: 200% 100%;
    animation: gradient 3s ease infinite;
}

@keyframes gradient {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* تأثير الشعار المتحرك */
.logo-container {
    position: relative;
    overflow: hidden;
    border-radius: 50%;
    padding: 5px;
    background: linear-gradient(45deg, #3498db, #2ecc71);
    animation: rotate 10s linear infinite;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* تأثير الظل المتحرك */
.shadow-pulse {
    animation: shadowPulse 3s infinite;
}

@keyframes shadowPulse {
    0% {
        box-shadow: 0 0 10px rgba(52, 152, 219, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(46, 204, 113, 0.7);
    }
    100% {
        box-shadow: 0 0 10px rgba(52, 152, 219, 0.5);
    }
}

/* تأثير الخلفية المتموجة */
.wave-bg {
    position: relative;
    overflow: hidden;
}

.wave-bg::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 15px;
    background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120' preserveAspectRatio='none'%3E%3Cpath d='M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z' opacity='.25' class='shape-fill'%3E%3C/path%3E%3Cpath d='M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z' opacity='.5' class='shape-fill'%3E%3C/path%3E%3Cpath d='M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z' class='shape-fill'%3E%3C/path%3E%3C/svg%3E") no-repeat;
    background-size: cover;
}

/* تأثير الشعار الدوار */
.rotating-logo {
    animation: spin 10s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* تأثير الأيقونات النابضة */
.icon-pulse {
    animation: iconPulse 2s ease infinite;
}

@keyframes iconPulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

/* تأثير الشريط المتحرك */
.ribbon {
    position: absolute;
    top: 15px;
    right: -30px;
    transform: rotate(45deg);
    background: linear-gradient(45deg, #3498db, #2ecc71);
    padding: 5px 40px;
    color: white;
    font-weight: bold;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    z-index: 1;
}

/* تأثير الخلفية المتدرجة المتحركة */
.gradient-bg {
    background: linear-gradient(-45deg, #3498db, #2ecc71, #9b59b6, #f1c40f);
    background-size: 400% 400%;
    animation: gradientBG 15s ease infinite;
}

@keyframes gradientBG {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* تأثير الظل المتعدد */
.multi-shadow {
    box-shadow: 
        0 5px 10px rgba(52, 152, 219, 0.3),
        0 10px 20px rgba(46, 204, 113, 0.2),
        0 15px 30px rgba(155, 89, 182, 0.1);
}

/* تأثير الحدود المتدرجة */
.gradient-border {
    position: relative;
    border: none;
}

.gradient-border::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #3498db, #2ecc71, #9b59b6, #f1c40f);
    z-index: -1;
    border-radius: inherit;
    background-size: 400% 400%;
    animation: gradientBG 15s ease infinite;
}