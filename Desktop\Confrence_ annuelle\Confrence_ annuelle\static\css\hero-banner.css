/* Hero Banner Styles */
.hero-banner {
    background-image: url('/static/img/مؤتمر السيرة النبوية السنوي في موريتانيا.png');
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    padding: 0;
    margin-bottom: 2rem;
    border-radius: 0.5rem;
    position: relative;
    overflow: hidden;
    height: auto;
    min-height: 50vh;
    max-height: 70vh;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-banner::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 77, 64, 0.2);
    z-index: 1;
}

.hero-banner .card-body {
    position: relative;
    z-index: 2;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 1rem;
    padding: 2rem;
    max-width: 60%;
    margin: 0 auto;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.hero-banner h1 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
    color: #004d40;
}

.hero-banner p {
    font-size: 1.5rem;
    opacity: 0.9;
    color: #004d40;
}

.hero-banner .btn-light {
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    background-color: #004d40;
    color: white;
    border: none;
    transition: all 0.3s ease;
}

.hero-banner .btn-light:hover {
    background-color: #00695c;
    transform: translateY(-2px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .hero-banner {
        height: 80vh;
    }
    
    .hero-banner .card-body {
        max-width: 90%;
        padding: 1.5rem;
    }
    
    .hero-banner h1 {
        font-size: 2.5rem;
    }
    
    .hero-banner p {
        font-size: 1.2rem;
    }
}