{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% block title %}{% trans "اتصل بـ" %} {{ organization.name }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
<style>
    /* Hero Mini Styles */
    .hero-mini {
        background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
        color: white;
        padding: 4rem 0;
        text-align: center;
        position: relative;
        overflow: hidden;
        margin-bottom: 3rem;
        box-shadow: 0 0.5rem 2rem rgba(0, 0, 0, 0.2);
        border-radius: 0 0 2.5rem 2.5rem;
    }
    
    .hero-mini::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url('{% static "img/pattern.png" %}');
        opacity: 0.1;
        animation: pulse 8s infinite alternate;
    }
    
    @keyframes pulse {
        0% { opacity: 0.05; transform: scale(1); }
        100% { opacity: 0.2; transform: scale(1.05); }
    }
    
    .organization-logo-medium {
        width: 150px;
        height: 150px;
        object-fit: contain;
        margin: 0 auto 1.5rem;
        border-radius: 50%;
        padding: 0.5rem;
        background-color: white;
        border: 5px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.3);
        transition: all 0.4s ease;
    }
    
    .organization-logo-medium:hover {
        transform: scale(1.08) rotate(5deg);
        box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.35);
    }
    
    .contact-form-card {
        border: none;
        box-shadow: 0 0.8rem 2rem rgba(0, 0, 0, 0.1);
        border-radius: 1.5rem;
        overflow: hidden;
        transition: transform 0.4s ease, box-shadow 0.4s ease;
        background-color: #ffffff;
        border-right: 5px solid #3949ab;
    }
    
    .contact-form-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 1.2rem 3rem rgba(0, 0, 0, 0.18);
    }
    
    .form-floating > label {
        right: auto;
        left: auto;
    }
    
    .form-control {
        border-width: 2px;
        transition: all 0.3s ease;
        border-radius: 0.75rem;
        padding: 1.2rem 1rem;
    }
    
    .form-control:focus {
        border-color: #3949ab;
        box-shadow: 0 0 0 0.25rem rgba(57, 73, 171, 0.25);
        transform: translateY(-3px);
    }
    
    .input-focused {
        transform: translateY(-3px);
    }
    
    .form-floating {
        transition: all 0.3s ease;
        margin-bottom: 1.5rem;
    }
    
    .was-validated .form-control:valid {
        border-color: #198754;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: right calc(0.375em + 0.1875rem) center;
        background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
    }
    
    .was-validated .form-control:invalid {
        border-color: #dc3545;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: right calc(0.375em + 0.1875rem) center;
        background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
    }
    
    .btn-primary {
        transition: all 0.4s ease;
        border-radius: 0.75rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        background: linear-gradient(135deg, #3949ab 0%, #1a237e 100%);
        border: none;
        position: relative;
        overflow: hidden;
        z-index: 1;
    }
    
    .btn-primary::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, rgba(255,255,255,0.2), rgba(255,255,255,0));
        transition: all 0.6s ease;
        z-index: -1;
    }
    
    .btn-primary:hover::before {
        left: 100%;
    }
    
    .btn-primary:hover {
        transform: translateY(-4px);
        box-shadow: 0 0.8rem 1.5rem rgba(0, 0, 0, 0.2);
        background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
    }
    
    .btn-primary:active {
        transform: translateY(-2px);
    }
    
    .org-info-card {
        border-radius: 1.5rem;
        overflow: hidden;
        box-shadow: 0 0.8rem 2rem rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        border: none;
        background-color: #ffffff;
        border-left: 5px solid #3949ab;
    }
    
    .org-info-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 1.2rem 3rem rgba(0, 0, 0, 0.18);
    }
    
    .org-info-icon {
        width: 40px;
        height: 40px;
        min-width: 40px;
        border-radius: 50%;
        background-color: rgba(57, 73, 171, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        color: #3949ab;
        transition: all 0.3s ease;
    }
    
    .org-info-item {
        padding: 0.5rem;
        border-radius: 0.75rem;
        transition: all 0.3s ease;
    }
    
    .org-info-item:hover {
        background-color: rgba(57, 73, 171, 0.05);
    }
    
    .org-info-item:hover .org-info-icon {
        background-color: #3949ab;
        color: white;
        transform: scale(1.1) rotate(10deg);
    }
    
    .card-header {
        border-bottom: none;
        padding: 1.25rem 1.5rem;
    }
    
    .card-body {
        padding: 1.5rem;
    }
</style>
{% endblock %}

{% block extra_js %}
<script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Form validation
        const forms = document.querySelectorAll('.needs-validation');
        
        Array.from(forms).forEach(form => {
            form.addEventListener('submit', event => {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                } else {
                    // Show loading state on submit button
                    const submitBtn = document.getElementById('submitBtn');
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>{% trans "جاري الإرسال..." %}';
                    submitBtn.disabled = true;
                    
                    // Re-enable button after 3 seconds if form doesn't submit (for demo purposes)
                    setTimeout(() => {
                        if (submitBtn.disabled) {
                            submitBtn.innerHTML = originalText;
                            submitBtn.disabled = false;
                        }
                    }, 3000);
                }
                
                form.classList.add('was-validated');
            }, false);
        });
        
        // Input focus effects
        const formControls = document.querySelectorAll('.form-control');
        formControls.forEach(control => {
            control.addEventListener('focus', () => {
                control.parentElement.classList.add('input-focused');
            });
            
            control.addEventListener('blur', () => {
                control.parentElement.classList.remove('input-focused');
            });
        });
        
        // Animate form elements on load
        const formElements = document.querySelectorAll('.form-floating, .d-grid');
        formElements.forEach((element, index) => {
            element.style.opacity = '0';
            element.style.transform = 'translateY(20px)';
            
            setTimeout(() => {
                element.style.transition = 'all 0.5s ease';
                element.style.opacity = '1';
                element.style.transform = 'translateY(0)';
            }, 100 + (index * 100));
        });
        
        // Animate info items
        const infoItems = document.querySelectorAll('.org-info-item');
        infoItems.forEach((item, index) => {
            item.style.opacity = '0';
            item.style.transform = 'translateX(-20px)';
            
            setTimeout(() => {
                item.style.transition = 'all 0.5s ease';
                item.style.opacity = '1';
                item.style.transform = 'translateX(0)';
            }, 100 + (index * 100));
        });
        
        // Icon hover effects
        const infoIcons = document.querySelectorAll('.org-info-icon');
        infoIcons.forEach(icon => {
            icon.addEventListener('mouseenter', () => {
                icon.style.transform = 'scale(1.1) rotate(10deg)';
            });
            
            icon.addEventListener('mouseleave', () => {
                icon.style.transform = 'scale(1) rotate(0)';
            });
        });
    });
</script>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="hero-mini text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-md-8 mx-auto text-center">
                {% if organization.logo %}
                <img src="{{ organization.logo.url }}" alt="{{ organization.name }}" class="organization-logo-medium">
                {% else %}
                <div class="organization-logo-medium d-flex align-items-center justify-content-center bg-light mx-auto">
                    <i class="fas fa-building fa-3x text-primary"></i>
                </div>
                {% endif %}
                
                <h1 class="display-5 fw-bold mb-3">{% trans "اتصل بـ" %} {% if organization.name_fr %}{{ organization.name_fr }}{% else %}{{ organization.name }}{% endif %}</h1>
                <p class="lead mb-0">{% trans "يمكنك إرسال رسالة مباشرة إلى المؤسسة من خلال النموذج أدناه" %}</p>
            </div>
        </div>
    </div>
</div>

<div class="container mt-5 mb-5">
    <div class="row">
        <!-- Organization Information (Left Side) -->
        <div class="col-lg-5 order-lg-1">
            <div class="org-info-card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-building me-2"></i>{% trans "معلومات المؤسسة" %}</h5>
                </div>
                <div class="card-body">
                    {% if organization.email %}
                    <div class="org-info-item d-flex align-items-center mb-3">
                        <div class="org-info-icon">
                            <i class="bi bi-envelope-fill"></i>
                        </div>
                        <div>
                            <h6 class="mb-0">{% trans "البريد الإلكتروني" %}</h6>
                            <p class="mb-0">{{ organization.email }}</p>
                        </div>
                    </div>
                    {% endif %}

                    {% if organization.phone_number %}
                    <div class="org-info-item d-flex align-items-center mb-3">
                        <div class="org-info-icon">
                            <i class="bi bi-telephone-fill"></i>
                        </div>
                        <div>
                            <h6 class="mb-0">{% trans "رقم الهاتف" %}</h6>
                            <p class="mb-0">{{ organization.phone_number }}</p>
                        </div>
                    </div>
                    {% endif %}

                    {% if organization.website %}
                    <div class="org-info-item d-flex align-items-center mb-3">
                        <div class="org-info-icon">
                            <i class="bi bi-globe"></i>
                        </div>
                        <div>
                            <h6 class="mb-0">{% trans "الموقع الإلكتروني" %}</h6>
                            <p class="mb-0">
                                <a href="{{ organization.website }}" target="_blank" class="text-decoration-none">{{ organization.website }}</a>
                            </p>
                        </div>
                    </div>
                    {% endif %}

                    {% if organization.contact_person %}
                    <div class="org-info-item d-flex align-items-center mb-3">
                        <div class="org-info-icon">
                            <i class="bi bi-person-fill"></i>
                        </div>
                        <div>
                            <h6 class="mb-0">{% trans "الشخص المسؤول" %}</h6>
                            <p class="mb-0">{{ organization.contact_person }}</p>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>

            {% if organization.latitude and organization.longitude %}
            <div id="organization-map" class="mb-4"></div>
            {% endif %}

            <div class="text-center">
                <a href="{% url 'organizations:organization_detail' organization.id %}" class="btn btn-outline-primary">
                    <i class="bi bi-arrow-right me-2"></i>{% trans "العودة إلى صفحة المؤسسة" %}
                </a>
            </div>
        </div>

        <!-- Contact Form (Right Side) -->
        <div class="col-lg-7 order-lg-2">
            <div class="contact-form-card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="bi bi-envelope me-2"></i>{% trans "نموذج الاتصال" %}</h5>
                </div>
                <div class="card-body">
                    <form method="post" class="needs-validation" novalidate>
                        {% csrf_token %}
                        
                        <div class="form-floating mb-3">
                            <input type="text" class="form-control" id="id_name" name="name" placeholder="{% trans 'الاسم' %}" required>
                            <label for="id_name">{% trans "الاسم" %}</label>
                            <div class="invalid-feedback">{% trans "يرجى إدخال اسمك" %}</div>
                        </div>
                        
                        <div class="form-floating mb-3">
                            <input type="email" class="form-control" id="id_email" name="email" placeholder="{% trans 'البريد الإلكتروني' %}" required>
                            <label for="id_email">{% trans "البريد الإلكتروني" %}</label>
                            <div class="invalid-feedback">{% trans "يرجى إدخال بريد إلكتروني صحيح" %}</div>
                        </div>
                        
                        <div class="form-floating mb-3">
                            <input type="text" class="form-control" id="id_subject" name="subject" placeholder="{% trans 'الموضوع' %}" required>
                            <label for="id_subject">{% trans "الموضوع" %}</label>
                            <div class="invalid-feedback">{% trans "يرجى إدخال موضوع الرسالة" %}</div>
                        </div>
                        
                        <div class="form-floating mb-4">
                            <textarea class="form-control" id="id_message" name="message" placeholder="{% trans 'الرسالة' %}" style="height: 150px" required></textarea>
                            <label for="id_message">{% trans "الرسالة" %}</label>
                            <div class="invalid-feedback">{% trans "يرجى إدخال نص الرسالة" %}</div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary" id="submitBtn">
                                <i class="bi bi-send me-2"></i>{% trans "إرسال الرسالة" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}