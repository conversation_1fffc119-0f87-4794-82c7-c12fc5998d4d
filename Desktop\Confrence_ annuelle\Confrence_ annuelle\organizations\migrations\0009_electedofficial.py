# Generated by Django 5.2.4 on 2025-07-05 15:32

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('organizations', '0008_invitation_status_invitation_updated_at_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ElectedOfficial',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=200, verbose_name='الاسم الكامل')),
                ('position', models.CharField(choices=[('mayor', 'عمدة'), ('deputy', 'نائب'), ('head', 'رئيس جهة')], max_length=20, verbose_name='المنصب')),
                ('region', models.CharField(max_length=100, verbose_name='المنطقة/الدائرة')),
                ('party', models.Char<PERSON><PERSON>(blank=True, max_length=100, null=True, verbose_name='الحزب السياسي')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('status', models.CharField(choices=[('active', 'نشط'), ('inactive', 'غير نشط'), ('retired', 'متقاعد')], default='active', max_length=20, verbose_name='الحالة')),
                ('start_date', models.DateField(verbose_name='تاريخ بداية المنصب')),
                ('end_date', models.DateField(blank=True, null=True, verbose_name='تاريخ نهاية المنصب')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإضافة')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'منتخب',
                'verbose_name_plural': 'المنتخبون',
                'ordering': ['name'],
            },
        ),
    ]
