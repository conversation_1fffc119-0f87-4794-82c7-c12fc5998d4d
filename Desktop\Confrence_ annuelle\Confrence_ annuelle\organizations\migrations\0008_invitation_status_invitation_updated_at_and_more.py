# Generated by Django 5.1.2 on 2025-07-04 16:40

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('organizations', '0007_scholarinvitation_phone_number_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='invitation',
            name='status',
            field=models.CharField(choices=[('draft', 'Draft'), ('sent', 'Sent'), ('cancelled', 'Cancelled'), ('responded', 'Responded')], default='draft', max_length=20, verbose_name='Status'),
        ),
        migrations.AddField(
            model_name='invitation',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, verbose_name='Updated At'),
        ),
        migrations.AddField(
            model_name='scholar',
            name='application_status',
            field=models.CharField(blank=True, help_text='حالة طلب المشاركة أو التقديم', max_length=100, null=True, verbose_name='حالة التقديم'),
        ),
        migrations.AddField(
            model_name='scholar',
            name='presentation_title',
            field=models.CharField(blank=True, max_length=500, null=True, verbose_name='عنوان العرض'),
        ),
        migrations.AddField(
            model_name='scholar',
            name='presentation_type',
            field=models.CharField(blank=True, choices=[('none', 'لا يوجد'), ('paper', 'ورقة بحثية'), ('lecture', 'محاضرة'), ('workshop', 'ورشة عمل')], default='none', max_length=20, null=True, verbose_name='نوع العرض'),
        ),
    ]
