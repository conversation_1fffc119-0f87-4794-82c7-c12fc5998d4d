/* Visual Effects CSS - تأثيرات بصرية متقدمة */

/* تأثيرات جديدة للواجهة العلوية */
.navbar {
    position: relative;
    overflow: visible;
    z-index: 9000;
}

/* .navbar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, #3498db, #9b59b6, #e74c3c, #f1c40f, #2ecc71);
    z-index: 1001;
    animation: gradient-shift 5s linear infinite;
    background-size: 500% 100%;
} */

@keyframes gradient-shift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* تأثير الخلفية المبسطة */
.animated-bg {
    position: relative;
    overflow: hidden;
}

.animated-bg::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(
        circle at center,
        rgba(255, 255, 255, 0.02) 0%,
        rgba(255, 255, 255, 0) 50%
    );
    /* تم إبطاء الحركة وتقليل التأثير */
    animation: rotate 30s linear infinite;
    z-index: -1;
}

.animated-bg.color-shift::before {
    background: linear-gradient(
        135deg,
        rgba(52, 152, 219, 0.03) 0%,
        rgba(155, 89, 182, 0.03) 100%
    );
    /* تم إيقاف الدوران للخلفية الملونة */
    animation: none;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* تأثير الزجاج */
/* .glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
    border-radius: 10px;
    transition: all 0.3s ease;
} */

.glass-effect:hover {
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.glass-effect.dark {
    background: rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.glass-effect.colored {
    background: rgba(52, 152, 219, 0.1);
}

/* تأثير النيون للنصوص */
/* .neon-text {
    color: #3498db;
    text-shadow: 0 0 5px rgba(52, 152, 219, 0.5), 0 0 10px rgba(52, 152, 219, 0.3);
    transition: all 0.3s ease;
} */

/* تأثير الظهور عند التمرير - مبسط */
.fade-in-element {
    opacity: 0;
    transform: translateY(15px);
    transition: opacity 0.5s ease, transform 0.5s ease;
}

.fade-in-element.visible {
    opacity: 1;
    transform: translateY(0);
}

/* تأثيرات ظهور متنوعة - مبسطة */
.fade-in-scale {
    opacity: 0;
    transform: scale(0.95);
    transition: opacity 0.4s ease-out, transform 0.4s ease-out;
}

.fade-in-scale.visible {
    opacity: 1;
    transform: scale(1);
}

/* تم تبسيط تأثير الدوران */
.fade-in-rotate {
    opacity: 0;
    transform: translateY(10px);
    transition: opacity 0.4s ease-out, transform 0.4s ease-out;
}

.fade-in-rotate.visible {
    opacity: 1;
    transform: translateY(0);
}

/* تأثير الزجاج المبسط */
.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.glass-effect:hover {
    background: rgba(255, 255, 255, 0.12);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.glass-effect.dark {
    background: rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.glass-effect.colored {
     background: rgba(52, 152, 219, 0.1);
  }

/* تأثير النص المميز - تم تقليل التوهج */
.neon-text {
    color: #fff;
    text-shadow: 0 0 3px #0d6efd;
    transition: all 0.3s ease;
}

.neon-text:hover {
    text-shadow: 0 0 5px #0d6efd;
}

.neon-text.purple {
    text-shadow: 0 0 3px #9b59b6;
}

.neon-text.purple:hover {
    text-shadow: 0 0 5px #9b59b6;
}

.neon-text.green {
    text-shadow: 0 0 3px #2ecc71;
}

.neon-text.green:hover {
    text-shadow: 0 0 5px #2ecc71;
}

/* تأثير الحدوب المحسنة - تم تقليل التوهج */
/* .glow-border {
    border: 1px solid #0d6efd;
    box-shadow: 0 0 3px rgba(13, 110, 253, 0.3);
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
} */

.glow-border:hover {
    box-shadow: 0 0 5px rgba(13, 110, 253, 0.5);
}

.glow-border.purple {
    border-color: #9b59b6;
    box-shadow: 0 0 3px rgba(155, 89, 182, 0.3);
}

.glow-border.purple:hover {
    box-shadow: 0 0 5px rgba(155, 89, 182, 0.5);
}

.glow-border.green {
    border-color: #2ecc71;
    box-shadow: 0 0 3px rgba(46, 204, 113, 0.3);
}

.glow-border.green:hover {
    box-shadow: 0 0 5px rgba(46, 204, 113, 0.5);
}

/* تم إزالة تأثير قوس قزح المتحرك */

/* تأثير الأزرار ثلاة الأبعاد */
.btn-3d {
    position: relative;
    border: none;
    background: linear-gradient(to bottom, #0d6efd, #0a58ca);
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    box-shadow: 0 5px 0 #084298, 0 10px 20px rgba(0, 0, 0, 0.2);
    transition: all 0.1s ease;
    overflow: hidden;
}

/* .btn-3d::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: all 0.5s ease;
} */

.btn-3d:hover::before {
    left: 100%;
}

.btn-3d:hover {
    transform: translateY(-2px);
    box-shadow: 0 7px 0 #084298, 0 14px 25px rgba(0, 0, 0, 0.25);
}

.btn-3d:active {
    transform: translateY(3px);
    box-shadow: 0 2px 0 #084298, 0 5px 10px rgba(0, 0, 0, 0.2);
}

.btn-3d.purple {
    background: linear-gradient(to bottom, #9b59b6, #8e44ad);
    box-shadow: 0 5px 0 #6c3483, 0 10px 20px rgba(0, 0, 0, 0.2);
}

.btn-3d.purple:hover {
    box-shadow: 0 7px 0 #6c3483, 0 14px 25px rgba(0, 0, 0, 0.25);
}

.btn-3d.green {
    background: linear-gradient(to bottom, #2ecc71, #27ae60);
    box-shadow: 0 5px 0 #1e8449, 0 10px 20px rgba(0, 0, 0, 0.2);
}

.btn-3d.green:hover {
    box-shadow: 0 7px 0 #1e8449, 0 14px 25px rgba(0, 0, 0, 0.25);
}

/* تأثير الشعار المتحرك المحسن */
.logo-animated {
    position: relative;
    overflow: hidden;
}

.logo-animated::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(
        transparent, 
        rgba(255, 255, 255, 0.3), 
        transparent 30%
    );
    animation: rotate 4s linear infinite;
}

.logo-animated img {
    transition: all 0.5s ease;
}

.logo-animated:hover img {
    transform: rotate(10deg) scale(1.1);
    filter: drop-shadow(0 0 5px rgba(52, 152, 219, 0.5));
}

.logo-animated.pulse img {
    animation: logo-pulse 2s infinite;
}

@keyframes logo-pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes rotate {
    100% { transform: rotate(360deg); }
}

/* تأثير الخلفية المتموجة المحسنة */
.wave-background {
    position: relative;
    overflow: hidden;
}

.wave-background::before,
.wave-background::after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100px;
    height: 100px;
}

.wave-background::before {
    bottom: 0;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120' preserveAspectRatio='none'%3E%3Cpath d='M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z' style='fill: rgba(255, 255, 255, 0.1);'%3E%3C/path%3E%3C/svg%3E");
}

.wave-background::after {
    bottom: 0;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120' preserveAspectRatio='none'%3E%3Cpath d='M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z' style='fill: rgba(255, 255, 255, 0.2);'%3E%3C/path%3E%3C/svg%3E");
    animation: wave 10s linear infinite;
}

.wave-background.double-wave::after {
    bottom: 20px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120' preserveAspectRatio='none'%3E%3Cpath d='M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z' style='fill: rgba(255, 255, 255, 0.15);'%3E%3C/path%3E%3C/svg%3E");
    animation: wave 15s linear infinite reverse;
}

.wave-background.colored::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120' preserveAspectRatio='none'%3E%3Cpath d='M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z' style='fill: rgba(52, 152, 219, 0.2);'%3E%3C/path%3E%3C/svg%3E");
}

@keyframes wave {
    0% { background-position-x: 0; }
    100% { background-position-x: 1000px; }
}

/* تأثير الكروت المتحركة */
.card-hover-effect {
    transition: all 0.5s ease;
    transform-style: preserve-3d;
    position: relative;
    overflow: hidden;
}

.card-hover-effect::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 50%);
    transition: all 0.3s ease;
}

.card-hover-effect:hover {
    transform: rotateY(10deg) rotateX(5deg) scale(1.05);
    box-shadow: 
        -20px 20px 30px rgba(0, 0, 0, 0.1),
        0 0 20px rgba(13, 110, 253, 0.2);
}

.card-hover-effect:hover::after {
    transform: scale(1.2) rotate(10deg);
}

.card-hover-effect.zoom-image img {
    transition: all 0.5s ease;
}

.card-hover-effect.zoom-image:hover img {
    transform: scale(1.1);
}

/* تأثير الأيقونات المتحركة */
.icon-float {
    animation: float 3s ease-in-out infinite;
    display: inline-block;
    transform-origin: center;
}

.icon-float.slow {
    animation-duration: 5s;
}

.icon-float.fast {
    animation-duration: 2s;
}

.icon-float.rotate {
    animation: float-rotate 3s ease-in-out infinite;
}

@keyframes float {
    0% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0); }
}

@keyframes float-rotate {
    0% { transform: translateY(0) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(10deg); }
    100% { transform: translateY(0) rotate(0deg); }
}

/* تأثير الشريط المتحرك المحسن */
.ribbon-enhanced {
    position: absolute;
    top: 20px;
    right: -5px;
    padding: 5px 10px;
    background: linear-gradient(45deg, #2ecc71, #27ae60);
    color: white;
    font-size: 14px;
    font-weight: bold;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    z-index: 1;
    overflow: hidden;
    transition: all 0.3s ease;
}

.ribbon-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    right: -10px;
    border-left: 10px solid #27ae60;
    border-top: 13px solid transparent;
    border-bottom: 13px solid transparent;
}

.ribbon-enhanced::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: ribbon-shine 2s infinite;
}

.ribbon-enhanced:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.3);
}

.ribbon-enhanced.purple {
    background: linear-gradient(45deg, #9b59b6, #8e44ad);
}

.ribbon-enhanced.purple::before {
    border-left-color: #8e44ad;
}

.ribbon-enhanced.green {
    background: linear-gradient(45deg, #2ecc71, #27ae60);
}

.ribbon-enhanced.green::before {
    border-left-color: #27ae60;
}

.ribbon-enhanced.animated {
    animation: ribbon-pulse 2s infinite;
}

@keyframes ribbon-shine {
    0% { left: -100%; }
    100% { left: 100%; }
}

@keyframes ribbon-pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* تأثير الظل متعدد المستويات */
.multi-layer-shadow {
    box-shadow:
        0 2px 4px rgba(0, 0, 0, 0.1),
        0 4px 8px rgba(0, 0, 0, 0.1),
        0 8px 16px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.multi-layer-shadow:hover {
    box-shadow:
        0 4px 8px rgba(0, 0, 0, 0.1),
        0 8px 16px rgba(0, 0, 0, 0.1),
        0 16px 32px rgba(0, 0, 0, 0.1);
}

.multi-layer-shadow.colored {
    box-shadow:
        0 2px 4px rgba(52, 152, 219, 0.1),
        0 4px 8px rgba(52, 152, 219, 0.1),
        0 8px 16px rgba(52, 152, 219, 0.1);
}

.multi-layer-shadow.colored:hover {
    box-shadow:
        0 4px 8px rgba(52, 152, 219, 0.15),
        0 8px 16px rgba(52, 152, 219, 0.15),
        0 16px 32px rgba(52, 152, 219, 0.15);
}

.multi-layer-shadow.purple {
    box-shadow:
        0 2px 4px rgba(155, 89, 182, 0.1),
        0 4px 8px rgba(155, 89, 182, 0.1),
        0 8px 16px rgba(155, 89, 182, 0.1);
}

.multi-layer-shadow.purple:hover {
    box-shadow:
        0 4px 8px rgba(155, 89, 182, 0.15),
        0 8px 16px rgba(155, 89, 182, 0.15),
        0 16px 32px rgba(155, 89, 182, 0.15);
}

/* تأثير الخلفية المتدرجة المتحركة */
.gradient-animation {
    background: linear-gradient(-45deg, #0d6efd, #2ecc71, #9b59b6, #f1c40f);
    background-size: 400% 400%;
    animation: gradientBG 15s ease infinite;
}

.gradient-animation.blue-purple {
    background: linear-gradient(-45deg, #3498db, #9b59b6, #2980b9, #8e44ad);
    background-size: 400% 400%;
    animation: gradientBG 15s ease infinite;
}

.gradient-animation.green-blue {
    background: linear-gradient(-45deg, #2ecc71, #3498db, #27ae60, #2980b9);
    background-size: 400% 400%;
    animation: gradientBG 15s ease infinite;
}

.gradient-animation.fast {
    animation-duration: 8s;
}

.gradient-animation.slow {
    animation-duration: 25s;
}

@keyframes gradientBG {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* تأثير الزر المضيء */
.btn-glow {
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn-glow::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0) 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.btn-glow:hover::after {
    opacity: 1;
}

.btn-glow.colored {
    border: 1px solid #3498db;
    color: #3498db;
    transition: all 0.3s ease;
}

.btn-glow.colored:hover {
    background-color: rgba(52, 152, 219, 0.1);
    box-shadow: 0 0 15px rgba(52, 152, 219, 0.5);
}

.btn-glow.purple {
    border: 1px solid #9b59b6;
    color: #9b59b6;
    transition: all 0.3s ease;
}

.btn-glow.purple:hover {
    background-color: rgba(155, 89, 182, 0.1);
    box-shadow: 0 0 15px rgba(155, 89, 182, 0.5);
}

.btn-glow.pulse {
    animation: btn-pulse 2s infinite;
}

@keyframes btn-pulse {
    0% { box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.4); }
    70% { box-shadow: 0 0 0 10px rgba(52, 152, 219, 0); }
    100% { box-shadow: 0 0 0 0 rgba(52, 152, 219, 0); }
}

/* تأثير التدرج الشفاف */
.gradient-opacity {
    position: relative;
}

.gradient-opacity::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0) 100%);
    z-index: 1;
    transition: opacity 0.3s ease;
}

.gradient-opacity:hover::after {
    opacity: 0.8;
}

.gradient-opacity.dark::after {
    background: linear-gradient(to bottom, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.2) 100%);
}

.gradient-opacity.side::after {
    background: linear-gradient(to right, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0) 100%);
}

.gradient-opacity.radial::after {
    background: radial-gradient(circle at center, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0) 70%);
}

/* تأثير الحركة عند التمرير */
.scroll-animate-right {
    opacity: 0;
    transform: translateX(-50px);
    transition: all 0.8s ease;
}

.scroll-animate-right.visible {
    opacity: 1;
    transform: translateX(0);
}

.scroll-animate-left {
    opacity: 0;
    transform: translateX(50px);
    transition: all 0.8s ease;
}

.scroll-animate-left.visible {
    opacity: 1;
    transform: translateX(0);
}

.scroll-animate-up {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.scroll-animate-up.visible {
    opacity: 1;
    transform: translateY(0);
}

.scroll-animate-down {
    opacity: 0;
    transform: translateY(-30px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.scroll-animate-down.visible {
    opacity: 1;
    transform: translateY(0);
}

.scroll-animate-scale {
    opacity: 0;
    transform: scale(0.8);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.scroll-animate-scale.visible {
    opacity: 1;
    transform: scale(1);
}

.scroll-animate-rotate {
    opacity: 0;
    transform: rotate(-10deg) scale(0.8);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.scroll-animate-rotate.visible {
    opacity: 1;
    transform: rotate(0deg) scale(1);
}

/* تأثير الظل المتحرك */
.moving-shadow {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.moving-shadow::after {
    content: '';
    position: absolute;
    top: 10px;
    left: 10px;
    right: -10px;
    bottom: -10px;
    background-color: rgba(0,0,0,0.1);
    z-index: -1;
    transition: all 0.3s ease;
}

/* تعريف تأثير النبض للأزرار عند النقر */
.pulse-once {
    animation: button-pulse 0.3s ease-in-out;
}

@keyframes button-pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.moving-shadow:hover::after {
    top: 15px;
    left: 15px;
    right: -15px;
    bottom: -15px;
}

.moving-shadow.colored::after {
    background-color: rgba(52, 152, 219, 0.1);
}

.moving-shadow.purple::after {
    background-color: rgba(155, 89, 182, 0.1);
}

.moving-shadow.green::after {
    background-color: rgba(46, 204, 113, 0.1);
}

.moving-shadow.side:hover::after {
    top: 10px;
    left: 15px;
    right: -15px;
    bottom: -10px;
}

.moving-shadow.diagonal:hover::after {
    top: 15px;
    left: 15px;
    right: -15px;
    bottom: -15px;
}


/* تأثير توسيع الأزرار عند التحويم */
.btn-hover-expand {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-hover-expand:hover {
    transform: scale(1.05);
    padding-left: 25px;
    padding-right: 25px;
}

.btn-hover-expand::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 0;
    width: 100%;
    height: 3px;
    background-color: rgba(0,0,0,0.1);
    z-index: -1;
    transition: all 0.3s ease;
}

.btn-hover-expand:hover::after {
    transform: scaleX(1);
    transform-origin: left;
}

.glass-effect:hover {
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.glass-effect.dark {
    background: rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.glass-effect.colored {
    background: rgba(52, 152, 219, 0.1);
}

/* تأثير النيون للنصوص */
/* .neon-text {
    color: #3498db;
    text-shadow: 0 0 5px rgba(52, 152, 219, 0.5), 0 0 10px rgba(52, 152, 219, 0.3);
    transition: all 0.3s ease;
} */

/* تأثير الظهور عند التمرير - مبسط */
.fade-in-element {
    opacity: 0;
    transform: translateY(15px);
    transition: opacity 0.5s ease, transform 0.5s ease;
}

.fade-in-element.visible {
    opacity: 1;
    transform: translateY(0);
}

/* تأثيرات ظهور متنوعة - مبسطة */
.fade-in-scale {
    opacity: 0;
    transform: scale(0.95);
    transition: opacity 0.4s ease-out, transform 0.4s ease-out;
}

.fade-in-scale.visible {
    opacity: 1;
    transform: scale(1);
}

/* تم تبسيط تأثير الدوران */
.fade-in-rotate {
    opacity: 0;
    transform: translateY(10px);
    transition: opacity 0.4s ease-out, transform 0.4s ease-out;
}

.fade-in-rotate.visible {
    opacity: 1;
    transform: translateY(0);
}

/* تأثير الزجاج المبسط */
.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(5px);
    -webkit-backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.glass-effect:hover {
    background: rgba(255, 255, 255, 0.12);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.glass-effect.dark {
    background: rgba(0, 0, 0, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.glass-effect.colored {
     background: rgba(52, 152, 219, 0.1);
  }

/* تأثير النص المميز - تم تقليل التوهج */
.neon-text {
    color: #fff;
    text-shadow: 0 0 3px #0d6efd;
    transition: all 0.3s ease;
}

.neon-text:hover {
    text-shadow: 0 0 5px #0d6efd;
}

.neon-text.purple {
    text-shadow: 0 0 3px #9b59b6;
}

.neon-text.purple:hover {
    text-shadow: 0 0 5px #9b59b6;
}

.neon-text.green {
    text-shadow: 0 0 3px #2ecc71;
}

.neon-text.green:hover {
    text-shadow: 0 0 5px #2ecc71;
}

/* تأثير الحدوب المحسنة - تم تقليل التوهج */
/* .glow-border {
    border: 1px solid #0d6efd;
    box-shadow: 0 0 3px rgba(13, 110, 253, 0.3);
    transition: all 0.3s ease;
    position: relative;
    z-index: 1;
} */

.glow-border:hover {
    box-shadow: 0 0 5px rgba(13, 110, 253, 0.5);
}

.glow-border.purple {
    border-color: #9b59b6;
    box-shadow: 0 0 3px rgba(155, 89, 182, 0.3);
}

.glow-border.purple:hover {
    box-shadow: 0 0 5px rgba(155, 89, 182, 0.5);
}

.glow-border.green {
    border-color: #2ecc71;
    box-shadow: 0 0 3px rgba(46, 204, 113, 0.3);
}

.glow-border.green:hover {
    box-shadow: 0 0 5px rgba(46, 204, 113, 0.5);
}

/* تم إزالة تأثير قوس قزح المتحرك */

/* تأثير الأزرار ثلاة الأبعاد */
.btn-3d {
    position: relative;
    border: none;
    background: linear-gradient(to bottom, #0d6efd, #0a58ca);
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    box-shadow: 0 5px 0 #084298, 0 10px 20px rgba(0, 0, 0, 0.2);
    transition: all 0.1s ease;
    overflow: hidden;
}

/* .btn-3d::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: all 0.5s ease;
} */

.btn-3d:hover::before {
    left: 100%;
}

.btn-3d:hover {
    transform: translateY(-2px);
    box-shadow: 0 7px 0 #084298, 0 14px 25px rgba(0, 0, 0, 0.25);
}

.btn-3d:active {
    transform: translateY(3px);
    box-shadow: 0 2px 0 #084298, 0 5px 10px rgba(0, 0, 0, 0.2);
}

.btn-3d.purple {
    background: linear-gradient(to bottom, #9b59b6, #8e44ad);
    box-shadow: 0 5px 0 #6c3483, 0 10px 20px rgba(0, 0, 0, 0.2);
}

.btn-3d.purple:hover {
    box-shadow: 0 7px 0 #6c3483, 0 14px 25px rgba(0, 0, 0, 0.25);
}

.btn-3d.green {
    background: linear-gradient(to bottom, #2ecc71, #27ae60);
    box-shadow: 0 5px 0 #1e8449, 0 10px 20px rgba(0, 0, 0, 0.2);
}

.btn-3d.green:hover {
    box-shadow: 0 7px 0 #1e8449, 0 14px 25px rgba(0, 0, 0, 0.25);
}

/* تأثير الشعار المتحرك المحسن */
.logo-animated {
    position: relative;
    overflow: hidden;
}

.logo-animated::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(
        transparent, 
        rgba(255, 255, 255, 0.3), 
        transparent 30%
    );
    animation: rotate 4s linear infinite;
}

.logo-animated img {
    transition: all 0.5s ease;
}

.logo-animated:hover img {
    transform: rotate(10deg) scale(1.1);
    filter: drop-shadow(0 0 5px rgba(52, 152, 219, 0.5));
}

.logo-animated.pulse img {
    animation: logo-pulse 2s infinite;
}

@keyframes logo-pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes rotate {
    100% { transform: rotate(360deg); }
}

/* تأثير الخلفية المتموجة المحسنة */
.wave-background {
    position: relative;
    overflow: hidden;
}

.wave-background::before,
.wave-background::after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    background-repeat: no-repeat;
    background-position: center;
    background-size: 100% 100px;
    height: 100px;
}

.wave-background::before {
    bottom: 0;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120' preserveAspectRatio='none'%3E%3Cpath d='M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z' style='fill: rgba(255, 255, 255, 0.1);'%3E%3C/path%3E%3C/svg%3E");
}

.wave-background::after {
    bottom: 0;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120' preserveAspectRatio='none'%3E%3Cpath d='M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z' style='fill: rgba(255, 255, 255, 0.2);'%3E%3C/path%3E%3C/svg%3E");
    animation: wave 10s linear infinite;
}

.wave-background.double-wave::after {
    bottom: 20px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120' preserveAspectRatio='none'%3E%3Cpath d='M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z' style='fill: rgba(255, 255, 255, 0.15);'%3E%3C/path%3E%3C/svg%3E");
    animation: wave 15s linear infinite reverse;
}

.wave-background.colored::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1200 120' preserveAspectRatio='none'%3E%3Cpath d='M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z' style='fill: rgba(52, 152, 219, 0.2);'%3E%3C/path%3E%3C/svg%3E");
}

@keyframes wave {
    0% { background-position-x: 0; }
    100% { background-position-x: 1000px; }
}

/* تأثير الكروت المتحركة */
.card-hover-effect {
    transition: all 0.5s ease;
    transform-style: preserve-3d;
    position: relative;
    overflow: hidden;
}

.card-hover-effect::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 50%);
    transition: all 0.3s ease;
}

.card-hover-effect:hover {
    transform: rotateY(10deg) rotateX(5deg) scale(1.05);
    box-shadow: 
        -20px 20px 30px rgba(0, 0, 0, 0.1),
        0 0 20px rgba(13, 110, 253, 0.2);
}

.card-hover-effect:hover::after {
    transform: scale(1.2) rotate(10deg);
}

.card-hover-effect.zoom-image img {
    transition: all 0.5s ease;
}

.card-hover-effect.zoom-image:hover img {
    transform: scale(1.1);
}

/* تأثير الأيقونات المتحركة */
.icon-float {
    animation: float 3s ease-in-out infinite;
    display: inline-block;
    transform-origin: center;
}

.icon-float.slow {
    animation-duration: 5s;
}

.icon-float.fast {
    animation-duration: 2s;
}

.icon-float.rotate {
    animation: float-rotate 3s ease-in-out infinite;
}

@keyframes float {
    0% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0); }
}

@keyframes float-rotate {
    0% { transform: translateY(0) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(10deg); }
    100% { transform: translateY(0) rotate(0deg); }
}

/* تأثير الشريط المتحرك المحسن */
.ribbon-enhanced {
    position: absolute;
    top: 20px;
    right: -5px;
    padding: 5px 10px;
    background: linear-gradient(45deg, #2ecc71, #27ae60);
    color: white;
    font-size: 14px;
    font-weight: bold;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
    z-index: 1;
    overflow: hidden;
    transition: all 0.3s ease;
}

.ribbon-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    right: -10px;
    border-left: 10px solid #27ae60;
    border-top: 13px solid transparent;
    border-bottom: 13px solid transparent;
}

.ribbon-enhanced::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: ribbon-shine 2s infinite;
}

.ribbon-enhanced:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.3);
}

.ribbon-enhanced.purple {
    background: linear-gradient(45deg, #9b59b6, #8e44ad);
}

.ribbon-enhanced.purple::before {
    border-left-color: #8e44ad;
}

.ribbon-enhanced.green {
    background: linear-gradient(45deg, #2ecc71, #27ae60);
}

.ribbon-enhanced.green::before {
    border-left-color: #27ae60;
}

.ribbon-enhanced.animated {
    animation: ribbon-pulse 2s infinite;
}

@keyframes ribbon-shine {
    0% { left: -100%; }
    100% { left: 100%; }
}

@keyframes ribbon-pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

/* تأثير الظل متعدد المستويات */
.multi-layer-shadow {
    box-shadow:
        0 2px 4px rgba(0, 0, 0, 0.1),
        0 4px 8px rgba(0, 0, 0, 0.1),
        0 8px 16px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.multi-layer-shadow:hover {
    box-shadow:
        0 4px 8px rgba(0, 0, 0, 0.1),
        0 8px 16px rgba(0, 0, 0, 0.1),
        0 16px 32px rgba(0, 0, 0, 0.1);
}

.multi-layer-shadow.colored {
    box-shadow:
        0 2px 4px rgba(52, 152, 219, 0.1),
        0 4px 8px rgba(52, 152, 219, 0.1),
        0 8px 16px rgba(52, 152, 219, 0.1);
}

.multi-layer-shadow.colored:hover {
    box-shadow:
        0 4px 8px rgba(52, 152, 219, 0.15),
        0 8px 16px rgba(52, 152, 219, 0.15),
        0 16px 32px rgba(52, 152, 219, 0.15);
}

.multi-layer-shadow.purple {
    box-shadow:
        0 2px 4px rgba(155, 89, 182, 0.1),
        0 4px 8px rgba(155, 89, 182, 0.1),
        0 8px 16px rgba(155, 89, 182, 0.1);
}

.multi-layer-shadow.purple:hover {
    box-shadow:
        0 4px 8px rgba(155, 89, 182, 0.15),
        0 8px 16px rgba(155, 89, 182, 0.15),
        0 16px 32px rgba(155, 89, 182, 0.15);
}

/* تأثير الخلفية المتدرجة المتحركة */
.gradient-animation {
    background: linear-gradient(-45deg, #0d6efd, #2ecc71, #9b59b6, #f1c40f);
    background-size: 400% 400%;
    animation: gradientBG 15s ease infinite;
}

.gradient-animation.blue-purple {
    background: linear-gradient(-45deg, #3498db, #9b59b6, #2980b9, #8e44ad);
    background-size: 400% 400%;
    animation: gradientBG 15s ease infinite;
}

.gradient-animation.green-blue {
    background: linear-gradient(-45deg, #2ecc71, #3498db, #27ae60, #2980b9);
    background-size: 400% 400%;
    animation: gradientBG 15s ease infinite;
}

.gradient-animation.fast {
    animation-duration: 8s;
}

.gradient-animation.slow {
    animation-duration: 25s;
}

@keyframes gradientBG {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* تأثير الزر المضيء */
.btn-glow {
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn-glow::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0) 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.btn-glow:hover::after {
    opacity: 1;
}

.btn-glow.colored {
    border: 1px solid #3498db;
    color: #3498db;
    transition: all 0.3s ease;
}

.btn-glow.colored:hover {
    background-color: rgba(52, 152, 219, 0.1);
    box-shadow: 0 0 15px rgba(52, 152, 219, 0.5);
}

.btn-glow.purple {
    border: 1px solid #9b59b6;
    color: #9b59b6;
    transition: all 0.3s ease;
}

.btn-glow.purple:hover {
    background-color: rgba(155, 89, 182, 0.1);
    box-shadow: 0 0 15px rgba(155, 89, 182, 0.5);
}

.btn-glow.pulse {
    animation: btn-pulse 2s infinite;
}

@keyframes btn-pulse {
    0% { box-shadow: 0 0 0 0 rgba(52, 152, 219, 0.4); }
    70% { box-shadow: 0 0 0 10px rgba(52, 152, 219, 0); }
    100% { box-shadow: 0 0 0 0 rgba(52, 152, 219, 0); }
}

/* تأثير التدرج الشفاف */
.gradient-opacity {
    position: relative;
}

.gradient-opacity::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0) 100%);
    z-index: 1;
    transition: opacity 0.3s ease;
}

.gradient-opacity:hover::after {
    opacity: 0.8;
}

.gradient-opacity.dark::after {
    background: linear-gradient(to bottom, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.2) 100%);
}

.gradient-opacity.side::after {
    background: linear-gradient(to right, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0) 100%);
}

.gradient-opacity.radial::after {
    background: radial-gradient(circle at center, rgba(0,0,0,0.7) 0%, rgba(0,0,0,0) 70%);
}

/* تأثير الحركة عند التمرير */
.scroll-animate-right {
    opacity: 0;
    transform: translateX(-50px);
    transition: all 0.8s ease;
}

.scroll-animate-right.visible {
    opacity: 1;
    transform: translateX(0);
}

.scroll-animate-left {
    opacity: 0;
    transform: translateX(50px);
    transition: all 0.8s ease;
}

.scroll-animate-left.visible {
    opacity: 1;
    transform: translateX(0);
}

.scroll-animate-up {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.scroll-animate-up.visible {
    opacity: 1;
    transform: translateY(0);
}

.scroll-animate-down {
    opacity: 0;
    transform: translateY(-30px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.scroll-animate-down.visible {
    opacity: 1;
    transform: translateY(0);
}

.scroll-animate-scale {
    opacity: 0;
    transform: scale(0.8);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.scroll-animate-scale.visible {
    opacity: 1;
    transform: scale(1);
}

.scroll-animate-rotate {
    opacity: 0;
    transform: rotate(-10deg) scale(0.8);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.scroll-animate-rotate.visible {
    opacity: 1;
    transform: rotate(0deg) scale(1);
}

/* تأثير الظل المتحرك */
.moving-shadow {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.moving-shadow::after {
    content: '';
    position: absolute;
    top: 10px;
    left: 10px;
    right: -10px;
    bottom: -10px;
    background-color: rgba(0,0,0,0.1);