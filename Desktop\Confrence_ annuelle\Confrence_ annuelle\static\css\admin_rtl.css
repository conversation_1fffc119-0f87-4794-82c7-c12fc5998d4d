/* تنسيقات خاصة لواجهة الإدارة باللغة العربية */

/* تحسين اتجاه النص للغة العربية */
.rtl #header,
.rtl #content,
.rtl #footer {
    direction: rtl;
}

/* تصحيح محاذاة العناصر في واجهة الإدارة */
.rtl .object-tools {
    float: left;
}

.rtl #changelist-filter {
    right: auto;
    left: 0;
}

.rtl .change-list .filtered .results,
.rtl .change-list .filtered .paginator,
.rtl .filtered #toolbar,
.rtl .filtered div.xfull {
    margin-right: 0;
    margin-left: 240px;
}

/* تحسين أزرار الإجراءات */
.rtl .object-tools li {
    float: right;
    margin-left: 5px;
    margin-right: 0;
}

/* تحسين الجداول */
.rtl table thead th.sorted .sortoptions {
    float: left;
}

/* تحسين النماذج */
.rtl .aligned label {
    padding: 0 0 3px 10px;
    float: right;
}

.rtl .aligned .checkbox-row label {
    padding-right: 0;
    padding-left: 10px;
}

.rtl form .aligned p.help,
.rtl form .aligned div.help {
    padding-left: 0;
    padding-right: 160px;
}

.rtl form .aligned ul {
    margin-right: 160px;
    margin-left: 0;
}

.rtl.login .form-row label {
    float: right;
    padding-left: 0.5em;
    padding-right: 0;
}

/* تحسين القوائم المنسدلة */
.rtl .selector {
    float: right;
}

.rtl .selector-available,
.rtl .selector-chosen {
    float: right;
}

.rtl .selector .selector-available,
.rtl .selector .selector-chosen {
    float: right;
}

.rtl .selector ul.selector-chooser {
    float: right;
}

/* تحسين الفلاتر */
.rtl #changelist-filter h2 {
    padding: 5px 15px 5px 0;
}

.rtl #changelist-filter h3 {
    margin: 0 15px 0 0;
}

.rtl #changelist-filter ul {
    margin-right: 0;
    padding-right: 15px;
}

/* تحسين الرسائل */
.rtl ul.messagelist li {
    padding: 8px 30px 8px 8px;
    background-position: 99% 8px;
}

/* تحسين التنقل */
.rtl .breadcrumbs {
    padding-right: 15px;
    padding-left: 0;
    text-align: right;
}

/* تحسين الأيقونات */
.rtl .addlink,
.rtl .changelink,
.rtl .deletelink {
    padding-left: 0;
    padding-right: 16px;
    background-position: 100% 1px;
}

/* تحسين الهوامش والحشوات */
.rtl .module h2,
.rtl .module caption,
.rtl .inline-group h2 {
    padding: 8px 12px 8px 0;
}

/* تحسين الأزرار */
.rtl .submit-row {
    text-align: left;
}

.rtl .submit-row input.default {
    margin-left: 0;
    margin-right: 8px;
    float: left;
}

.rtl .submit-row p.deletelink-box {
    float: right;
}

/* تحسين الخط */
.rtl #header,
.rtl #content,
.rtl #footer,
.rtl .module h2,
.rtl .module caption,
.rtl .inline-group h2,
.rtl #changelist-filter h2,
.rtl #changelist-filter h3,
.rtl #changelist .paginator,
.rtl .breadcrumbs,
.rtl .object-tools,
.rtl .aligned label,
.rtl .submit-row,
.rtl ul.messagelist li,
.rtl .selector,
.rtl .selector-available h2,
.rtl .selector-chosen h2 {
    font-family: 'Tajawal', sans-serif;
}