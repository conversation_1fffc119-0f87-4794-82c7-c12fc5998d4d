# Generated by Django 5.2.4 on 2025-07-05 16:54

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('organizations', '0010_formerminister'),
    ]

    operations = [
        migrations.CreateModel(
            name='DiplomaticCorps',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='الاسم الكامل')),
                ('rank', models.Char<PERSON><PERSON>(choices=[('ambassador', 'سفير'), ('consul_general', 'قنصل عام'), ('consul', 'قنصل'), ('first_secretary', 'سكرتير أول'), ('second_secretary', 'سكرتير ثاني'), ('third_secretary', 'سكرتير ثالث'), ('attache', 'ملحق'), ('counselor', 'مستشار'), ('minister_plenipotentiary', 'وزير مفوض')], max_length=30, verbose_name='الرتبة الدبلوماسية')),
                ('country', models.CharField(max_length=100, verbose_name='البلد/المهمة')),
                ('mission_type', models.CharField(choices=[('embassy', 'سفارة'), ('consulate', 'قنصلية'), ('permanent_mission', 'بعثة دائمة'), ('international_org', 'منظمة دولية'), ('ministry', 'وزارة الخارجية')], max_length=20, verbose_name='نوع المهمة')),
                ('start_date', models.DateField(verbose_name='تاريخ بداية المهمة')),
                ('end_date', models.DateField(blank=True, null=True, verbose_name='تاريخ نهاية المهمة')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('current_position', models.CharField(blank=True, max_length=200, null=True, verbose_name='المنصب الحالي')),
                ('status', models.CharField(choices=[('active', 'نشط'), ('inactive', 'غير نشط'), ('retired', 'متقاعد'), ('transferred', 'منقول')], default='active', max_length=20, verbose_name='الحالة')),
                ('achievements', models.TextField(blank=True, null=True, verbose_name='الإنجازات الدبلوماسية')),
                ('languages', models.CharField(blank=True, max_length=200, null=True, verbose_name='اللغات')),
                ('education', models.TextField(blank=True, null=True, verbose_name='المؤهلات العلمية')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإضافة')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'عضو السلك الدبلوماسي',
                'verbose_name_plural': 'السلك الدبلوماسي',
                'ordering': ['-start_date', 'name'],
            },
        ),
    ]
