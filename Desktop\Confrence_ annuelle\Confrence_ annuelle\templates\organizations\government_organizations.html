{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "المؤسسات الحكومية" %}{% endblock %}

{% block extra_css %}
<style>
    .hero-section {
        background: linear-gradient(135deg, #00796b 0%, #004d40 100%);
        padding: 80px 0;
        color: white;
        text-align: center;
        position: relative;
        overflow: hidden;
        z-index: 1;
    }
    
    /* تحسين دعم اللغة العربية في صفحة المؤسسات */
    [dir="rtl"] .card-body {
        text-align: right;
        direction: rtl;
    }
    
    [dir="rtl"] .card-title {
        text-align: right;
        direction: rtl;
    }
    
    [dir="rtl"] .org-info p {
        text-align: right;
        direction: rtl;
    }
    
    [dir="rtl"] .info-label {
        text-align: right;
        direction: rtl;
    }
    
    /* تحسين اتجاه الأزرار والروابط */
    [dir="rtl"] .back-link {
        text-align: right;
        direction: rtl;
    }
    
    [dir="rtl"] .back-link:hover {
        transform: translateX(-5px);
    }
    
    [dir="rtl"] .btn i {
        margin-right: 0;
        margin-left: 5px;
    }
    
    [dir="rtl"] .section-header {
        text-align: center;
        direction: rtl;
    }
    
    [dir="rtl"] .lead {
        text-align: center;
        direction: rtl;
    }

    .organization-card {
        border-radius: 15px;
        overflow: hidden;
        margin-bottom: 30px;


        
        transition: all 0.3s ease;
        position: relative;
        background-color: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 32px rgba(0, 121, 107, 0.1);
    }

    .organization-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0, 121, 107, 0.2);
    }

    .org-info {
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px dashed #e0e0e0;
    }

    .org-info:last-child {
        border-bottom: none;
    }

    .org-info h5 {
        color: #00796b;
        font-weight: 600;
        margin-bottom: 10px;
    }

    .org-info p {
        margin-bottom: 5px;
    }

    .info-label {
        font-weight: 600;
        color: #004d40;
    }

    .bg-decoration {
        position: absolute;
        opacity: 0.1;
        z-index: 0;
    }

    .bg-circle-1 {
        width: 300px;
        height: 300px;
        border-radius: 50%;
        background: #00796b;
        top: -100px;
        right: -100px;
        animation: float 8s ease-in-out infinite;
    }

    .bg-circle-2 {
        width: 200px;
        height: 200px;
        border-radius: 50%;
        background: #004d40;
        bottom: -50px;
        left: 10%;
        animation: float 10s ease-in-out infinite reverse;
    }

    .bg-wave {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 50px;
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%23ffffff' fill-opacity='1' d='M0,96L48,112C96,128,192,160,288,160C384,160,480,128,576,112C672,96,768,96,864,112C960,128,1056,160,1152,160C1248,160,1344,128,1392,112L1440,96L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z'%3E%3C/path%3E%3C/svg%3E");
        background-size: cover;
    }

    .back-link {
        display: inline-block;
        margin-top: 20px;
        color: white;
        text-decoration: none;
        transition: all 0.3s ease;
    }

    .back-link:hover {
        transform: translateX(5px);
        color: rgba(255, 255, 255, 0.8);
    }
</style>
{% endblock %}

{% block content %}
<div class="hero-section">
    <div class="container">
        <h1 class="display-4">{% trans "المؤسسات الحكومية" %}</h1>
        <p class="lead">{% trans "استعراض جميع المؤسسات الحكومية المشاركة في المؤتمر" %}</p>
        <div class="mt-4">
            {% if LANGUAGE_CODE == 'ar' %}
            <a href="{% url 'organizations:organization_list' %}" class="back-link mr-3">{% trans "العودة إلى قائمة المؤسسات" %} <i class="fas fa-arrow-left ml-2"></i></a>
            <a href="{% url 'organizations:organization_create' %}" class="btn btn-primary me-2">{% trans "إضافة مؤسسة جديدة" %} <i class="fas fa-plus mr-2"></i></a>
            <a href="{% url 'organizations:government_invitations' %}" class="btn btn-success">{% trans "إرسال دعوات جماعية" %} <i class="fas fa-envelope mr-2"></i></a>
            {% else %}
            <a href="{% url 'organizations:organization_list' %}" class="back-link mr-3"><i class="fas fa-arrow-right ml-2"></i> {% trans "العودة إلى قائمة المؤسسات" %}</a>
            <a href="{% url 'organizations:organization_create' %}" class="btn btn-primary me-2"><i class="fas fa-plus mr-2"></i> {% trans "إضافة مؤسسة جديدة" %}</a>
            <a href="{% url 'organizations:government_invitations' %}" class="btn btn-success"><i class="fas fa-envelope mr-2"></i> {% trans "إرسال دعوات جماعية" %}</a>
            {% endif %}
        </div>
    </div>
    <div class="bg-decoration bg-circle-1"></div>
    <div class="bg-decoration bg-circle-2"></div>
    <div class="bg-decoration bg-decoration-3"></div>
    <div class="bg-decoration bg-decoration-4"></div>
    <div class="bg-wave"></div>
</div>

<!-- Background Decorations -->
<div class="bg-decoration bg-decoration-1"></div>
<div class="bg-decoration bg-decoration-2"></div>
<div class="bg-decoration bg-decoration-3"></div>
<div class="bg-decoration bg-decoration-4"></div>

<!-- Organizations Section -->
<div class="container mb-5">
    <div class="row">
        <!-- Government Organizations -->
        <div class="col-12">
            <div class="section-header mb-4 text-center">
                <h2 class="text-primary"><i class="fas fa-landmark me-2 icon-pulse"></i> {% trans "المؤسسات الحكومية" %}</h2>
                <p class="text-muted">{% trans "المؤسسات الحكومية المشاركة في المؤتمر" %}</p>
            </div>

            <!-- Organizations List -->
            {% if organizations %}
            <div class="row row-cols-1 row-cols-md-2 g-4">
                {% for organization in organizations %}
                <div class="col">
                    <div class="card organization-card h-100 shadow-hover multi-layer-shadow">
                        <div class="card-body">
                            <div class="text-center mb-3">
                                {% if organization.logo %}
                                <img src="{{ organization.logo.url }}" alt="{{ organization.name }}" class="organization-logo">
                                {% else %}
                                <i class="fas fa-building fa-3x text-primary"></i>
                                {% endif %}
                            </div>
                            <h4 class="card-title text-center mb-4">
                                {% if organization.name_fr %}{{ organization.name_fr }}{% else %}{{ organization.name }}{% endif %}
                            </h4>

                            <div class="org-info">
                                {% if organization.address %}
                                <p><span class="info-label">{% trans "العنوان:" %}</span> {{ organization.address }}</p>
                                {% endif %}
                                {% if organization.contact_person %}
                                <p><span class="info-label">{% trans "الشخص المسؤول:" %}</span> {{ organization.contact_person }}</p>
                                {% endif %}
                                {% if organization.website %}
                                <p><span class="info-label">{% trans "الموقع الإلكتروني:" %}</span> <a href="{{ organization.website }}" target="_blank">{{ organization.website }}</a></p>
                                {% endif %}
                                {% if organization.email %}
                                <p><span class="info-label">{% trans "البريد الإلكتروني:" %}</span> {{ organization.email }}</p>
                                {% endif %}
                                {% if organization.phone %}
                                <p><span class="info-label">{% trans "رقم الهاتف:" %}</span> {{ organization.phone }}</p>
                                {% endif %}
                            </div>

                            {% if organization.description %}
                            <div class="org-info">
                                <h5>{% trans "الوصف:" %}</h5>
                                <p>{{ organization.description|truncatewords:20 }}</p>
                            </div>
                            {% endif %}

                            <div class="mt-3 d-flex flex-wrap justify-content-center gap-2">
                                <a href="{% url 'organizations:organization_detail' organization.pk %}" class="btn btn-primary btn-sm btn-action btn-3d btn-hover-expand" data-bs-toggle="tooltip" data-bs-placement="top" title="{% trans "عرض تفاصيل المؤسسة" %}">
                                    <i class="fas fa-eye me-1 icon-float"></i> {% trans "عرض التفاصيل" %}
                                </a>

                                {% if organization.website %}
                                <a href="{{ organization.website }}" target="_blank" class="btn btn-info btn-sm btn-action btn-3d btn-hover-expand" data-bs-toggle="tooltip" data-bs-placement="top" title="{% trans "زيارة الموقع الرسمي" %}">
                                    <i class="fas fa-globe me-1 icon-float"></i> {% trans "زيارة الموقع" %}
                                </a>
                                {% endif %}

                                <a href="{% url 'organizations:contact_organization' organization.pk %}" class="btn btn-success btn-sm btn-action btn-3d btn-hover-expand" data-bs-toggle="tooltip" data-bs-placement="top" title="{% trans "التواصل مع المؤسسة" %}">
                                    <i class="fas fa-envelope me-1 icon-float"></i> {% trans "اتصل بنا" %}
                                </a>

                                {% if user.is_staff %}
                                <a href="{% url 'organizations:organization_update' organization.pk %}" class="btn btn-warning btn-sm btn-action btn-3d btn-hover-expand" data-bs-toggle="tooltip" data-bs-placement="top" title="{% trans "تعديل المؤسسة" %}">
                                    <i class="fas fa-edit me-1 icon-float"></i> {% trans "تعديل" %}
                                </a>

                                <a href="{% url 'organizations:organization_delete' organization.pk %}" class="btn btn-danger btn-sm btn-action btn-3d btn-hover-expand" data-bs-toggle="tooltip" data-bs-placement="top" title="{% trans "حذف المؤسسة" %}" onclick="return confirm('{% trans "هل أنت متأكد من حذف هذه المؤسسة؟" %}')">
                                    <i class="fas fa-trash me-1 icon-float"></i> {% trans "حذف" %}
                                </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="alert alert-info glass-effect multi-layer-shadow">
                <i class="fas fa-info-circle me-2 pulse-element"></i> {% trans "لا توجد مؤسسات حكومية مسجلة حتى الآن." %}
            </div>
            {% endif %}

        </div>
    </div>
</div>



{% endblock %}

{% block extra_js %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
<script src="{% static 'js/organization_effects.js' %}"></script>
{% endblock %}