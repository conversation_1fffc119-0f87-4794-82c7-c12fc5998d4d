from django.shortcuts import render, redirect, get_object_or_404
from django.utils.translation import gettext as _
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib import messages
from .models import Conference, ConferenceEdition, Notification
from organizations.models import Organization, PartyLeader

def home(request):
    """Home page view"""
    active_conference = Conference.objects.filter(is_active=True).first()
    active_edition = None
    if active_conference:
        active_edition = ConferenceEdition.objects.filter(
            conference=active_conference,
            is_active=True
        ).order_by('-year').first()
    
    context = {
        'title': _('Home'),
        'active_conference': active_conference,
        'active_edition': active_edition,
        'organization_count': Organization.objects.count(),
    }
    return render(request, 'core/home.html', context)

def about(request):
    """About page view"""
    active_conference = Conference.objects.filter(is_active=True).first()
    editions = []
    if active_conference:
        editions = ConferenceEdition.objects.filter(
            conference=active_conference
        ).order_by('-year')
    
    context = {
        'title': _('About'),
        'active_conference': active_conference,
        'editions': editions,
    }
    return render(request, 'core/about.html', context)

def contact(request):
    """Contact page view with email functionality"""
    if request.method == 'POST':
        name = request.POST.get('name')
        email = request.POST.get('email')
        subject = request.POST.get('subject')
        message = request.POST.get('message')
        
        # Validate form data
        if name and email and subject and message:
            # Send email
            from django.core.mail import send_mail
            from django.conf import settings
            
            email_subject = f"[اتصال من الموقع] {subject}"
            email_message = f"اسم المرسل: {name}\nالبريد الإلكتروني: {email}\n\nالرسالة:\n{message}"
            
            try:
                send_mail(
                    email_subject,
                    email_message,
                    settings.DEFAULT_FROM_EMAIL,
                    ['<EMAIL>'],  # البريد الإلكتروني المحدد
                    fail_silently=False,
                )
                messages.success(request, _('تم إرسال رسالتك بنجاح. سنتواصل معك قريباً.'))
                return redirect('core:contact')
            except Exception as e:
                messages.error(request, _('حدث خطأ أثناء إرسال الرسالة. يرجى المحاولة مرة أخرى لاحقاً.'))
        else:
            messages.error(request, _('يرجى ملء جميع الحقول المطلوبة.'))
    
    # Get active conference for additional context
    active_conference = Conference.objects.filter(is_active=True).first()
    
    context = {
        'title': _('Contact'),
        'active_conference': active_conference,
    }
    return render(request, 'core/contact.html', context)

@login_required
def notification_list(request):
    """List all notifications for the current user"""
    notifications = Notification.objects.filter(user=request.user).order_by('-created_at')
    
    # Mark all as read if requested
    if request.GET.get('mark_all_read'):
        notifications.update(is_read=True)
        messages.success(request, _('All notifications marked as read.'))
        return redirect('core:notification_list')
    
    # Filter by read status if requested
    status = request.GET.get('status')
    if status == 'unread':
        notifications = notifications.filter(is_read=False)
    elif status == 'read':
        notifications = notifications.filter(is_read=True)
    
    context = {
        'title': _('Notifications'),
        'notifications': notifications,
        'unread_count': Notification.objects.filter(user=request.user, is_read=False).count(),
    }
    return render(request, 'core/notification_list.html', context)

@login_required
def mark_notification_as_read(request, pk):
    """Mark a notification as read"""
    notification = get_object_or_404(Notification, pk=pk, user=request.user)
    notification.mark_as_read()
    
    # Redirect to related object if available
    if notification.related_object_type == 'invitation' and notification.related_object_id:
        return redirect('organizations:invitation_detail', pk=notification.related_object_id)
    
    messages.success(request, _('Notification marked as read.'))
    return redirect('core:notification_list')

@login_required
@user_passes_test(lambda u: u.is_staff)
def admin_dashboard(request):
    """Custom admin dashboard for staff users"""
    # Get statistics for the dashboard
    total_organizations = Organization.objects.count()
    total_party_leaders = PartyLeader.objects.count()
    active_conference = Conference.objects.filter(is_active=True).first()
    total_editions = ConferenceEdition.objects.count()

    # Get recent notifications
    recent_notifications = Notification.objects.order_by('-created_at')[:10]

    context = {
        'title': _('لوحة الإدارة'),
        'total_organizations': total_organizations,
        'total_party_leaders': total_party_leaders,
        'active_conference': active_conference,
        'total_editions': total_editions,
        'recent_notifications': recent_notifications,
    }
    return render(request, 'core/admin_dashboard.html', context)
