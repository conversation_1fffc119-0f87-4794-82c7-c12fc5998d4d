{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .form-section {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        padding: 25px;
        margin-bottom: 25px;
    }
    
    .form-section h5 {
        color: #667eea;
        font-weight: bold;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #f0f0f0;
    }
    
    .form-section h5 i {
        margin-right: 10px;
    }
    
    .form-control, .form-select {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        padding: 12px 15px;
        transition: all 0.3s ease;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .btn-3d {
        border-radius: 10px;
        padding: 12px 30px;
        font-weight: bold;
        text-transform: uppercase;
        letter-spacing: 1px;
        transition: all 0.3s ease;
        border: none;
        position: relative;
        overflow: hidden;
    }
    
    .btn-3d:before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }
    
    .btn-3d:hover:before {
        left: 100%;
    }
    
    .btn-primary.btn-3d {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    
    .btn-secondary.btn-3d {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        color: white;
    }
    
    .required-field {
        color: #dc3545;
    }

    .form-help {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 5px;
    }

    /* تنسيق معاينة الصورة */
    .image-preview-container {
        text-align: center;
        padding: 20px;
        border: 2px dashed #e9ecef;
        border-radius: 10px;
        background-color: #f8f9fa;
    }

    .current-image {
        max-width: 150px;
        max-height: 150px;
        border-radius: 10px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    .no-image-placeholder {
        color: #6c757d;
    }

    .no-image-placeholder i {
        font-size: 4rem;
        margin-bottom: 10px;
    }

    /* تحسين حقل رفع الملف */
    input[type="file"] {
        border: 2px dashed #667eea !important;
        background-color: #f8f9fc;
        padding: 20px !important;
        text-align: center;
    }

    input[type="file"]:hover {
        border-color: #5a67d8 !important;
        background-color: #f0f2ff;
    }

    /* تحسين معاينة الصورة المرفوعة */
    .image-preview {
        max-width: 200px;
        max-height: 200px;
        border-radius: 10px;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        margin-top: 10px;
        display: none;
    }

    .upload-area {
        position: relative;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .upload-area:hover {
        transform: translateY(-2px);
    }

    .drag-drop-text {
        pointer-events: none;
        color: #667eea;
        font-weight: 500;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="section-header mb-4 text-center">
                <h2 class="text-primary"><i class="fas fa-user-cog me-2"></i> {{ title }}</h2>
                {% if admin_profile %}
                    <p class="text-muted">تعديل بيانات المسؤول: {{ admin_profile.get_display_name }}</p>
                {% else %}
                    <p class="text-muted">إنشاء حساب مسؤول جديد في النظام</p>
                {% endif %}
            </div>

            <form method="post" enctype="multipart/form-data" novalidate>
                {% csrf_token %}
                
                <!-- معلومات الحساب -->
                <div class="form-section">
                    <h5><i class="fas fa-user"></i> معلومات الحساب</h5>
                    
                    <div class="row">
                        {% if not admin_profile %}
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">اسم المستخدم <span class="required-field">*</span></label>
                                {{ form.username }}
                                {% if form.username.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.username.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-help">اسم المستخدم للدخول إلى النظام</div>
                            </div>
                        </div>
                        {% endif %}
                        
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">البريد الإلكتروني</label>
                                {{ form.email }}
                                {% if form.email.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.email.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    {% if not admin_profile %}
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">كلمة المرور <span class="required-field">*</span></label>
                                {{ form.password }}
                                {% if form.password.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.password.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">تأكيد كلمة المرور <span class="required-field">*</span></label>
                                {{ form.password_confirm }}
                                {% if form.password_confirm.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.password_confirm.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>

                <!-- المعلومات الشخصية -->
                <div class="form-section">
                    <h5><i class="fas fa-id-card"></i> المعلومات الشخصية</h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الاسم الأول</label>
                                {{ form.first_name }}
                                {% if form.first_name.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.first_name.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">اسم العائلة</label>
                                {{ form.last_name }}
                                {% if form.last_name.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.last_name.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-8">
                            <div class="mb-3">
                                <label class="form-label">الاسم الكامل</label>
                                {{ form.full_name }}
                                {% if form.full_name.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.full_name.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-help">الاسم الكامل كما سيظهر في النظام</div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label class="form-label">رقم الهاتف</label>
                                {{ form.phone }}
                                {% if form.phone.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.phone.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- الصورة الشخصية -->
                <div class="form-section">
                    <h5><i class="fas fa-camera"></i> الصورة الشخصية</h5>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">رفع صورة شخصية</label>
                                {{ form.profile_image }}
                                {% if form.profile_image.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.profile_image.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-help">صورة شخصية للمسؤول (اختيارية) - يُفضل أن تكون مربعة الشكل</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">معاينة الصورة</label>
                                <div class="image-preview-container upload-area">
                                    {% if admin_profile and admin_profile.profile_image %}
                                        <img src="{{ admin_profile.profile_image.url }}" alt="الصورة الحالية" class="current-image">
                                        <p class="text-muted mt-2">الصورة الحالية</p>
                                        <p class="drag-drop-text"><small>اضغط أو اسحب صورة جديدة هنا</small></p>
                                    {% else %}
                                        <div class="no-image-placeholder">
                                            <i class="fas fa-cloud-upload-alt"></i>
                                            <p class="text-muted mt-2">اضغط أو اسحب الصورة هنا</p>
                                            <p class="drag-drop-text"><small>JPG, PNG, GIF - حد أقصى 5MB</small></p>
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- معلومات الصلاحيات -->
                <div class="form-section">
                    <h5><i class="fas fa-shield-alt"></i> معلومات الصلاحيات</h5>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">نوع المسؤول <span class="required-field">*</span></label>
                                {{ form.admin_type }}
                                {% if form.admin_type.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.admin_type.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-help">يحدد مستوى الصلاحيات في النظام</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">القسم</label>
                                {{ form.department }}
                                {% if form.department.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.department.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    
                    {% if admin_profile %}
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <div class="form-check">
                                    {{ form.is_active }}
                                    <label class="form-check-label" for="{{ form.is_active.id_for_label }}">
                                        الحساب نشط
                                    </label>
                                </div>
                                {% if form.is_active.errors %}
                                    <div class="text-danger mt-1">
                                        {% for error in form.is_active.errors %}
                                            <small>{{ error }}</small>
                                        {% endfor %}
                                    </div>
                                {% endif %}
                                <div class="form-help">إلغاء التفعيل يمنع المسؤول من الدخول إلى النظام</div>
                            </div>
                        </div>
                    </div>
                    {% endif %}
                </div>

                <!-- ملاحظات إضافية -->
                <div class="form-section">
                    <h5><i class="fas fa-sticky-note"></i> ملاحظات إضافية</h5>
                    
                    <div class="mb-3">
                        <label class="form-label">ملاحظات</label>
                        {{ form.notes }}
                        {% if form.notes.errors %}
                            <div class="text-danger mt-1">
                                {% for error in form.notes.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                        <div class="form-help">ملاحظات إضافية حول المسؤول</div>
                    </div>
                </div>

                <!-- أزرار الحفظ -->
                <div class="text-center">
                    <button type="submit" class="btn btn-primary btn-3d me-3">
                        <i class="fas fa-save me-2"></i>{{ submit_text }}
                    </button>
                    <a href="{% url 'core:admin_list' %}" class="btn btn-secondary btn-3d">
                        <i class="fas fa-times me-2"></i>إلغاء
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // معاينة الصورة قبل الرفع
    const imageInput = document.querySelector('input[type="file"][name="profile_image"]');
    const imagePreviewContainer = document.querySelector('.image-preview-container');

    if (imageInput) {
        imageInput.addEventListener('change', function(e) {
            const file = e.target.files[0];

            if (file) {
                // التحقق من نوع الملف
                if (!file.type.startsWith('image/')) {
                    alert('يرجى اختيار ملف صورة صحيح');
                    return;
                }

                // التحقق من حجم الملف (5MB max)
                if (file.size > 5 * 1024 * 1024) {
                    alert('حجم الصورة كبير جداً. يرجى اختيار صورة أصغر من 5 ميجابايت');
                    return;
                }

                const reader = new FileReader();
                reader.onload = function(e) {
                    // إنشاء عنصر الصورة الجديد
                    const newImageHtml = `
                        <img src="${e.target.result}" alt="معاينة الصورة" class="current-image">
                        <p class="text-muted mt-2">معاينة الصورة الجديدة</p>
                        <button type="button" class="btn btn-sm btn-outline-danger mt-2" onclick="clearImagePreview()">
                            <i class="fas fa-times me-1"></i>إزالة
                        </button>
                    `;

                    imagePreviewContainer.innerHTML = newImageHtml;
                };
                reader.readAsDataURL(file);
            }
        });
    }
});

function clearImagePreview() {
    const imageInput = document.querySelector('input[type="file"][name="profile_image"]');
    const imagePreviewContainer = document.querySelector('.image-preview-container');

    // مسح قيمة الإدخال
    imageInput.value = '';

    // إعادة المحتوى الأصلي
    const originalContent = `
        <div class="no-image-placeholder">
            <i class="fas fa-user-circle"></i>
            <p class="text-muted mt-2">لا توجد صورة</p>
        </div>
    `;

    imagePreviewContainer.innerHTML = originalContent;
}

// تحسين تجربة السحب والإفلات
document.addEventListener('DOMContentLoaded', function() {
    const uploadArea = document.querySelector('.image-preview-container');
    const fileInput = document.querySelector('input[type="file"][name="profile_image"]');

    if (uploadArea && fileInput) {
        // منع السلوك الافتراضي للسحب والإفلات
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, preventDefaults, false);
        });

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        // تمييز المنطقة عند السحب
        ['dragenter', 'dragover'].forEach(eventName => {
            uploadArea.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            uploadArea.addEventListener(eventName, unhighlight, false);
        });

        function highlight(e) {
            uploadArea.style.borderColor = '#667eea';
            uploadArea.style.backgroundColor = '#f0f2ff';
        }

        function unhighlight(e) {
            uploadArea.style.borderColor = '#e9ecef';
            uploadArea.style.backgroundColor = '#f8f9fa';
        }

        // معالجة الإفلات
        uploadArea.addEventListener('drop', handleDrop, false);

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;

            if (files.length > 0) {
                fileInput.files = files;
                // تشغيل حدث التغيير
                const event = new Event('change', { bubbles: true });
                fileInput.dispatchEvent(event);
            }
        }

        // جعل المنطقة قابلة للنقر
        uploadArea.addEventListener('click', function() {
            fileInput.click();
        });

        uploadArea.style.cursor = 'pointer';
    }
});
</script>
{% endblock %}
