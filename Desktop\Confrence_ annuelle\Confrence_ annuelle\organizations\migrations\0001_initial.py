# Generated by Django 5.1.2 on 2025-05-23 08:02

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Organization',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, verbose_name='Name')),
                ('address', models.TextField(blank=True, null=True, verbose_name='Address')),
                ('email', models.EmailField(max_length=254, verbose_name='Email')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='Phone')),
                ('contact_person', models.CharField(blank=True, max_length=255, null=True, verbose_name='Contact Person')),
                ('website', models.URLField(blank=True, null=True, verbose_name='Website')),
                ('description', models.TextField(blank=True, null=True, verbose_name='Description')),
                ('participation_status', models.CharField(choices=[('invited', 'Invited'), ('confirmed', 'Confirmed'), ('declined', 'Declined'), ('attended', 'Attended')], default='invited', max_length=20, verbose_name='Participation Status')),
                ('logo', models.ImageField(blank=True, null=True, upload_to='organizations/logos/', verbose_name='Logo')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
            ],
            options={
                'verbose_name': 'Organization',
                'verbose_name_plural': 'Organizations',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Invitation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('subject', models.CharField(max_length=255, verbose_name='Subject')),
                ('message', models.TextField(verbose_name='Message')),
                ('sent_at', models.DateTimeField(auto_now_add=True, verbose_name='Sent At')),
                ('is_sent', models.BooleanField(default=False, verbose_name='Is Sent')),
                ('response_status', models.CharField(blank=True, max_length=20, null=True, verbose_name='Response Status')),
                ('response_date', models.DateTimeField(blank=True, null=True, verbose_name='Response Date')),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='invitations', to='organizations.organization')),
            ],
            options={
                'verbose_name': 'Invitation',
                'verbose_name_plural': 'Invitations',
                'ordering': ['-sent_at'],
            },
        ),
    ]
