<!DOCTYPE html>
<html>
<head>
    <title>اختبار الأزرار</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container mt-5">
        <h1>اختبار الأزرار - {{ title }}</h1>
        
        <div class="stats-bar bg-light p-3 rounded mb-4 d-flex justify-content-between">
            <div class="stats-info">
                <span class="me-3">إجمالي المسؤولين: {{ total_count }}</span>
                <span>الفئات: {{ officials_by_category|length }}</span>
            </div>
            
            <div class="action-buttons">
                <a href="{% url 'organizations:add_official' %}" class="btn btn-success me-2">
                    <i class="fas fa-plus me-1"></i>إضافة مسؤول
                </a>
                
                <div class="btn-group me-2" role="group">
                    <button type="button" class="btn btn-info dropdown-toggle" data-bs-toggle="dropdown">
                        <i class="fas fa-download me-1"></i>استيراد/تصدير
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="{% url 'organizations:export_officials' %}">
                            <i class="fas fa-file-excel me-2"></i>تصدير إلى Excel
                        </a></li>
                        <li><a class="dropdown-item" href="{% url 'organizations:download_officials_template' %}">
                            <i class="fas fa-download me-2"></i>تحميل قالب الاستيراد
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#importModal">
                            <i class="fas fa-upload me-2"></i>استيراد من Excel
                        </a></li>
                    </ul>
                </div>
                
                <button type="button" class="btn btn-danger" id="bulkDeleteBtn">
                    <i class="fas fa-trash me-1"></i>حذف المحدد
                </button>
            </div>
        </div>
        
        <div class="alert alert-info">
            هذه صفحة اختبار للتأكد من أن الأزرار تعمل بشكل صحيح.
        </div>
        
        {% if officials_by_category %}
            {% for category_name, officials in officials_by_category.items %}
                <div class="card mb-3">
                    <div class="card-header">
                        <h5>{{ category_name }} ({{ officials|length }})</h5>
                    </div>
                    <div class="card-body">
                        {% for official in officials %}
                            <div class="border p-2 mb-2 rounded">
                                <strong>{{ official.get_full_title }}</strong>
                                <div class="mt-1">
                                    <a href="{% url 'organizations:edit_official' official.id %}" class="btn btn-sm btn-outline-primary me-1">
                                        <i class="fas fa-edit"></i> تعديل
                                    </a>
                                    <a href="{% url 'organizations:delete_official' official.id %}" class="btn btn-sm btn-outline-danger">
                                        <i class="fas fa-trash"></i> حذف
                                    </a>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            {% endfor %}
        {% else %}
            <div class="alert alert-warning">لا توجد بيانات للعرض</div>
        {% endif %}
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
