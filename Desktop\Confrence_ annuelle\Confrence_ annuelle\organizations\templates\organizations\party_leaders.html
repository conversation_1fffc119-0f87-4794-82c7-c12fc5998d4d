{% extends 'base.html' %}
{% load static %}

{% block title %}رؤساء الأحزاب{% endblock %}

{% block extra_css %}
<style>
    .party-leader-card {
        border: 1px solid #e0e0e0;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        position: relative;
        overflow: hidden;
    }
    
    .party-leader-card::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.1);
        transform: rotate(45deg);
        transition: all 0.3s ease;
    }
    
    .party-leader-card:hover::before {
        top: -60%;
        right: -60%;
    }
    
    .party-leader-photo {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        object-fit: cover;
        border: 3px solid rgba(255, 255, 255, 0.3);
        margin-bottom: 15px;
    }
    
    .party-leader-name {
        font-size: 1.4rem;
        font-weight: bold;
        margin-bottom: 5px;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    }
    
    .party-name {
        font-size: 1.1rem;
        margin-bottom: 10px;
        opacity: 0.9;
    }
    
    .party-leader-title {
        background: rgba(255, 255, 255, 0.2);
        padding: 5px 15px;
        border-radius: 20px;
        font-size: 0.9rem;
        display: inline-block;
        margin-bottom: 15px;
    }
    
    .contact-info {
        font-size: 0.9rem;
        opacity: 0.8;
    }
    
    .contact-info i {
        margin-right: 8px;
        width: 15px;
    }
    
    .action-buttons {
        position: absolute;
        top: 15px;
        left: 15px;
        z-index: 10;
    }
    
    .btn-action {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 5px 10px;
        border-radius: 5px;
        font-size: 0.8rem;
        margin-right: 5px;
        transition: all 0.3s ease;
    }
    
    .btn-action:hover {
        background: rgba(255, 255, 255, 0.3);
        color: white;
        transform: translateY(-2px);
    }
    
    .status-badge {
        position: absolute;
        top: 15px;
        right: 15px;
        padding: 5px 12px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: bold;
    }
    
    .status-active { background: #28a745; }
    .status-inactive { background: #ffc107; color: #000; }
    .status-retired { background: #17a2b8; }
    .status-deceased { background: #dc3545; }
    
    .search-filters {
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        margin-bottom: 30px;
    }
    
    .stats-cards {
        margin-bottom: 30px;
    }
    
    .stat-card {
        background: white;
        padding: 20px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        text-align: center;
        border-left: 4px solid #667eea;
    }
    
    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        color: #667eea;
    }
    
    .stat-label {
        color: #666;
        font-size: 0.9rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-users-cog"></i> رؤساء الأحزاب</h2>
                <div>
                    <a href="{% url 'organizations:add_party_leader' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة رئيس حزب
                    </a>
                    <a href="{% url 'organizations:import_party_leaders' %}" class="btn btn-success">
                        <i class="fas fa-file-import"></i> استيراد
                    </a>
                    <a href="{% url 'organizations:export_party_leaders' %}" class="btn btn-info">
                        <i class="fas fa-file-export"></i> تصدير
                    </a>
                    <button class="btn btn-danger" id="bulkDeleteBtn" style="display: none;">
                        <i class="fas fa-trash"></i> حذف المحدد
                    </button>
                </div>
            </div>

            <!-- إحصائيات -->
            <div class="row stats-cards">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number">{{ total_count }}</div>
                        <div class="stat-label">إجمالي رؤساء الأحزاب</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-number">{{ party_leaders|length }}</div>
                        <div class="stat-label">المعروضون حالياً</div>
                    </div>
                </div>
            </div>

            <!-- البحث والفلترة -->
            <div class="search-filters">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label">البحث</label>
                        <input type="text" class="form-control" name="search" value="{{ search_query }}" 
                               placeholder="البحث في الاسم، الحزب، الهاتف، أو البريد الإلكتروني">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">الحالة</label>
                        <select class="form-select" name="status">
                            <option value="">جميع الحالات</option>
                            {% for value, label in status_choices %}
                                <option value="{{ value }}" {% if status_filter == value %}selected{% endif %}>{{ label }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">المنصب</label>
                        <select class="form-select" name="title">
                            <option value="">جميع المناصب</option>
                            {% for value, label in title_choices %}
                                <option value="{{ value }}" {% if title_filter == value %}selected{% endif %}>{{ label }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search"></i> بحث
                            </button>
                        </div>
                    </div>
                </form>
            </div>

            <!-- قائمة رؤساء الأحزاب -->
            <div class="row">
                {% for party_leader in party_leaders %}
                <div class="col-lg-4 col-md-6 mb-4">
                    <div class="party-leader-card">
                        <div class="action-buttons">
                            <input type="checkbox" class="party-leader-checkbox" value="{{ party_leader.id }}" style="margin-right: 10px;">
                            <a href="{% url 'organizations:edit_party_leader' party_leader.pk %}" class="btn-action">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a href="{% url 'organizations:delete_party_leader' party_leader.pk %}" class="btn-action">
                                <i class="fas fa-trash"></i>
                            </a>
                        </div>
                        
                        <div class="status-badge status-{{ party_leader.status }}">
                            {{ party_leader.get_status_display }}
                        </div>
                        
                        <div class="text-center">
                            {% if party_leader.photo %}
                                <img src="{{ party_leader.photo.url }}" alt="{{ party_leader.name }}" class="party-leader-photo">
                            {% else %}
                                <div class="party-leader-photo d-flex align-items-center justify-content-center" style="background: rgba(255,255,255,0.2);">
                                    <i class="fas fa-user fa-2x"></i>
                                </div>
                            {% endif %}
                            
                            <div class="party-leader-name">{{ party_leader.name }}</div>
                            <div class="party-name">{{ party_leader.party_name }}</div>
                            <div class="party-leader-title">{{ party_leader.get_title_display }}</div>
                            
                            <div class="contact-info text-start">
                                {% if party_leader.phone %}
                                    <div class="mb-1">
                                        <i class="fas fa-phone"></i> {{ party_leader.phone }}
                                    </div>
                                {% endif %}
                                {% if party_leader.email %}
                                    <div class="mb-1">
                                        <i class="fas fa-envelope"></i> {{ party_leader.email }}
                                    </div>
                                {% endif %}
                                {% if party_leader.party_founded_year %}
                                    <div class="mb-1">
                                        <i class="fas fa-calendar"></i> تأسس عام {{ party_leader.party_founded_year }}
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fas fa-users-cog fa-5x text-muted mb-3"></i>
                        <h4 class="text-muted">لا توجد بيانات رؤساء أحزاب</h4>
                        <p class="text-muted">يمكنك إضافة رئيس حزب جديد أو استيراد البيانات من ملف Excel</p>
                        <a href="{% url 'organizations:add_party_leader' %}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> إضافة رئيس حزب
                        </a>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const checkboxes = document.querySelectorAll('.party-leader-checkbox');
    const bulkDeleteBtn = document.getElementById('bulkDeleteBtn');
    
    // إظهار/إخفاء زر الحذف المتعدد
    function toggleBulkDeleteBtn() {
        const checkedBoxes = document.querySelectorAll('.party-leader-checkbox:checked');
        bulkDeleteBtn.style.display = checkedBoxes.length > 0 ? 'inline-block' : 'none';
    }
    
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', toggleBulkDeleteBtn);
    });
    
    // الحذف المتعدد
    bulkDeleteBtn.addEventListener('click', function() {
        const checkedBoxes = document.querySelectorAll('.party-leader-checkbox:checked');
        const ids = Array.from(checkedBoxes).map(cb => cb.value);
        
        if (ids.length === 0) {
            alert('يرجى تحديد رؤساء الأحزاب المراد حذفهم');
            return;
        }
        
        if (confirm(`هل أنت متأكد من حذف ${ids.length} رئيس حزب؟`)) {
            fetch('{% url "organizations:bulk_delete_party_leaders" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': '{{ csrf_token }}'
                },
                body: JSON.stringify({party_leader_ids: ids})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('حدث خطأ: ' + data.message);
                }
            });
        }
    });
});
</script>
{% endblock %}
