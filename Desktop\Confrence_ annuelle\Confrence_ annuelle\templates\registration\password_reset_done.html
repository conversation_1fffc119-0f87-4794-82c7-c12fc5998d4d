{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card glass-effect fade-in-element">
            <div class="card-header">
                <h3 class="neon-text text-center">{% trans "تم إرسال بريد إعادة تعيين كلمة المرور" %}</h3>
            </div>
            <div class="card-body">
                <div class="alert alert-success glass-effect animate__animated animate__fadeInDown">
                    <i class="fas fa-check-circle icon-float"></i> {% trans "تم إرسال تعليمات إعادة تعيين كلمة المرور إلى بريدك الإلكتروني" %}
                </div>
                
                <p class="card-text">{% trans "لقد أرسلنا لك تعليمات حول كيفية تعيين كلمة المرور الخاصة بك، إذا كان هناك حساب بالبريد الإلكتروني الذي أدخلته. سوف تتلقى الرسالة قريبًا." %}</p>
                
                <p class="card-text">{% trans "إذا لم تتلق بريدًا إلكترونيًا، فيرجى التأكد من أنك أدخلت العنوان الذي سجلت به، وتحقق من مجلد البريد العشوائي." %}</p>
                
                <div class="d-grid gap-2 mt-4">
                    <a href="{% url 'login' %}" class="btn btn-3d btn-primary">{% trans "العودة إلى تسجيل الدخول" %}</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}