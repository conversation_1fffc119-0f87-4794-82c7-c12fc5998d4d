# Generated by Django 5.2.4 on 2025-07-05 16:35

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('organizations', '0009_electedofficial'),
    ]

    operations = [
        migrations.CreateModel(
            name='FormerMinister',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='الاسم الكامل')),
                ('ministry', models.CharField(max_length=200, verbose_name='الوزارة')),
                ('government', models.CharField(blank=True, max_length=200, null=True, verbose_name='الحكومة')),
                ('start_date', models.DateField(verbose_name='تاريخ بداية المنصب')),
                ('end_date', models.DateField(blank=True, null=True, verbose_name='تاريخ نهاية المنصب')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('current_position', models.CharField(blank=True, max_length=200, null=True, verbose_name='المنصب الحالي')),
                ('status', models.CharField(choices=[('active', 'نشط'), ('inactive', 'غير نشط'), ('deceased', 'متوفى')], default='active', max_length=20, verbose_name='الحالة')),
                ('achievements', models.TextField(blank=True, null=True, verbose_name='الإنجازات')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإضافة')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'وزير سابق',
                'verbose_name_plural': 'الوزراء السابقون',
                'ordering': ['-start_date', 'name'],
            },
        ),
    ]
