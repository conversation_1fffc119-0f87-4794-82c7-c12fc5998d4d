# Generated by Django 5.1.2 on 2025-07-15 14:10

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('organizations', '0013_diplomaticcorps_created_by_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PartyLeader',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, verbose_name='الاسم الكامل')),
                ('party_name', models.CharField(max_length=255, verbose_name='اسم الحزب')),
                ('title', models.CharField(choices=[('president', 'رئيس الحزب'), ('chairperson', 'رئيسة الحزب'), ('secretary_general', 'الأمين العام'), ('leader', 'زعيم الحزب'), ('founder', 'مؤسس الحزب'), ('other', 'أخرى')], default='president', max_length=50, verbose_name='المنصب')),
                ('phone', models.CharField(blank=True, max_length=20, verbose_name='رقم الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='البريد الإلكتروني')),
                ('address', models.TextField(blank=True, verbose_name='العنوان')),
                ('biography', models.TextField(blank=True, verbose_name='السيرة الذاتية')),
                ('photo', models.ImageField(blank=True, null=True, upload_to='party_leaders/', verbose_name='الصورة الشخصية')),
                ('party_founded_year', models.IntegerField(blank=True, null=True, verbose_name='سنة تأسيس الحزب')),
                ('party_ideology', models.CharField(blank=True, max_length=255, verbose_name='الأيديولوجية السياسية')),
                ('party_headquarters', models.TextField(blank=True, verbose_name='مقر الحزب')),
                ('achievements', models.TextField(blank=True, verbose_name='الإنجازات')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('status', models.CharField(choices=[('active', 'نشط'), ('inactive', 'غير نشط'), ('retired', 'متقاعد'), ('deceased', 'متوفى')], default='active', max_length=20, verbose_name='الحالة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'رئيس حزب',
                'verbose_name_plural': 'رؤساء الأحزاب',
                'ordering': ['name'],
            },
        ),
    ]
