<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الأزرار</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-center mb-4">اختبار أزرار المسؤولين الرسميين</h1>
        
        <!-- تنبيه واضح -->
        <div class="alert alert-danger text-center" style="font-size: 20px; font-weight: bold;">
            🚨 هذه هي الأزرار التي يجب أن تظهر في صفحة المسؤولين الرسميين 🚨
        </div>

        <!-- الأزرار -->
        <div style="background: #fff; padding: 30px; margin: 20px 0; border-radius: 15px; box-shadow: 0 5px 15px rgba(0,0,0,0.1); text-align: center; border: 3px solid #007bff;">
            <h2 style="color: #007bff; margin-bottom: 25px; font-weight: bold; font-size: 24px;">
                🛠️ أدوات إدارة المسؤولين الرسميين
            </h2>

            <div style="display: flex; justify-content: center; flex-wrap: wrap; gap: 15px; margin-top: 20px;">
                <!-- زر إضافة مسؤول -->
                <a href="/organizations/officials/add/"
                   style="background: #28a745; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 18px; font-weight: bold; display: inline-block; min-width: 200px; text-align: center;">
                    ➕ إضافة مسؤول جديد
                </a>

                <!-- زر تصدير -->
                <a href="/organizations/officials/export/"
                   style="background: #007bff; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 18px; font-weight: bold; display: inline-block; min-width: 200px; text-align: center;">
                    📊 تصدير إلى Excel
                </a>

                <!-- زر تحميل قالب -->
                <a href="/organizations/officials/template/"
                   style="background: #17a2b8; color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-size: 18px; font-weight: bold; display: inline-block; min-width: 200px; text-align: center;">
                    📥 تحميل قالب Excel
                </a>

                <!-- زر استيراد -->
                <button type="button" onclick="alert('زر الاستيراد يعمل!')"
                        style="background: #ffc107; color: #212529; padding: 15px 30px; border: none; border-radius: 8px; font-size: 18px; font-weight: bold; cursor: pointer; min-width: 200px;">
                    📤 استيراد من Excel
                </button>
            </div>

            <!-- أزرار إضافية للحذف الجماعي -->
            <div style="margin-top: 20px; padding-top: 20px; border-top: 2px solid #e9ecef;">
                <button type="button" onclick="alert('زر تحديد الكل يعمل!')"
                        style="background: #6c757d; color: white; padding: 10px 20px; border: none; border-radius: 8px; font-size: 16px; font-weight: bold; cursor: pointer; margin-right: 10px;">
                    ☑️ تحديد الكل
                </button>

                <button type="button" onclick="alert('زر الحذف الجماعي يعمل!')"
                        style="background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 8px; font-size: 16px; font-weight: bold; cursor: pointer;">
                    🗑️ حذف المحدد
                </button>
            </div>
        </div>

        <div class="alert alert-info text-center mt-4">
            <h4>إذا كنت ترى هذه الأزرار هنا ولا تراها في الصفحة الأصلية، فالمشكلة في التخزين المؤقت</h4>
            <p>جرب الضغط على Ctrl+F5 أو Ctrl+Shift+R لتحديث الصفحة بقوة</p>
        </div>

        <div class="text-center mt-4">
            <a href="/organizations/officials/" class="btn btn-primary btn-lg">
                العودة إلى صفحة المسؤولين الرسميين
            </a>
        </div>
    </div>
</body>
</html>
