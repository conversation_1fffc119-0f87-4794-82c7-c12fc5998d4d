{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}حذف الدعوة{% endblock %}

{% block extra_css %}
<style>
    .hero-mini {
        background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
        color: white;
        padding: 3rem 0;
        text-align: center;
        position: relative;
        overflow: hidden;
        margin-bottom: 2rem;
    }
    
    .hero-mini::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url('{% static "img/pattern.png" %}');
        opacity: 0.1;
    }
    
    .hero-mini .container {
        position: relative;
        z-index: 1;
    }
    
    .delete-card {
        border: none;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
        border-radius: 0.5rem;
        overflow: hidden;
        margin-bottom: 2rem;
    }
    
    .delete-card .card-header {
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
        border-bottom: none;
        padding: 1.25rem 1.5rem;
        color: #721c24;
    }
    
    .delete-card .card-body {
        padding: 1.5rem;
    }
    
    .invitation-info {
        background-color: #f8f9fa;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }
    
    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 50px;
        font-weight: 600;
        font-size: 0.8rem;
    }
    
    .status-draft {
        background-color: #e9ecef;
        color: #495057;
    }
    
    .status-sent {
        background-color: #d4edda;
        color: #155724;
    }
    
    .status-cancelled {
        background-color: #f8d7da;
        color: #721c24;
    }
    
    .status-responded {
        background-color: #cce5ff;
        color: #004085;
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="hero-mini text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-md-8 mx-auto text-center">
                <h1 class="display-5 fw-bold mb-3">حذف الدعوة</h1>
                <p class="lead">تأكيد حذف الدعوة المرسلة للمؤسسة</p>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Navigation Links -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'organizations:organization_list' %}">المؤسسات</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'organizations:invitation_list' %}">الدعوات</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'organizations:invitation_detail' invitation.pk %}">تفاصيل الدعوة</a></li>
                    <li class="breadcrumb-item active" aria-current="page">حذف الدعوة</li>
                </ol>
            </nav>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card delete-card">
                <div class="card-header d-flex align-items-center">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <h4 class="mb-0">تأكيد حذف الدعوة</h4>
                </div>
                <div class="card-body">
                    <div class="alert alert-danger" role="alert">
                        <h5 class="alert-heading"><i class="fas fa-exclamation-circle me-2"></i>تحذير!</h5>
                        <p>أنت على وشك حذف هذه الدعوة بشكل نهائي. هذا الإجراء لا يمكن التراجع عنه.</p>
                        {% if invitation.status == 'sent' %}
                        <hr>
                        <p class="mb-0">ملاحظة: هذه الدعوة تم إرسالها بالفعل إلى المؤسسة. حذفها لن يلغي استلام المؤسسة للدعوة، ولكن سيتم حذفها من قاعدة البيانات الخاصة بك.</p>
                        {% endif %}
                    </div>
                    
                    <div class="invitation-info">
                        <h5 class="mb-3"><i class="fas fa-info-circle me-2 text-primary"></i>معلومات الدعوة</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <strong>المؤسسة:</strong>
                                    <span>{{ invitation.organization.name }}</span>
                                </div>
                                <div class="mb-3">
                                    <strong>عنوان الدعوة:</strong>
                                    <span>{{ invitation.subject }}</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <strong>تاريخ الإنشاء:</strong>
                                    <span>{{ invitation.created_at|date:"Y-m-d H:i" }}</span>
                                </div>
                                <div class="mb-3">
                                    <strong>حالة الدعوة:</strong>
                                    <span class="status-badge status-{{ invitation.status }} mt-1 d-inline-block">
                                        {% if invitation.status == 'draft' %}
                                        <i class="fas fa-file me-1"></i> مسودة
                                        {% elif invitation.status == 'sent' %}
                                        <i class="fas fa-paper-plane me-1"></i> مرسلة
                                        {% elif invitation.status == 'cancelled' %}
                                        <i class="fas fa-ban me-1"></i> ملغاة
                                        {% elif invitation.status == 'responded' %}
                                        <i class="fas fa-reply me-1"></i> تم الرد
                                        {% endif %}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <form method="post" novalidate>
                        {% csrf_token %}
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'organizations:invitation_detail' invitation.pk %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-1"></i> العودة
                            </a>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash me-1"></i> تأكيد الحذف
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}