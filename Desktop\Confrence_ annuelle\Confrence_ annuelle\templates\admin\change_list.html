{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_list %}

{% block extrahead %}
{{ block.super }}
<style>
    .results {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        overflow: hidden;
        margin: 20px 0;
    }

    .results table {
        width: 100%;
        border-collapse: collapse;
        margin: 0;
    }

    .results table th {
        background: linear-gradient(135deg, var(--primary-color), var(--info-color));
        color: white;
        font-weight: 600;
        padding: 15px 20px;
        text-align: right;
        border: none;
        position: sticky;
        top: 0;
        z-index: 10;
    }

    .results table th a {
        color: white;
        text-decoration: none;
        font-weight: 600;
    }

    .results table th a:hover {
        color: #ffd700;
    }

    .results table td {
        padding: 15px 20px;
        border-bottom: 1px solid #eee;
        vertical-align: middle;
    }

    .results table tr:hover {
        background: rgba(44, 90, 160, 0.05);
    }

    .results table tr:nth-child(even) {
        background: rgba(248, 249, 250, 0.5);
    }

    .results table tr:nth-child(even):hover {
        background: rgba(44, 90, 160, 0.05);
    }

    .action-checkbox-column {
        width: 50px;
        text-align: center;
    }

    .action-checkbox {
        transform: scale(1.2);
        accent-color: var(--primary-color);
    }

    .actions {
        background: white;
        border-radius: 10px;
        padding: 15px 20px;
        margin: 20px 0;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        display: flex;
        align-items: center;
        gap: 15px;
    }

    .actions select {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 8px 12px;
        background: white;
        color: var(--dark-color);
        font-weight: 500;
    }

    .actions select:focus {
        border-color: var(--primary-color);
        outline: none;
        box-shadow: 0 0 0 3px rgba(44, 90, 160, 0.1);
    }

    .actions button {
        background: linear-gradient(135deg, var(--primary-color), var(--info-color));
        color: white;
        border: none;
        border-radius: 8px;
        padding: 8px 16px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .actions button:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(44, 90, 160, 0.4);
    }

    .actions .action-counter {
        color: var(--secondary-color);
        font-weight: 500;
        margin-right: auto;
    }

    .paginator {
        background: white;
        border-radius: 10px;
        padding: 15px 20px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        margin: 20px 0;
        text-align: center;
    }

    .paginator a {
        background: var(--primary-color);
        color: white;
        border-radius: 5px;
        padding: 8px 12px;
        margin: 0 2px;
        text-decoration: none;
        transition: all 0.3s ease;
        display: inline-block;
    }

    .paginator a:hover {
        background: var(--info-color);
        transform: translateY(-1px);
    }

    .paginator .this-page {
        background: var(--success-color);
        color: white;
        border-radius: 5px;
        padding: 8px 12px;
        margin: 0 2px;
        font-weight: 600;
    }

    .paginator .end {
        margin: 0 10px;
        color: var(--secondary-color);
    }

    #changelist-filter {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        border: none;
        margin: 20px 0;
    }

    #changelist-filter h2 {
        background: linear-gradient(135deg, var(--secondary-color), #2c3e50);
        color: white;
        padding: 15px 20px;
        margin: 0;
        font-weight: 600;
        border-radius: 15px 15px 0 0;
    }

    #changelist-filter h3 {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        color: var(--dark-color);
        padding: 10px 20px;
        margin: 0;
        font-weight: 600;
        font-size: 0.9rem;
        border-bottom: 1px solid #dee2e6;
    }

    #changelist-filter ul {
        padding: 0;
        margin: 0;
        list-style: none;
    }

    #changelist-filter li {
        border-bottom: 1px solid #f1f3f4;
    }

    #changelist-filter li:last-child {
        border-bottom: none;
    }

    #changelist-filter a {
        display: block;
        padding: 10px 20px;
        color: var(--dark-color);
        text-decoration: none;
        transition: all 0.3s ease;
    }

    #changelist-filter a:hover {
        background: rgba(44, 90, 160, 0.05);
        color: var(--primary-color);
        transform: translateX(-5px);
    }

    #changelist-filter .selected {
        background: linear-gradient(135deg, var(--primary-color), var(--info-color));
        color: white;
        font-weight: 600;
    }

    #changelist-filter .selected:hover {
        background: linear-gradient(135deg, var(--info-color), var(--primary-color));
        transform: none;
    }

    #toolbar {
        background: white;
        border-radius: 10px;
        padding: 15px 20px;
        margin: 20px 0;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        display: flex;
        align-items: center;
        gap: 15px;
    }

    #toolbar form {
        display: flex;
        align-items: center;
        gap: 10px;
        margin: 0;
    }

    #toolbar input[type="text"] {
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 8px 12px;
        background: white;
        color: var(--dark-color);
        font-weight: 500;
        min-width: 200px;
    }

    #toolbar input[type="text"]:focus {
        border-color: var(--primary-color);
        outline: none;
        box-shadow: 0 0 0 3px rgba(44, 90, 160, 0.1);
    }

    #toolbar input[type="submit"] {
        background: linear-gradient(135deg, var(--primary-color), var(--info-color));
        color: white;
        border: none;
        border-radius: 8px;
        padding: 8px 16px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    #toolbar input[type="submit"]:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(44, 90, 160, 0.4);
    }

    .object-tools {
        margin: 20px 0;
    }

    .object-tools li {
        display: inline-block;
        margin-left: 10px;
    }

    .object-tools a {
        background: linear-gradient(135deg, var(--success-color), #2ecc71);
        color: white;
        border-radius: 8px;
        padding: 10px 20px;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    .object-tools a:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4);
    }

    .object-tools a:before {
        content: '+';
        font-weight: bold;
        font-size: 1.2rem;
    }

    .addlink:before {
        content: '+' !important;
    }

    .changelink:before {
        content: '✏️' !important;
    }

    .deletelink:before {
        content: '🗑️' !important;
    }

    /* Status badges */
    .status-badge {
        padding: 4px 12px;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .status-invited {
        background: rgba(243, 156, 18, 0.1);
        color: var(--warning-color);
        border: 1px solid var(--warning-color);
    }

    .status-confirmed {
        background: rgba(39, 174, 96, 0.1);
        color: var(--success-color);
        border: 1px solid var(--success-color);
    }

    .status-declined {
        background: rgba(231, 76, 60, 0.1);
        color: var(--danger-color);
        border: 1px solid var(--danger-color);
    }

    .status-attended {
        background: rgba(52, 152, 219, 0.1);
        color: var(--info-color);
        border: 1px solid var(--info-color);
    }

    /* Responsive */
    @media (max-width: 768px) {
        .results table {
            font-size: 0.9rem;
        }
        
        .results table th,
        .results table td {
            padding: 10px 15px;
        }
        
        #toolbar {
            flex-direction: column;
            align-items: stretch;
        }
        
        #toolbar form {
            flex-direction: column;
        }
        
        #toolbar input[type="text"] {
            min-width: auto;
            width: 100%;
        }
        
        .actions {
            flex-direction: column;
            align-items: stretch;
        }
        
        .object-tools a {
            display: block;
            text-align: center;
            margin: 5px 0;
        }
    }
</style>
{% endblock %}

{% block content_title %}
<h1 style="color: var(--primary-color); font-weight: 600; margin-bottom: 1rem; display: flex; align-items: center;">
    {% if cl.opts.verbose_name_plural %}
        <i class="fas fa-list" style="margin-left: 10px;"></i>
        {{ cl.opts.verbose_name_plural|capfirst }}
    {% else %}
        <i class="fas fa-list" style="margin-left: 10px;"></i>
        {{ title }}
    {% endif %}
</h1>
{% endblock %}

{% block content %}
<div id="content-main">
    {% block object-tools %}
        {% if has_add_permission %}
            <ul class="object-tools">
                {% block object-tools-items %}
                    <li>
                        <a href="{% url cl.opts|admin_urlname:'add' %}{% if is_popup %}?{{ is_popup }}{% endif %}" class="addlink">
                            {% blocktrans with cl.opts.verbose_name as name %}إضافة {{ name }}{% endblocktrans %}
                        </a>
                    </li>
                {% endblock %}
            </ul>
        {% endif %}
    {% endblock %}

    {% if cl.formset.errors %}
        <p class="errornote">
            {% if cl.formset.errors|length == 1 %}{% trans "Please correct the error below." %}{% else %}{% trans "Please correct the errors below." %}{% endif %}
        </p>
        {{ cl.formset.non_form_errors }}
    {% endif %}

    <div class="module filtered" id="changelist">
        {% block search %}{% search_form cl %}{% endblock %}
        {% block date_hierarchy %}{% date_hierarchy cl %}{% endblock %}

        <form id="changelist-form" method="post" novalidate>{% csrf_token %}
            {% block result_list %}
                {% if action_form and actions_on_top and cl.show_admin_actions %}
                    <div class="actions">
                        {% admin_actions %}
                    </div>
                {% endif %}
                {% result_list cl %}
                {% if action_form and actions_on_bottom and cl.show_admin_actions %}
                    <div class="actions">
                        {% admin_actions %}
                    </div>
                {% endif %}
            {% endblock %}
        </form>
    </div>

    {% block pagination %}{% pagination cl %}{% endblock %}
</div>
{% endblock %}
