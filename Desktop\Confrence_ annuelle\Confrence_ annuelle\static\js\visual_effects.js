/**
 * ملف JavaScript لتفعيل التأثيرات البصرية المتقدمة
 */

document.addEventListener('DOMContentLoaded', function() {
    // تفعيل تأثيرات الظهور عند التمرير
    initScrollAnimations();
    
    // تفعيل تأثيرات الأزرار ثلاة الأبعاد
    init3DButtons();
    
    // تفعيل تأثيرات روابط التنقل
    initNavLinkHover();
    
    // تفعيل تأثيرات الكروت المتحركة
    initCardHoverEffects();
    
    // تفعيل تأثيرات الأزرار المتوسعة عند التحويم
    initHoverExpandButtons();
});

/**
 * تفعيل تأثيرات الظهور عند التمرير (مبسطة)
 */
function initScrollAnimations() {
    // الحصول على جميع العناصر التي تحتوي على تأثيرات الظهور عند التمرير
    const fadeElements = document.querySelectorAll(
        '.fade-in-element, .fade-in-scale, .fade-in-rotate'
    );
    
    // إضافة مراقب التمرير مع تبسيط الإعدادات
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
            }
            // تم إزالة خيار إزالة الصنف عند الخروج من منطقة العرض لتحسين الأداء
        });
    }, {
        threshold: 0.2, // زيادة نسبة الظهور لتقليل التأثيرات المبكرة
        rootMargin: '0px' // إزالة الهامش الإضافي للتفعيل المبكر
    });
    
    // إضافة المراقب لكل عنصر
    fadeElements.forEach(el => {
        observer.observe(el);
    });
}

/**
 * تفعيل تأثيرات الأزرار ثلاثية الأبعاد (مبسطة)
 */
function init3DButtons() {
    const buttons = document.querySelectorAll('.btn-3d');
    
    buttons.forEach(button => {
        button.addEventListener('mousemove', function(e) {
            const rect = button.getBoundingClientRect();
            const x = e.clientX - rect.left; // موقع المؤشر بالنسبة للزر (أفقي)
            const y = e.clientY - rect.top;  // موقع المؤشر بالنسبة للزر (رأسي)
            
            // حساب زاوية الدوران بناءً على موقع المؤشر
            const centerX = rect.width / 2;
            const centerY = rect.height / 2;
            
            // حساب الزاوية (تم تقليل الزاوية القصوى من 5 إلى 2 درجات)
            const rotateX = ((y - centerY) / centerY) * -2; // تقليل التأثير
            const rotateY = ((x - centerX) / centerX) * 2; // تقليل التأثير
            
            // تطبيق التحويل مع تقليل الارتفاع
            button.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateY(-1px)`;
        });
        
        button.addEventListener('mouseleave', function() {
            // إعادة الزر إلى وضعه الطبيعي
            button.style.transform = '';
        });
    });
}

/**
 * تفعيل تأثيرات روابط التنقل
 */
function initNavLinkHover() {
    const navLinks = document.querySelectorAll('.nav-link-hover');
    
    navLinks.forEach(link => {
        link.addEventListener('mouseenter', function() {
            // إضافة تأثيرات إضافية عند تمرير المؤشر
            link.classList.add('nav-hover-active');
        });
        
        link.addEventListener('mouseleave', function() {
            // إزالة التأثيرات عند مغادرة المؤشر
            link.classList.remove('nav-hover-active');
        });
    });
}

/**
 * تفعيل تأثيرات الكروت المتحركة
 */
function initCardHoverEffects() {
    const cards = document.querySelectorAll('.card-hover-effect');
    
    cards.forEach(card => {
        card.addEventListener('mousemove', function(e) {
            // تفعيل تأثير التحرك ثلاثي الأبعاد فقط للكروت التي تحتوي على صنف tilt
            if (card.classList.contains('tilt')) {
                const rect = card.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;
                
                const centerX = rect.width / 2;
                const centerY = rect.height / 2;
                
                const rotateX = ((y - centerY) / centerY) * -5;
                const rotateY = ((x - centerX) / centerX) * 5;
                
                card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) scale(1.05)`;
            }
        });
        
        card.addEventListener('mouseleave', function() {
            if (card.classList.contains('tilt')) {
                card.style.transform = '';
            }
        });
    });
}

/**
 * تفعيل تأثيرات الأزرار المتوسعة عند التحويم
 */
function initHoverExpandButtons() {
    const expandButtons = document.querySelectorAll('.btn-hover-expand');
    
    expandButtons.forEach(button => {
        // إضافة مستمع لحدث النقر للتأكد من أن الأزرار تعمل
        button.addEventListener('click', function(e) {
            // منع السلوك الافتراضي للرابط مؤقتًا لإضافة التأثير البصري
            e.preventDefault();
            
            // إضافة تأثير بصري عند النقر
            this.classList.add('pulse-once');
            
            // الحصول على الرابط المستهدف
            const targetHref = this.getAttribute('href');
            
            // طباعة رسالة تأكيد في وحدة التحكم
            console.log('تم النقر على زر:', this.textContent.trim());
            console.log('الانتقال إلى:', targetHref);
            
            // الانتقال إلى الرابط بعد إظهار التأثير البصري
            setTimeout(() => {
                this.classList.remove('pulse-once');
                // التأكد من وجود رابط قبل الانتقال
                if (targetHref) {
                    window.location.href = targetHref;
                }
            }, 300);
        });
    });
    
    // إضافة تأثيرات إضافية عند تمرير المؤشر
    expandButtons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.classList.add('btn-hover-active');
        });
        
        button.addEventListener('mouseleave', function() {
            this.classList.remove('btn-hover-active');
        });
    });
}