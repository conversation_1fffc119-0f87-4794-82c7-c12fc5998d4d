#!/usr/bin/env python
"""
Django management command لإنشاء حساب المسؤول الكبير Saad
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import User, Group, Permission
from django.contrib.contenttypes.models import ContentType
from core.models import AdminProfile


class Command(BaseCommand):
    help = 'إنشاء حساب المسؤول الكبير Saad مع الصلاحيات الكاملة'

    def handle(self, *args, **options):
        # إنشاء أو تحديث حساب المسؤول الكبير
        username = 'Saad'
        password = 'Saad127'
        email = '<EMAIL>'
        
        # التحقق من وجود المستخدم
        user, created = User.objects.get_or_create(
            username=username,
            defaults={
                'email': email,
                'is_staff': True,
                'is_superuser': True,
                'is_active': True,
                'first_name': 'سعد',
                'last_name': 'المسؤول الكبير'
            }
        )
        
        # تعيين كلمة المرور
        user.set_password(password)
        user.save()
        
        # إنشاء أو تحديث ملف المسؤول
        admin_profile, profile_created = AdminProfile.objects.get_or_create(
            user=user,
            defaults={
                'admin_type': 'super_admin',
                'full_name': 'سعد - المسؤول الكبير',
                'department': 'الإدارة العامة',
                'notes': 'المسؤول الكبير للنظام - صلاحيات كاملة',
                'is_active': True
            }
        )
        
        # تحديث البيانات إذا كان الملف موجوداً
        if not profile_created:
            admin_profile.admin_type = 'super_admin'
            admin_profile.full_name = 'سعد - المسؤول الكبير'
            admin_profile.department = 'الإدارة العامة'
            admin_profile.notes = 'المسؤول الكبير للنظام - صلاحيات كاملة'
            admin_profile.is_active = True
            admin_profile.save()
        
        # إنشاء مجموعات الصلاحيات
        self.create_admin_groups()
        
        # إضافة المسؤول الكبير لمجموعة المسؤولين الكبار
        super_admin_group = Group.objects.get(name='Super Admins')
        user.groups.add(super_admin_group)
        
        if created:
            self.stdout.write(
                self.style.SUCCESS(f'تم إنشاء حساب المسؤول الكبير "{username}" بنجاح!')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(f'تم تحديث حساب المسؤول الكبير "{username}" بنجاح!')
            )
        
        self.stdout.write(
            self.style.WARNING(f'اسم المستخدم: {username}')
        )
        self.stdout.write(
            self.style.WARNING(f'كلمة المرور: {password}')
        )

    def create_admin_groups(self):
        """إنشاء مجموعات الصلاحيات للمسؤولين"""
        
        # مجموعة المسؤولين الكبار
        super_admin_group, created = Group.objects.get_or_create(name='Super Admins')
        if created:
            self.stdout.write('تم إنشاء مجموعة المسؤولين الكبار')
        
        # مجموعة المسؤولين العاديين
        regular_admin_group, created = Group.objects.get_or_create(name='Regular Admins')
        if created:
            self.stdout.write('تم إنشاء مجموعة المسؤولين العاديين')
        
        # إضافة جميع الصلاحيات للمسؤولين الكبار
        all_permissions = Permission.objects.all()
        super_admin_group.permissions.set(all_permissions)
        
        # إضافة صلاحيات محدودة للمسؤولين العاديين
        # (سيتم تحديدها لاحقاً حسب الحاجة)
        basic_permissions = Permission.objects.filter(
            content_type__app_label__in=['organizations', 'core'],
            codename__in=['view_organization', 'add_organization', 'change_organization']
        )
        regular_admin_group.permissions.set(basic_permissions)
        
        self.stdout.write('تم تحديث صلاحيات المجموعات')
