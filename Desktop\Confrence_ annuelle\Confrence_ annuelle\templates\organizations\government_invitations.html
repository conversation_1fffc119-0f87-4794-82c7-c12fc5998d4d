{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "إرسال دعوات للمؤسسات الحكومية" %}{% endblock %}

{% block extra_css %}
<style>
    /* Mini Hero Section */
    .mini-hero {
        background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('{% static "img/pattern.png" %}');
        background-size: cover;
        background-position: center;
        color: white;
        padding: 3rem 0;
        position: relative;
        overflow: hidden;
        margin-bottom: 2rem;
    }
    
    .mini-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('{% static "img/pattern.png" %}');
        opacity: 0.2;
        animation: pulse 15s infinite;
    }
    
    @keyframes pulse {
        0% { opacity: 0.1; }
        50% { opacity: 0.3; }
        100% { opacity: 0.1; }
    }
    
    .mini-hero .container {
        position: relative;
        z-index: 1;
    }
    
    /* Organization Cards */
    .org-card {
        background-color: white;
        border-radius: 10px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        padding: 1.5rem;
        margin-bottom: 2rem;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    
    .org-card:hover {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        transform: translateY(-5px);
    }
    
    .org-card .org-icon {
        position: absolute;
        top: 1rem;
        right: 1rem;
        font-size: 2rem;
        color: rgba(0, 123, 255, 0.2);
        transition: all 0.3s ease;
    }
    
    .org-card:hover .org-icon {
        transform: scale(1.2);
        color: rgba(0, 123, 255, 0.5);
    }
    
    .org-card h5 {
        margin-bottom: 1rem;
        font-weight: 600;
        color: #333;
    }
    
    .org-card .org-details {
        margin-bottom: 1rem;
        font-size: 0.9rem;
    }
    
    .org-card .org-details p {
        margin-bottom: 0.5rem;
    }
    
    .org-card .org-actions {
        display: flex;
        justify-content: space-between;
        margin-top: 1.5rem;
    }
    
    /* Buttons */
    .btn-invite {
        background-color: #28a745;
        border-color: #28a745;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 5px;
        transition: all 0.3s;
    }
    
    .btn-invite:hover {
        background-color: #218838;
        border-color: #1e7e34;
        transform: translateY(-2px);
    }
    
    .btn-view {
        background-color: #17a2b8;
        border-color: #17a2b8;
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 5px;
        transition: all 0.3s;
    }
    
    .btn-view:hover {
        background-color: #138496;
        border-color: #117a8b;
        transform: translateY(-2px);
    }
    
    /* Selection Controls */
    .selection-controls {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 10px;
        margin-bottom: 2rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    }
    
    .selection-controls .form-check {
        margin-bottom: 0.5rem;
    }
    
    .selection-controls .btn-group {
        margin-top: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="mini-hero">
    <div class="container">
        <h1 class="display-4">{% trans "إرسال دعوات للمؤسسات الحكومية" %}</h1>
        <p class="lead">{% trans "يمكنك إرسال دعوات فردية أو جماعية للمؤسسات الحكومية" %}</p>
    </div>
</div>

<div class="container">
    <!-- Selection Controls -->
    <div class="selection-controls">
        <form id="bulk-invitation-form" action="{% url 'organizations:send_bulk_invitation' %}" method="post">
            {% csrf_token %}
            <div class="row">
                <div class="col-md-6">
                    <h5>{% trans "تحديد المؤسسات" %}</h5>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="select-all">
                        <label class="form-check-label" for="select-all">{% trans "تحديد الكل" %}</label>
                    </div>
                    <div class="org-checkboxes mt-3">
                        {% for org in organizations %}
                        <div class="form-check">
                            <input class="form-check-input org-checkbox" type="checkbox" name="organizations" value="{{ org.id }}" id="org-{{ org.id }}">
                            <label class="form-check-label" for="org-{{ org.id }}">{{ org.name }}</label>
                        </div>
                        {% endfor %}
                    </div>
                </div>
                <div class="col-md-6">
                    <h5>{% trans "تفاصيل الدعوة" %}</h5>
                    <div class="mb-3">
                        <label for="subject" class="form-label">{% trans "الموضوع" %}</label>
                        <input type="text" class="form-control" id="subject" name="subject" required>
                    </div>
                    <div class="mb-3">
                        <label for="message" class="form-label">{% trans "الرسالة" %}</label>
                        <textarea class="form-control" id="message" name="message" rows="5" required></textarea>
                    </div>
                </div>
            </div>
            <div class="btn-group w-100">
                <button type="submit" class="btn btn-primary">{% trans "إرسال الدعوات المحددة" %}</button>
                <a href="{% url 'organizations:government_organizations' %}" class="btn btn-secondary">{% trans "العودة" %}</a>
            </div>
        </form>
    </div>

    <!-- Organizations List -->
    <h3 class="mb-4">{% trans "قائمة المؤسسات الحكومية" %}</h3>
    <div class="row">
        {% for org in organizations %}
        <div class="col-md-6">
            <div class="org-card">
                <div class="org-icon">
                    {% if org.name == 'الوكالة الوطنية للإحصاء والتحليل الديمغرافي والاقتصادي' %}
                    <i class="fas fa-chart-bar"></i>
                    {% elif org.name == 'الصندوق الوطني للتأمين الصحي' %}
                    <i class="fas fa-heartbeat"></i>
                    {% elif org.name == 'ميناء نواذيبو المستقل' %}
                    <i class="fas fa-ship"></i>
                    {% elif org.name == 'الشركة الوطنية لتنمية البنى التحتية الرقمية' %}
                    <i class="fas fa-network-wired"></i>
                    {% elif org.name == 'وزارة التحول الرقمي والابتكار وعصرنة الإدارة' %}
                    <i class="fas fa-laptop-code"></i>
                    {% elif org.name == 'وزارة التهذيب الوطني وإصلاح النظام التعليمي' %}
                    <i class="fas fa-graduation-cap"></i>
                    {% elif org.name == 'وزارة التجهيز والنقل' %}
                    <i class="fas fa-road"></i>
                    {% elif org.name == 'وزارة المعادن والصناعة' %}
                    <i class="fas fa-industry"></i>
                    {% elif org.name == 'وزارة الداخلية واللامركزية' %}
                    <i class="fas fa-shield-alt"></i>
                    {% elif org.name == 'وزارة التجارة والصناعة والصناعة التقليدية والسياحة' %}
                    <i class="fas fa-shopping-cart"></i>
                    {% elif org.name == 'المفتشية العامة للدولة' %}
                    <i class="fas fa-search"></i>
                    {% elif org.name == 'المفتشية العامة للمالية' %}
                    <i class="fas fa-file-invoice-dollar"></i>
                    {% elif org.name == 'وزارة الشؤون الخارجية والتعاون والموريتانيين في الخارج' %}
                    <i class="fas fa-globe"></i>
                    {% elif org.name == 'الوكالة الوطنية للبحوث الجيولوجية والتراث المعدني' %}
                    <i class="fas fa-mountain"></i>
                    {% elif org.name == 'الوكالة الوطنية "معادن موريتانيا"' %}
                    <i class="fas fa-gem"></i>
                    {% elif org.name == 'الشركة الوطنية للصناعة والمناجم' %}
                    <i class="fas fa-hard-hat"></i>
                    {% elif org.name == 'المكتب الوطني للتقييس والمترولوجيا' %}
                    <i class="fas fa-balance-scale"></i>
                    {% elif org.name == 'وزارة الاقتصاد والمالية' %}
                    <i class="fas fa-money-bill-wave"></i>
                    {% elif org.name == 'Université des États arabes' %}
                    <i class="fas fa-university"></i>
                    {% elif org.name == 'Ambassade des États-Unis d\'Amérique' %}
                    <i class="fas fa-flag-usa"></i>
                    {% elif org.name == 'Ministère de l\'Habitat, de l\'Urbanisme et de l\'Aménagement du Territoire' %}
                    <i class="fas fa-building"></i>
                    {% elif org.name == 'Université Ain Shams' %}
                    <i class="fas fa-university"></i>
                    {% elif org.name == 'Consulat Honoraire de Hongrie' %}
                    <i class="fas fa-passport"></i>
                    {% else %}
                    <i class="fas fa-landmark"></i>
                    {% endif %}
                </div>
                <h5>{{ org.name }}</h5>
                <div class="org-details">
                    {% if org.contact_person %}
                    <p><strong>{% trans "جهة الاتصال" %}:</strong> {{ org.contact_person }}</p>
                    {% endif %}
                    {% if org.email %}
                    <p><strong>{% trans "البريد الإلكتروني" %}:</strong> {{ org.email }}</p>
                    {% endif %}
                    {% if org.phone %}
                    <p><strong>{% trans "الهاتف" %}:</strong> {{ org.phone }}</p>
                    {% endif %}
                </div>
                <div class="org-actions">
                    <a href="{% url 'organizations:send_invitation' %}?organization_id={{ org.id }}" class="btn btn-invite">{% trans "إرسال دعوة" %}</a>
                    <a href="{% url 'organizations:organization_detail' org.id %}" class="btn btn-view">{% trans "عرض التفاصيل" %}</a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Select All Checkbox
        const selectAllCheckbox = document.getElementById('select-all');
        const orgCheckboxes = document.querySelectorAll('.org-checkbox');
        
        selectAllCheckbox.addEventListener('change', function() {
            orgCheckboxes.forEach(checkbox => {
                checkbox.checked = selectAllCheckbox.checked;
            });
        });
        
        // Update Select All when individual checkboxes change
        orgCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const allChecked = Array.from(orgCheckboxes).every(cb => cb.checked);
                const anyChecked = Array.from(orgCheckboxes).some(cb => cb.checked);
                
                selectAllCheckbox.checked = allChecked;
                selectAllCheckbox.indeterminate = anyChecked && !allChecked;
            });
        });
        
        // Form Validation
        const bulkInvitationForm = document.getElementById('bulk-invitation-form');
        
        bulkInvitationForm.addEventListener('submit', function(event) {
            const checkedOrgs = document.querySelectorAll('.org-checkbox:checked');
            
            if (checkedOrgs.length === 0) {
                event.preventDefault();
                alert('{% trans "يرجى تحديد مؤسسة واحدة على الأقل" %}');
            }
        });
    });
</script>
{% endblock %}