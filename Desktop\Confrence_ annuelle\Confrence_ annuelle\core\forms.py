"""
Forms خاصة بإدارة المسؤولين
"""

from django import forms
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from .models import AdminProfile


class AdminProfileForm(forms.ModelForm):
    """نموذج تعديل ملف المسؤول"""
    
    # حقول إضافية من User model
    first_name = forms.CharField(
        label='الاسم الأول',
        max_length=150,
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'الاسم الأول'})
    )
    
    last_name = forms.CharField(
        label='اسم العائلة',
        max_length=150,
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم العائلة'})
    )
    
    email = forms.EmailField(
        label='البريد الإلكتروني',
        required=False,
        widget=forms.EmailInput(attrs={'class': 'form-control', 'placeholder': 'البريد الإلكتروني'})
    )
    
    class Meta:
        model = AdminProfile
        fields = ['admin_type', 'full_name', 'phone', 'department', 'profile_image', 'notes', 'is_active']
        widgets = {
            'admin_type': forms.Select(attrs={'class': 'form-select'}),
            'full_name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'الاسم الكامل'}),
            'phone': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم الهاتف'}),
            'department': forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'القسم'}),
            'profile_image': forms.ClearableFileInput(attrs={'class': 'form-control', 'accept': 'image/*'}),
            'notes': forms.Textarea(attrs={'class': 'form-control', 'rows': 4, 'placeholder': 'ملاحظات إضافية'}),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
        }
        labels = {
            'admin_type': 'نوع المسؤول',
            'full_name': 'الاسم الكامل',
            'phone': 'رقم الهاتف',
            'department': 'القسم',
            'profile_image': 'الصورة الشخصية',
            'notes': 'ملاحظات',
            'is_active': 'نشط',
        }


class CreateAdminForm(forms.Form):
    """نموذج إنشاء مسؤول جديد"""
    
    username = forms.CharField(
        label='اسم المستخدم',
        max_length=150,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم المستخدم'})
    )
    
    email = forms.EmailField(
        label='البريد الإلكتروني',
        widget=forms.EmailInput(attrs={'class': 'form-control', 'placeholder': 'البريد الإلكتروني'})
    )
    
    password = forms.CharField(
        label='كلمة المرور',
        widget=forms.PasswordInput(attrs={'class': 'form-control', 'placeholder': 'كلمة المرور'})
    )
    
    password_confirm = forms.CharField(
        label='تأكيد كلمة المرور',
        widget=forms.PasswordInput(attrs={'class': 'form-control', 'placeholder': 'تأكيد كلمة المرور'})
    )
    
    first_name = forms.CharField(
        label='الاسم الأول',
        max_length=150,
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'الاسم الأول'})
    )
    
    last_name = forms.CharField(
        label='اسم العائلة',
        max_length=150,
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'اسم العائلة'})
    )
    
    admin_type = forms.ChoiceField(
        label='نوع المسؤول',
        choices=AdminProfile.ADMIN_TYPE_CHOICES,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    full_name = forms.CharField(
        label='الاسم الكامل',
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'الاسم الكامل'})
    )
    
    phone = forms.CharField(
        label='رقم الهاتف',
        max_length=20,
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'رقم الهاتف'})
    )
    
    department = forms.CharField(
        label='القسم',
        max_length=100,
        required=False,
        widget=forms.TextInput(attrs={'class': 'form-control', 'placeholder': 'القسم'})
    )
    
    profile_image = forms.ImageField(
        label='الصورة الشخصية',
        required=False,
        widget=forms.ClearableFileInput(attrs={'class': 'form-control', 'accept': 'image/*'})
    )

    notes = forms.CharField(
        label='ملاحظات',
        required=False,
        widget=forms.Textarea(attrs={'class': 'form-control', 'rows': 4, 'placeholder': 'ملاحظات إضافية'})
    )
    
    def clean_username(self):
        """التحقق من عدم وجود اسم المستخدم"""
        username = self.cleaned_data['username']
        if User.objects.filter(username=username).exists():
            raise ValidationError('اسم المستخدم موجود بالفعل')
        return username
    
    def clean_email(self):
        """التحقق من عدم وجود البريد الإلكتروني"""
        email = self.cleaned_data['email']
        if User.objects.filter(email=email).exists():
            raise ValidationError('البريد الإلكتروني مستخدم بالفعل')
        return email
    
    def clean(self):
        """التحقق من تطابق كلمات المرور"""
        cleaned_data = super().clean()
        password = cleaned_data.get('password')
        password_confirm = cleaned_data.get('password_confirm')
        
        if password and password_confirm:
            if password != password_confirm:
                raise ValidationError('كلمات المرور غير متطابقة')
        
        return cleaned_data


class AdminSearchForm(forms.Form):
    """نموذج البحث في المسؤولين"""
    
    search = forms.CharField(
        label='البحث',
        max_length=255,
        required=False,
        widget=forms.TextInput(attrs={
            'class': 'form-control',
            'placeholder': 'البحث بالاسم أو اسم المستخدم أو البريد الإلكتروني'
        })
    )
    
    admin_type = forms.ChoiceField(
        label='نوع المسؤول',
        choices=[('', 'جميع الأنواع')] + list(AdminProfile.ADMIN_TYPE_CHOICES),
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    status = forms.ChoiceField(
        label='الحالة',
        choices=[
            ('', 'جميع الحالات'),
            ('active', 'نشط'),
            ('inactive', 'غير نشط'),
        ],
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
