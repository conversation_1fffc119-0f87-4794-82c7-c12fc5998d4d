{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "لوحة الإدارة - مؤتمر السيرة النبوية" %}{% endblock %}

{% block extra_css %}
<style>
    /* تنسيقات خاصة بصفحة لوحة الإدارة */
    .admin-dashboard {
        padding: 2rem 0;
    }
    
    .dashboard-header {
        background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 10px;
        position: relative;
        overflow: hidden;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }
    
    .dashboard-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, rgba(255,255,255,0.1), rgba(255,255,255,0));
        transition: all 0.8s ease;
    }
    
    .dashboard-header:hover::before {
        left: 100%;
    }
    
    .dashboard-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 1rem;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    }
    
    .dashboard-subtitle {
        font-size: 1.2rem;
        opacity: 0.9;
    }
    
    .stat-card {
        background-color: white;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.15);
    }
    
    .stat-card::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 4px;
        background: linear-gradient(90deg, #1a237e, #3949ab);
        transform: scaleX(0);
        transform-origin: right;
        transition: transform 0.3s ease;
    }
    
    .stat-card:hover::after {
        transform: scaleX(1);
        transform-origin: left;
    }
    
    .stat-icon {
        font-size: 2.5rem;
        margin-bottom: 1rem;
        color: #3949ab;
    }
    
    .stat-value {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        color: #1a237e;
    }
    
    .stat-label {
        font-size: 1rem;
        color: #666;
    }
    
    .quick-actions {
        background-color: white;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
    }
    
    .action-btn {
        margin-bottom: 0.5rem;
        transition: all 0.3s ease;
    }
    
    .action-btn:hover {
        transform: translateY(-3px);
    }
    
    .recent-items {
        background-color: white;
        border-radius: 10px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
    }
    
    .item-list {
        max-height: 400px;
        overflow-y: auto;
    }
    
    .item-card {
        padding: 1rem;
        border-bottom: 1px solid #eee;
        transition: all 0.3s ease;
    }
    
    .item-card:hover {
        background-color: #f9f9f9;
    }
    
    .item-card:last-child {
        border-bottom: none;
    }
    
    .item-title {
        font-weight: 600;
        margin-bottom: 0.3rem;
    }
    
    .item-meta {
        font-size: 0.85rem;
        color: #666;
    }
    
    .bg-decoration {
        position: absolute;
        border-radius: 50%;
        opacity: 0.1;
        z-index: -1;
    }
    
    .bg-decoration-1 {
        width: 300px;
        height: 300px;
        background-color: #3949ab;
        top: -100px;
        right: -100px;
    }
    
    .bg-decoration-2 {
        width: 200px;
        height: 200px;
        background-color: #1a237e;
        bottom: -50px;
        left: -50px;
    }
</style>
{% endblock %}

{% block content %}
<div class="admin-dashboard">
    <!-- رأس لوحة المسؤول -->
    <div class="container">
        <div class="dashboard-header text-center animate__animated animate__fadeIn">
            <img src="{% static 'img/new_logo.svg' %}" alt="شعار المؤتمر" height="80" class="mb-3" style="filter: brightness(0) invert(1);">
            <h1 class="dashboard-title">{% trans "لوحة الإدارة" %}</h1>
            <p class="dashboard-subtitle">{% trans "مرحبًا بك في لوحة الإدارة المخصصة لمؤتمر السيرة النبوية السنوي" %}</p>
            <div class="bg-decoration bg-decoration-1"></div>
            <div class="bg-decoration bg-decoration-2"></div>
        </div>
    </div>
    
    <!-- إحصائيات سريعة -->
    <div class="container">
        <div class="row">
            <div class="col-md-3">
                <div class="stat-card text-center animate__animated animate__fadeInUp">
                    <i class="fas fa-building stat-icon"></i>
                    <div class="stat-value">{{ total_organizations }}</div>
                    <div class="stat-label">{% trans "المؤسسات" %}</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card text-center animate__animated animate__fadeInUp animate__delay-1s">
                    <i class="fas fa-calendar-alt stat-icon"></i>
                    <div class="stat-value">{{ total_editions }}</div>
                    <div class="stat-label">{% trans "دورات المؤتمر" %}</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card text-center animate__animated animate__fadeInUp animate__delay-2s">
                    <i class="fas fa-users stat-icon"></i>
                    <div class="stat-value">{{ user_count|default:"0" }}</div>
                    <div class="stat-label">{% trans "المستخدمين" %}</div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card text-center animate__animated animate__fadeInUp animate__delay-3s">
                    <i class="fas fa-users-cog stat-icon"></i>
                    <div class="stat-value">{{ total_party_leaders|default:"0" }}</div>
                    <div class="stat-label">{% trans "رؤساء الأحزاب" %}</div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- الإجراءات السريعة والعناصر الحديثة -->
    <div class="container mt-4">
        <div class="row">
            <!-- الإجراءات السريعة -->
            <div class="col-md-4">
                <div class="quick-actions animate__animated animate__fadeIn">
                    <h3 class="mb-4">{% trans "إجراءات سريعة" %}</h3>
                    <div class="d-grid gap-2">
                        <a href="{% url 'organizations:send_bulk_invitation' %}" class="btn btn-primary btn-3d action-btn">
                            <i class="fas fa-envelope icon-float"></i> {% trans "إرسال دعوات جماعية" %}
                        </a>
                        <a href="{% url 'organizations:invitation_list' %}" class="btn btn-info btn-3d action-btn">
                            <i class="fas fa-list icon-float"></i> {% trans "قائمة الدعوات" %}
                        </a>
                        <a href="{% url 'organizations:organization_list' %}" class="btn btn-success btn-3d action-btn">
                            <i class="fas fa-building icon-float"></i> {% trans "إدارة المؤسسات" %}
                        </a>
                        <a href="{% url 'organizations:party_leaders' %}" class="btn btn-warning btn-3d action-btn">
                            <i class="fas fa-users-cog icon-float"></i> {% trans "إدارة رؤساء الأحزاب" %}
                        </a>
                        <a href="/admin/" class="btn btn-secondary btn-3d action-btn">
                            <i class="fas fa-cogs icon-float"></i> {% trans "لوحة تحكم " %}
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- الإشعارات الحديثة -->
            <div class="col-md-8">
                <div class="recent-items animate__animated animate__fadeIn">
                    <h3 class="mb-4">{% trans "آخر الإشعارات" %}</h3>
                    <div class="item-list">
                        {% if recent_notifications %}
                            {% for notification in recent_notifications %}
                                <div class="item-card">
                                    <div class="item-title">{{ notification.title }}</div>
                                    <div class="item-content">{{ notification.content }}</div>
                                    <div class="item-meta">
                                        <i class="far fa-clock"></i> {{ notification.created_at|date:"j F Y, H:i" }}
                                        {% if notification.user %}
                                            <span class="ms-2"><i class="far fa-user"></i> {{ notification.user.username }}</span>
                                        {% endif %}
                                    </div>
                                </div>
                            {% endfor %}
                        {% else %}
                            <div class="text-center py-4">
                                <i class="far fa-bell-slash fa-3x text-muted mb-3"></i>
                                <p>{% trans "لا توجد إشعارات حديثة" %}</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}