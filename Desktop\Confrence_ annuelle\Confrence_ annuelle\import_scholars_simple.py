#!/usr/bin/env python
import os
import django

# Configure Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'conference.settings')
django.setup()

from organizations.models import Scholar

# البيانات من الصور المرسلة
scholars_data = [
    {
        'name': 'شيخ محمد فال (ابن) بن عبد الله',
        'title': 'sheikh',
        'position': 'شيخ العلامة محمد الحسين أحمد الخديم',
        'participation_status': 'invited'
    },
    {
        'name': 'الشيخ محمد يحي ابن فدي',
        'title': 'sheikh',
        'position': '',
        'participation_status': 'invited'
    },
    {
        'name': 'خليفة الشيخ المجلل الشيخ التجاني بن الشيخ الهادي',
        'title': 'sheikh',
        'position': '',
        'participation_status': 'invited'
    },
    {
        'name': 'خليفة الشيخ المجلل الشيخ الخليل بن الشيخان',
        'title': 'sheikh',
        'position': '',
        'participation_status': 'invited'
    },
    {
        'name': 'خليفة الشيخ المجلل الشيخ أحمد بن الشيخان',
        'title': 'sheikh',
        'position': '',
        'participation_status': 'invited'
    },
    {
        'name': 'الشيخ علي الرضا بن محمد ناجي',
        'title': 'sheikh',
        'position': '',
        'participation_status': 'invited'
    },
    {
        'name': 'الشيخ غيث بن اعل الشيخ',
        'title': 'sheikh',
        'position': '',
        'participation_status': 'invited'
    },
    {
        'name': 'الشيخ محمد فاضل بن اعل الشيخ',
        'title': 'sheikh',
        'position': '',
        'participation_status': 'invited'
    },
    {
        'name': 'الشيخ سيدي محمد (الفخامة) ولد الشيخ سيديا',
        'title': 'sheikh',
        'position': '',
        'participation_status': 'invited'
    },
    {
        'name': 'المنسق العام والمرجع الروحي للاتحاد العام للوزراء والهيئات ذات السند الكوني في القارة',
        'title': 'sheikh',
        'position': 'الشيخ سيدي أعمر سيديا الكوني',
        'participation_status': 'invited'
    },
    {
        'name': 'خليفة الشيخ المجلل الشيخ أمين ولد الصوفي',
        'title': 'sheikh',
        'position': '',
        'participation_status': 'invited'
    },
    {
        'name': 'الدكتور محمد الحافظ الحكمي',
        'title': 'doctor',
        'position': '',
        'participation_status': 'invited'
    },
    {
        'name': 'الشيخ الباي ولد جلاس',
        'title': 'sheikh',
        'position': '',
        'participation_status': 'invited'
    },
    {
        'name': 'الشيخ عبد الفتاح بياه',
        'title': 'sheikh',
        'position': '',
        'participation_status': 'invited'
    },
    {
        'name': 'الشيخ الكوري أداعه',
        'title': 'sheikh',
        'position': '',
        'participation_status': 'invited'
    },
    {
        'name': 'الشيخ محمد سالم أمون',
        'title': 'sheikh',
        'position': '',
        'participation_status': 'invited'
    },
    {
        'name': 'الشيخ عبد الله ولد اعل سالم',
        'title': 'sheikh',
        'position': '',
        'participation_status': 'invited'
    },
    {
        'name': 'الشيخ محمد المختار ولد امراله',
        'title': 'sheikh',
        'position': '',
        'participation_status': 'invited'
    },
    {
        'name': 'الشيخ أحمد الحاج',
        'title': 'sheikh',
        'position': '',
        'participation_status': 'invited'
    },
    {
        'name': 'معالي الوزير الدكتور أبو بكر أحمد',
        'title': 'doctor',
        'position': '',
        'participation_status': 'invited'
    },
    {
        'name': 'الشيخ محمد الأعظف سيدي',
        'title': 'sheikh',
        'position': '',
        'participation_status': 'invited'
    },
    {
        'name': 'الشيخ أحمدو سالم ولد محمد بحظيه',
        'title': 'sheikh',
        'position': '',
        'participation_status': 'invited'
    },
    {
        'name': 'الشيخ إبراهيم الشيخ سيدي المختار الشيخ سيديا',
        'title': 'sheikh',
        'position': '',
        'participation_status': 'invited'
    },
    {
        'name': 'الشيخ باب طال',
        'title': 'sheikh',
        'position': '',
        'participation_status': 'invited'
    },
    {
        'name': 'الشيخ أحمد سالم الشيخ المستعين',
        'title': 'sheikh',
        'position': '',
        'participation_status': 'invited'
    },
    {
        'name': 'الشيخ محمد محمود ولد محمد لول',
        'title': 'sheikh',
        'position': '',
        'participation_status': 'invited'
    },
    {
        'name': 'الشيخ الأمانة ولد الداه',
        'title': 'sheikh',
        'position': '',
        'participation_status': 'invited'
    },
    {
        'name': 'الشيخ أحمد ولد محمد مسكه',
        'title': 'sheikh',
        'position': '',
        'participation_status': 'invited'
    },
    {
        'name': 'الشيخ بن صباح',
        'title': 'sheikh',
        'position': 'الأمين العام لهيئة العلماء الموريتانيين',
        'participation_status': 'invited'
    }
]

created_count = 0
for scholar_data in scholars_data:
    # تنظيف الاسم
    name = scholar_data['name'].strip()
    
    # استخراج اللقب من الاسم إذا كان موجوداً
    title = scholar_data.get('title', 'sheikh')
    if name.startswith('الدكتور'):
        title = 'doctor'
        name = name.replace('الدكتور', '').strip()
    elif name.startswith('الأستاذ'):
        title = 'professor'
        name = name.replace('الأستاذ', '').strip()
    elif name.startswith('الشيخ'):
        title = 'sheikh'
        name = name.replace('الشيخ', '').strip()
    elif name.startswith('معالي'):
        name = name.replace('معالي الوزير', '').strip()
        if 'الدكتور' in name:
            title = 'doctor'
            name = name.replace('الدكتور', '').strip()

    # إنشاء أو تحديث العالم
    scholar, created = Scholar.objects.get_or_create(
        name=name,
        defaults={
            'title': title,
            'full_name': scholar_data['name'],
            'position': scholar_data.get('position', ''),
            'participation_status': scholar_data.get('participation_status', 'invited'),
            'country': 'موريتانيا'  # افتراضياً
        }
    )
    
    if created:
        created_count += 1
        print(f'تم إنشاء: {scholar.get_full_title_name()}')
    else:
        print(f'موجود مسبقاً: {scholar.get_full_title_name()}')

print(f'تم استيراد {created_count} عالم بنجاح')
