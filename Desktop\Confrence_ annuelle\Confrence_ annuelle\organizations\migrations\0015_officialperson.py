# Generated by Django 5.1.2 on 2025-07-15 16:14

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('organizations', '0014_partyleader'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='OfficialPerson',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(choices=[('حضرة الأستاذ الفاضل', 'حضرة الأستاذ الفاضل'), ('حضرة الأستاذة الفاضلة', 'حضرة الأستاذة الفاضلة'), ('صاحب المعالي', 'صاحب المعالي'), ('صاحبة المعالي', 'صاحبة المعالي'), ('صاحب السعادة', 'صاحب السعادة'), ('صاحبة السعادة', 'صاحبة السعادة'), ('الدكتور', 'الدكتور'), ('الدكتورة', 'الدكتورة')], default='حضرة الأستاذ الفاضل', max_length=50, verbose_name='اللقب')),
                ('name', models.CharField(max_length=255, verbose_name='الاسم')),
                ('position', models.CharField(max_length=255, verbose_name='المنصب')),
                ('category', models.CharField(choices=[('youth_council', 'أعضاء المجلس الوطني للشباب'), ('fishing_ministry', 'مسؤولون في وزارة الصيد والبنى التحتية البحرية والمنائية'), ('interior_ministry', 'مسؤولون في وزارة الداخلية وترقية اللامركزية والتنمية المحلية'), ('defense_ministry', 'مسؤولون في وزارة الدفاع وشؤون المتقاعدين وأولاد الشهداء')], max_length=50, verbose_name='الفئة')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('photo', models.ImageField(blank=True, null=True, upload_to='officials/photos/', verbose_name='الصورة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='أنشئ بواسطة')),
            ],
            options={
                'verbose_name': 'مسؤول رسمي',
                'verbose_name_plural': 'المسؤولون الرسميون',
                'ordering': ['category', 'name'],
            },
        ),
    ]
