from django.urls import path
from . import views, admin_views

app_name = 'core'

urlpatterns = [
    path('', views.home, name='home'),
    path('about/', views.about, name='about'),
    path('contact/', views.contact, name='contact'),
    path('notifications/', views.notification_list, name='notification_list'),
    path('notifications/<int:pk>/mark-as-read/', views.mark_notification_as_read, name='mark_notification_as_read'),
    path('admin-dashboard/', views.admin_dashboard, name='admin_dashboard'),

    # Admin Management URLs - للمسؤول الكبير فقط
    path('admin-management/', admin_views.admin_list, name='admin_list'),
    path('admin-management/create/', admin_views.admin_create, name='admin_create'),
    path('admin-management/<int:pk>/', admin_views.admin_detail, name='admin_detail'),
    path('admin-management/<int:pk>/edit/', admin_views.admin_edit, name='admin_edit'),
    path('admin-management/<int:pk>/delete/', admin_views.admin_delete, name='admin_delete'),
    path('admin-management/<int:pk>/toggle-status/', admin_views.admin_toggle_status, name='admin_toggle_status'),

    # Export and Statistics URLs
    path('admin-management/export/csv/', admin_views.export_admins_csv, name='export_admins_csv'),
    path('admin-management/statistics/', admin_views.admin_statistics, name='admin_statistics'),
]