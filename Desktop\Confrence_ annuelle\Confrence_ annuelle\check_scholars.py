#!/usr/bin/env python
import os
import django

# Configure Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'conference.settings')
django.setup()

from organizations.models import Scholar

# عدد العلماء
scholars_count = Scholar.objects.count()
print(f'عدد العلماء في قاعدة البيانات: {scholars_count}')

# عرض أول 10 علماء
print('\nأول 10 علماء:')
for scholar in Scholar.objects.all()[:10]:
    print(f'- {scholar.get_full_title_name()}')

if scholars_count > 10:
    print(f'... و {scholars_count - 10} عالم آخر')
