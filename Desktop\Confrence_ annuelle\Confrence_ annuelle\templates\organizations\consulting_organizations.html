{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "المؤسسات الاستشارية" %}{% endblock %}

{% block extra_css %}
<style>
    .hero-section {
        background: linear-gradient(135deg, #6a1b9a 0%, #4a148c 100%);
        padding: 80px 0;
        color: white;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .organization-card {
        border-radius: 15px;
        overflow: hidden;
        margin-bottom: 30px;
        transition: all 0.3s ease;
        position: relative;
        background-color: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 32px rgba(106, 27, 154, 0.1);
    }

    .organization-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(106, 27, 154, 0.2);
    }

    .default-logo {
        width: 80px;
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f5f5f5;
        border-radius: 50%;
        margin: 0 auto 15px;
        transition: all 0.3s ease;
    }

    .default-logo:hover {
        transform: scale(1.1);
        box-shadow: 0 0 20px rgba(106, 27, 154, 0.4);
    }

    .consulting-icon {
        transition: all 0.3s ease;
        filter: drop-shadow(0 2px 5px rgba(106, 27, 154, 0.3));
    }

    .default-logo:hover .consulting-icon {
        transform: scale(1.1);
        filter: drop-shadow(0 0 8px rgba(106, 27, 154, 0.6));
    }

    .bg-decoration {
        position: absolute;
        opacity: 0.1;
        z-index: 0;
    }

    .bg-circle-1 {
        width: 300px;
        height: 300px;
        border-radius: 50%;
        background: #6a1b9a;
        top: -100px;
        right: -100px;
        animation: float 8s ease-in-out infinite;
    }

    .bg-circle-2 {
        width: 200px;
        height: 200px;
        border-radius: 50%;
        background: #4a148c;
        bottom: -50px;
        left: 10%;
        animation: float 10s ease-in-out infinite reverse;
    }

    .bg-wave {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 50px;
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%23ffffff' fill-opacity='1' d='M0,96L48,112C96,128,192,160,288,160C384,160,480,128,576,112C672,96,768,96,864,112C960,128,1056,160,1152,160C1248,160,1344,128,1392,112L1440,96L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z'%3E%3C/path%3E%3C/svg%3E");
        background-size: cover;
    }

    .back-link {
        display: inline-block;
        margin-top: 20px;
        color: white;
        text-decoration: none;
        font-weight: bold;
        transition: all 0.3s ease;
    }

    .back-link:hover {
        transform: translateX(-5px);
        color: #e0e0e0;
    }

    .btn-action {
        margin: 5px;
        border-radius: 20px;
        padding: 8px 15px;
        font-size: 0.9rem;
        transition: all 0.3s ease;
    }

    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    @keyframes float {
        0% { transform: translateY(0px); }
        50% { transform: translateY(-20px); }
        100% { transform: translateY(0px); }
    }

    .bg-decoration-3 {
        position: absolute;
        width: 150px;
        height: 150px;
        background: #6a1b9a;
        opacity: 0.1;
        border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
        top: 20%;
        left: 5%;
        animation: morph 15s linear infinite alternate;
    }

    .bg-decoration-4 {
        position: absolute;
        width: 100px;
        height: 100px;
        background: #4a148c;
        opacity: 0.1;
        border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
        bottom: 20%;
        right: 5%;
        animation: morph 12s linear infinite alternate-reverse;
    }

    @keyframes morph {
        0% { border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%; }
        25% { border-radius: 58% 42% 75% 25% / 76% 46% 54% 24%; }
        50% { border-radius: 50% 50% 33% 67% / 55% 27% 73% 45%; }
        75% { border-radius: 33% 67% 58% 42% / 63% 68% 32% 37%; }
        100% { border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%; }
    }

    .icon-pulse {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }

    .wavy-underline {
        position: relative;
        display: inline-block;
    }

    .wavy-underline::after {
        content: '';
        position: absolute;
        left: 0;
        bottom: -5px;
        width: 100%;
        height: 8px;
        background: url("data:image/svg+xml,%3Csvg width='100' height='8' viewBox='0 0 100 8' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0.5 4C8.5 4 8.5 7.5 16.5 7.5C24.5 7.5 24.5 4 32.5 4C40.5 4 40.5 7.5 48.5 7.5C56.5 7.5 56.5 4 64.5 4C72.5 4 72.5 7.5 80.5 7.5C88.5 7.5 88.5 4 96.5 4' stroke='white' stroke-opacity='0.5' stroke-width='2'/%3E%3C/svg%3E");
        background-size: 100px 8px;
        background-repeat: repeat-x;
        animation: wave 2s linear infinite;
    }

    @keyframes wave {
        0% { background-position-x: 0; }
        100% { background-position-x: 100px; }
    }

    .invitation-actions {
        background-color: rgba(255, 255, 255, 0.9);
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 30px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    }
</style>
{% endblock %}

{% block content %}
<div class="hero-section">
    <div class="container">
        <h1 class="display-4">{% trans "المؤسسات الاستشارية" %}</h1>
        <p class="lead">{% trans "استعراض جميع المؤسسات الاستشارية المشاركة في المؤتمر" %}</p>
        <a href="{% url 'organizations:organization_list' %}" class="back-link"><i class="fas fa-arrow-right ml-2"></i> {% trans "العودة إلى قائمة المؤسسات" %}</a>
    </div>
    <div class="bg-decoration bg-circle-1"></div>
    <div class="bg-decoration bg-circle-2"></div>
    <div class="bg-decoration bg-decoration-3"></div>
    <div class="bg-decoration bg-decoration-4"></div>
    <div class="bg-wave"></div>
</div>

<!-- Background Decorations -->
<div class="bg-decoration bg-decoration-1"></div>
<div class="bg-decoration bg-decoration-2"></div>
<div class="bg-decoration bg-decoration-3"></div>
<div class="bg-decoration bg-decoration-4"></div>

<!-- Organizations Section -->
<div class="container mb-5">
    {% csrf_token %}
    <div class="row">
        <!-- Consulting Organizations -->
        <div class="col-12">
            <div class="section-header mb-4 text-center">
                <h2 class="text-primary"><i class="fas fa-briefcase me-2 icon-pulse"></i> {% trans "المؤسسات الاستشارية" %}</h2>
                <p class="text-muted">{% trans "المؤسسات الاستشارية المشاركة في المؤتمر" %}</p>
            </div>

            <!-- Organization Management Actions -->
            <div class="invitation-actions mb-4">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="mb-0"><i class="fas fa-cogs me-2"></i> {% trans "إدارة المؤسسات" %}</h5>
                    <div>
                        <button class="btn btn-success btn-sm" data-bs-toggle="modal" data-bs-target="#addOrganizationModal">
                            <i class="fas fa-plus me-1"></i> {% trans "إضافة مؤسسة جديدة" %}
                        </button>
                        <button class="btn btn-danger btn-sm ms-2" id="bulk-delete-btn" disabled>
                            <i class="fas fa-trash me-1"></i> {% trans "حذف المحدد" %}
                            (<span id="selected-count">0</span>)
                        </button>
                        <button class="btn btn-primary btn-sm ms-2" data-bs-toggle="modal" data-bs-target="#inviteModal">
                            <i class="fas fa-paper-plane me-1"></i> {% trans "إرسال دعوة فردية" %}
                        </button>
                        <button class="btn btn-info btn-sm ms-2" id="bulk-invite-btn" disabled>
                            <i class="fas fa-envelope-open me-1"></i> {% trans "إرسال دعوات متعددة" %}
                        </button>
                    </div>
                </div>
                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="select-all">
                    <label class="form-check-label" for="select-all">
                        {% trans "تحديد الكل" %}
                    </label>
                </div>
                <div class="selected-info small text-muted">
                    <span id="selected-text">{% trans "لم يتم تحديد أي مؤسسة" %}</span>
                </div>
            </div>
            
            {% if organizations %}
            <div class="row">
                {% for organization in organizations %}
                <div class="col-md-4 col-lg-3 mb-4 fade-in-element">
                    <div class="card h-100 organization-card glass-effect glow-border card-hover-effect">
                        <div class="card-header bg-white border-0 pt-4">
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="selected_organizations" value="{{ organization.pk }}" id="org-{{ organization.pk }}">
                                    <label class="form-check-label" for="org-{{ organization.pk }}"></label>
                                </div>
                                <span class="badge {% if organization.participation_status == 'confirmed' %}bg-success{% elif organization.participation_status == 'invited' %}bg-warning{% elif organization.participation_status == 'declined' %}bg-danger{% else %}bg-info{% endif %}">
                                    {{ organization.get_participation_status_display }}
                                </span>
                                <span class="badge bg-purple">{% trans "استشارية" %}</span>
                            </div>
                        </div>
                        <div class="card-body text-center">
                            <div class="organization-logo mb-3">
                                {% if organization.logo %}
                                <img src="{{ organization.logo.url }}" alt="{{ organization.name }}" class="img-fluid icon-float" style="max-height: 80px;">
                                {% else %}
                                <div class="default-logo multi-layer-shadow">
                                    {% with forloop.counter|divisibleby:5 as icon_type %}
                                    {% if icon_type == 1 %}
                                    <!-- استشارات إدارية -->
                                    <svg class="consulting-icon" width="80" height="80" viewBox="0 0 24 24">
                                        <path fill="#6a1b9a" d="M12,3L1,9L12,15L21,10.09V17H23V9M5,13.18V17.18L12,21L19,17.18V13.18L12,17L5,13.18Z"/>
                                    </svg>
                                    {% elif icon_type == 2 %}
                                    <!-- استشارات مالية -->
                                    <svg class="consulting-icon" width="80" height="80" viewBox="0 0 24 24">
                                        <path fill="#6a1b9a" d="M11.8,10.9c-2.27-0.59-3-1.2-3-2.15c0-1.09,1.01-1.85,2.7-1.85c1.78,0,2.44,0.85,2.5,2.1h2.21 c-0.07-1.72-1.12-3.3-3.21-3.81V3h-3v2.16c-1.94,0.42-3.5,1.68-3.5,3.61c0,2.31,1.91,3.46,4.7,4.13c2.5,0.6,3,1.48,3,2.41 c0,0.69-0.49,1.79-2.7,1.79c-2.06,0-2.87-0.92-2.98-2.1H6.32c0.12,2.19,1.76,3.42,3.68,3.83V21h3v-2.15 c1.95-0.37,3.5-1.5,3.5-3.55C16.5,12.46,14.07,11.49,11.8,10.9z"/>
                                    </svg>
                                    {% elif icon_type == 3 %}
                                    <!-- استشارات قانونية -->
                                    <svg class="consulting-icon" width="80" height="80" viewBox="0 0 24 24">
                                        <path fill="#6a1b9a" d="M12,1L3,5v6c0,5.55,3.84,10.74,9,12c5.16-1.26,9-6.45,9-12V5L12,1z M19,11c0,4.52-2.98,8.69-7,9.93 C7.98,19.69,5,15.52,5,11V6.3l7-3.11l7,3.11V11z"/>
                                        <path fill="#6a1b9a" d="M12,7c-2.76,0-5,2.24-5,5s2.24,5,5,5s5-2.24,5-5S14.76,7,12,7z M12,15c-1.65,0-3-1.35-3-3s1.35-3,3-3 s3,1.35,3,3S13.65,15,12,15z"/>
                                    </svg>
                                    {% elif icon_type == 4 %}
                                    <!-- استشارات تقنية -->
                                    <svg class="consulting-icon" width="80" height="80" viewBox="0 0 24 24">
                                        <path fill="#6a1b9a" d="M20,18c1.1,0,2-0.9,2-2V6c0-1.1-0.9-2-2-2H4C2.9,4,2,4.9,2,6v10c0,1.1,0.9,2,2,2H0v2h24v-2H20z M4,6h16v10H4V6z"/>
                                    </svg>
                                    {% else %}
                                    <!-- استشارات عامة -->
                                    <svg class="consulting-icon" width="80" height="80" viewBox="0 0 24 24">
                                        <path fill="#6a1b9a" d="M12,2C6.48,2,2,6.48,2,12s4.48,10,10,10s10-4.48,10-10S17.52,2,12,2z M13,19h-2v-2h2V19z M15.07,11.25l-0.9,0.92 C13.45,12.9,13,13.5,13,15h-2v-0.5c0-1.1,0.45-2.1,1.17-2.83l1.24-1.26c0.37-0.36,0.59-0.86,0.59-1.41c0-1.1-0.9-2-2-2 c-1.1,0-2,0.9-2,2H8c0-2.21,1.79-4,4-4s4,1.79,4,4C16,9.67,15.65,10.6,15.07,11.25z"/>
                                    </svg>
                                    {% endif %}
                                    {% endwith %}
                                </div>
                                {% endif %}
                            </div>
                            <h5 class="card-title wavy-underline">{{ organization.name }}</h5>
                            <p class="card-text small text-muted list-item-hover">{{ organization.description|truncatechars:100 }}</p>
                            <div class="mt-2">
                                <p class="card-text small mb-1"><i class="fas fa-map-marker-alt text-secondary me-1"></i> {{ organization.address }}</p>
                                <p class="card-text small mb-1"><i class="fas fa-envelope text-secondary me-1"></i> {{ organization.email }}</p>
                                <p class="card-text small"><i class="fas fa-phone text-secondary me-1"></i> {{ organization.phone }}</p>
                            </div>
                            
                            <div class="mt-3 d-flex flex-wrap justify-content-center gap-2">
                                <a href="{% url 'organizations:organization_detail' organization.pk %}" class="btn btn-primary btn-sm btn-action btn-3d btn-hover-expand" data-bs-toggle="tooltip" data-bs-placement="top" title="{% trans "عرض تفاصيل المؤسسة" %}">
                                    <i class="fas fa-eye me-1 icon-float"></i> {% trans "عرض التفاصيل" %}
                                </a>

                                <button class="btn btn-warning btn-sm btn-action btn-3d btn-hover-expand edit-org-btn"
                                        data-org-id="{{ organization.pk }}"
                                        data-org-name="{{ organization.name }}"
                                        data-org-description="{{ organization.description }}"
                                        data-org-address="{{ organization.address }}"
                                        data-org-email="{{ organization.email }}"
                                        data-org-phone="{{ organization.phone }}"
                                        data-org-website="{{ organization.website }}"
                                        data-bs-toggle="tooltip" data-bs-placement="top" title="{% trans "تعديل المؤسسة" %}">
                                    <i class="fas fa-edit me-1 icon-float"></i> {% trans "تعديل" %}
                                </button>

                                <button class="btn btn-danger btn-sm btn-action btn-3d btn-hover-expand delete-org-btn"
                                        data-org-id="{{ organization.pk }}"
                                        data-org-name="{{ organization.name }}"
                                        data-bs-toggle="tooltip" data-bs-placement="top" title="{% trans "حذف المؤسسة" %}">
                                    <i class="fas fa-trash me-1 icon-float"></i> {% trans "حذف" %}
                                </button>

                                {% if organization.website %}
                                <a href="{{ organization.website }}" target="_blank" class="btn btn-info btn-sm btn-action btn-3d btn-hover-expand" data-bs-toggle="tooltip" data-bs-placement="top" title="{% trans "زيارة الموقع الرسمي" %}">
                                    <i class="fas fa-globe me-1 icon-float"></i> {% trans "زيارة الموقع" %}
                                </a>
                                {% endif %}

                                <a href="{% url 'organizations:contact_organization' organization.pk %}" class="btn btn-success btn-sm btn-action btn-3d btn-hover-expand" data-bs-toggle="tooltip" data-bs-placement="top" title="{% trans "التواصل مع المؤسسة" %}">
                                    <i class="fas fa-envelope me-1 icon-float"></i> {% trans "اتصل بنا" %}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="alert alert-info glass-effect multi-layer-shadow">
                <i class="fas fa-info-circle me-2 pulse-element"></i> {% trans "لا توجد مؤسسات استشارية مسجلة حتى الآن." %}
            </div>
            {% endif %}
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
// تشغيل الكود فور تحميل الصفحة
$(document).ready(function() {
    console.log('jQuery ready - Script is running');

    // تفعيل tooltips
    $('[data-bs-toggle="tooltip"]').tooltip();

    console.log('Found', $('.edit-org-btn').length, 'edit buttons');
    console.log('Found', $('.delete-org-btn').length, 'delete buttons');

    // وظائف بسيطة للتحديد والعدادات
    function updateCounts() {
        const count = $('input[name="selected_organizations"]:checked').length;
        $('#selected-count').text(count);
        $('#bulk-invite-btn, #bulk-delete-btn').prop('disabled', count === 0);
    }

    // تحديد الكل
    $('#select-all').on('change', function() {
        $('input[name="selected_organizations"]').prop('checked', this.checked);
        updateCounts();
    });

    // تحديث العداد عند التغيير
    $(document).on('change', 'input[name="selected_organizations"]', updateCounts);

    // الدعوات الجماعية
    $('#bulk-invite-btn').on('click', function() {
        const selectedIds = $('input[name="selected_organizations"]:checked').map(function() {
            return this.value;
        }).get();

        if (selectedIds.length > 0) {
            window.location.href = `{% url 'organizations:send_bulk_invitation' %}?ids=${selectedIds.join(',')}`;
        } else {
            alert('يرجى تحديد مؤسسة واحدة على الأقل');
        }
    });

    // الحذف الجماعي
    $('#bulk-delete-btn').on('click', function() {
        const selectedIds = $('input[name="selected_organizations"]:checked').map(function() {
            return this.value;
        }).get();

        if (selectedIds.length > 0 && confirm(`هل أنت متأكد من حذف ${selectedIds.length} مؤسسة؟`)) {
            $.ajax({
                url: '{% url "organizations:bulk_delete_organizations" %}',
                method: 'POST',
                data: JSON.stringify({'organization_ids': selectedIds}),
                contentType: 'application/json',
                headers: {
                    'X-CSRFToken': $('[name=csrfmiddlewaretoken]').val()
                },
                success: function(data) {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert('حدث خطأ أثناء الحذف');
                    }
                },
                error: function() {
                    alert('حدث خطأ أثناء الحذف');
                }
            });
        }
    });

    // تحديث العدادات عند التحميل
    updateCounts();

    // تعديل المؤسسة
    $(document).on('click', '.edit-org-btn', function(e) {
        e.preventDefault();
        console.log('Edit button clicked!');

        const orgId = $(this).data('org-id');
        const orgName = $(this).data('org-name');
        const orgDescription = $(this).data('org-description');
        const orgAddress = $(this).data('org-address');
        const orgEmail = $(this).data('org-email');
        const orgPhone = $(this).data('org-phone');
        const orgWebsite = $(this).data('org-website');

        // ملء النموذج بالبيانات الحالية
        $('#edit-org-id').val(orgId || '');
        $('#edit-org-name').val(orgName || '');
        $('#edit-org-description').val(orgDescription || '');
        $('#edit-org-address').val(orgAddress || '');
        $('#edit-org-email').val(orgEmail || '');
        $('#edit-org-phone').val(orgPhone || '');
        $('#edit-org-website').val(orgWebsite || '');

        // إظهار النموذج
        $('#editOrganizationModal').modal('show');
    });

    // حذف المؤسسة
    $(document).on('click', '.delete-org-btn', function(e) {
        e.preventDefault();
        console.log('Delete button clicked');

        const orgId = $(this).data('org-id');
        const orgName = $(this).data('org-name');

        if (confirm(`هل أنت متأكد من حذف مؤسسة "${orgName}"؟`)) {
            const csrfToken = $('[name=csrfmiddlewaretoken]').val();

            $.ajax({
                url: `{% url "organizations:delete_organization" 0 %}`.replace('0', orgId),
                method: 'POST',
                headers: {
                    'X-CSRFToken': csrfToken
                },
                success: function(data) {
                    if (data.success) {
                        alert(data.message);
                        location.reload();
                    } else {
                        alert(data.message || 'حدث خطأ أثناء الحذف');
                    }
                },
                error: function() {
                    alert('حدث خطأ أثناء الحذف');
                }
            });
        }
    });

    // معالجة نموذج إضافة المؤسسة
    $('#addOrganizationForm').on('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);

        $.ajax({
            url: '{% url "organizations:add_organization" %}',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'X-CSRFToken': $('[name=csrfmiddlewaretoken]').val()
            },
            success: function(data) {
                if (data.success) {
                    alert(data.message);
                    $('#addOrganizationModal').modal('hide');
                    location.reload();
                } else {
                    alert(data.message || 'حدث خطأ أثناء إضافة المؤسسة');
                }
            },
            error: function() {
                alert('حدث خطأ أثناء إضافة المؤسسة');
            }
        });
    });

    // معالجة نموذج تعديل المؤسسة
    $('#editOrganizationForm').on('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);

        $.ajax({
            url: '{% url "organizations:edit_organization" %}',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'X-CSRFToken': $('[name=csrfmiddlewaretoken]').val()
            },
            success: function(data) {
                if (data.success) {
                    alert(data.message);
                    $('#editOrganizationModal').modal('hide');
                    location.reload();
                } else {
                    alert(data.message || 'حدث خطأ أثناء تعديل المؤسسة');
                }
            },
            error: function() {
                alert('حدث خطأ أثناء تعديل المؤسسة');
            }
        });
    });
});
</script>

<!-- Add Organization Modal -->
<div class="modal fade" id="addOrganizationModal" tabindex="-1" aria-labelledby="addOrganizationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addOrganizationModalLabel">
                    <i class="fas fa-plus me-2"></i>إضافة مؤسسة استشارية جديدة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="addOrganizationForm" method="post" action="{% url 'organizations:add_organization' %}" enctype="multipart/form-data">
                {% csrf_token %}
                <input type="hidden" name="organization_type" value="private">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="org-name" class="form-label">اسم المؤسسة *</label>
                            <input type="text" class="form-control" id="org-name" name="name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="org-email" class="form-label">البريد الإلكتروني *</label>
                            <input type="email" class="form-control" id="org-email" name="email" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="org-phone" class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="org-phone" name="phone">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="org-website" class="form-label">الموقع الإلكتروني</label>
                            <input type="url" class="form-control" id="org-website" name="website">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="org-address" class="form-label">العنوان</label>
                        <input type="text" class="form-control" id="org-address" name="address">
                    </div>
                    <div class="mb-3">
                        <label for="org-description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="org-description" name="description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="org-logo" class="form-label">شعار المؤسسة</label>
                        <input type="file" class="form-control" id="org-logo" name="logo" accept="image/*">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save me-1"></i>حفظ المؤسسة
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Organization Modal -->
<div class="modal fade" id="editOrganizationModal" tabindex="-1" aria-labelledby="editOrganizationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editOrganizationModalLabel">
                    <i class="fas fa-edit me-2"></i>تعديل المؤسسة
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editOrganizationForm" method="post" action="{% url 'organizations:edit_organization' %}" enctype="multipart/form-data">
                {% csrf_token %}
                <input type="hidden" id="edit-org-id" name="organization_id">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit-org-name" class="form-label">اسم المؤسسة *</label>
                            <input type="text" class="form-control" id="edit-org-name" name="name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit-org-email" class="form-label">البريد الإلكتروني *</label>
                            <input type="email" class="form-control" id="edit-org-email" name="email" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="edit-org-phone" class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="edit-org-phone" name="phone">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="edit-org-website" class="form-label">الموقع الإلكتروني</label>
                            <input type="url" class="form-control" id="edit-org-website" name="website">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="edit-org-address" class="form-label">العنوان</label>
                        <input type="text" class="form-control" id="edit-org-address" name="address">
                    </div>
                    <div class="mb-3">
                        <label for="edit-org-description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="edit-org-description" name="description" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="edit-org-logo" class="form-label">شعار المؤسسة</label>
                        <input type="file" class="form-control" id="edit-org-logo" name="logo" accept="image/*">
                        <small class="text-muted">اتركه فارغاً للاحتفاظ بالشعار الحالي</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-save me-1"></i>حفظ التغييرات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

{% endblock %}