{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "اتصل بنا - المؤتمر السنوي للسيرة النبوية" %}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<style>
    :root {
        --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        --danger-gradient: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        --info-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        --dark-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        --glass-bg: rgba(255, 255, 255, 0.25);
        --glass-border: rgba(255, 255, 255, 0.18);
        --shadow-light: 0 8px 32px rgba(31, 38, 135, 0.37);
        --shadow-heavy: 0 15px 35px rgba(31, 38, 135, 0.5);
        --border-radius: 20px;
        --transition: all 0.4s cubic-bezier(0.23, 1, 0.320, 1);
    }

    * {
        font-family: 'Poppins', sans-serif;
    }

    body {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
    }

    /* Hero section - تصميم جديد كلياً */
    .hero-contact {
        background: var(--primary-gradient);
        position: relative;
        overflow: hidden;
        padding: 8rem 0 4rem;
        margin-bottom: 0;
    }

    .hero-contact::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
        animation: float 20s ease-in-out infinite;
    }

    .hero-contact::after {
        content: '';
        position: absolute;
        bottom: -2px;
        left: 0;
        width: 100%;
        height: 100px;
        background: linear-gradient(to top, #f5f7fa, transparent);
        clip-path: polygon(0 100%, 100% 100%, 100% 0, 0 80%);
    }

    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-10px) rotate(2deg); }
    }
    
    .hero-title {
        font-size: 3.5rem;
        font-weight: 700;
        margin-bottom: 1.5rem;
        text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        position: relative;
        z-index: 2;
    }

    .hero-subtitle {
        font-size: 1.3rem;
        font-weight: 300;
        opacity: 0.9;
        margin-bottom: 2rem;
        position: relative;
        z-index: 2;
    }

    .hero-icon {
        font-size: 4rem;
        margin-bottom: 2rem;
        opacity: 0.8;
        position: relative;
        z-index: 2;
        animation: bounce 2s infinite;
    }

    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
        40% { transform: translateY(-10px); }
        60% { transform: translateY(-5px); }
    }

    /* إحصائيات الهيرو */
    .hero-stats {
        margin-top: 3rem;
        position: relative;
        z-index: 2;
    }

    .hero-stats .stat-item {
        background: rgba(255, 255, 255, 0.15);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 1.5rem 1rem;
        text-align: center;
        transition: var(--transition);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .hero-stats .stat-item:hover {
        background: rgba(255, 255, 255, 0.25);
        transform: translateY(-5px);
    }

    .hero-stats .stat-number {
        font-size: 2rem;
        font-weight: 700;
        display: block;
        margin-bottom: 0.5rem;
        color: white;
    }

    .hero-stats .stat-label {
        font-size: 0.9rem;
        opacity: 0.9;
        color: white;
        font-weight: 500;
    }
    
    /* بطاقات جديدة بتصميم Glass Morphism */
    .glass-card {
        background: var(--glass-bg);
        backdrop-filter: blur(16px);
        -webkit-backdrop-filter: blur(16px);
        border-radius: var(--border-radius);
        border: 1px solid var(--glass-border);
        box-shadow: var(--shadow-light);
        transition: var(--transition);
        position: relative;
        overflow: hidden;
    }

    .glass-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 1px;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    }

    .glass-card:hover {
        transform: translateY(-10px);
        box-shadow: var(--shadow-heavy);
        border-color: rgba(255, 255, 255, 0.3);
    }

    .contact-info-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
    }
    
    /* عناصر المعلومات الجديدة */
    .info-item {
        padding: 2rem;
        text-align: center;
        position: relative;
        transition: var(--transition);
    }

    .info-item:hover {
        transform: scale(1.05);
    }

    .info-icon {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem;
        font-size: 2rem;
        color: white;
        position: relative;
        overflow: hidden;
    }

    .info-icon::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        transition: all 0.6s ease;
    }

    .info-item:hover .info-icon::before {
        left: 100%;
    }

    .info-title {
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: #2c3e50;
    }

    .info-text {
        color: #6c757d;
        line-height: 1.6;
        margin: 0;
    }

    .info-text a {
        color: #667eea;
        text-decoration: none;
        font-weight: 500;
        transition: var(--transition);
    }

    .info-text a:hover {
        color: #764ba2;
        text-decoration: underline;
    }
    
    /* تصميم النموذج الجديد */
    .contact-form {
        background: var(--glass-bg);
        backdrop-filter: blur(16px);
        border-radius: var(--border-radius);
        border: 1px solid var(--glass-border);
        padding: 3rem;
        box-shadow: var(--shadow-light);
    }

    .form-floating {
        position: relative;
        margin-bottom: 1.5rem;
    }

    .form-control {
        background: rgba(255, 255, 255, 0.8);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 15px;
        padding: 1rem 1.5rem;
        font-size: 1rem;
        transition: var(--transition);
        backdrop-filter: blur(8px);
    }

    .form-control:focus {
        background: rgba(255, 255, 255, 0.95);
        border-color: #667eea;
        box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.15);
        transform: translateY(-2px);
        outline: none;
    }

    .form-control::placeholder {
        color: rgba(108, 117, 125, 0.7);
    }

    .form-label {
        font-weight: 600;
        color: #2c3e50;
        margin-bottom: 0.5rem;
        font-size: 0.95rem;
    }

    /* أزرار جديدة بتصميم متطور */
    .btn-modern {
        background: var(--primary-gradient);
        border: none;
        border-radius: 50px;
        padding: 15px 40px;
        font-weight: 600;
        font-size: 1.1rem;
        color: white;
        text-transform: uppercase;
        letter-spacing: 1px;
        transition: var(--transition);
        position: relative;
        overflow: hidden;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }

    .btn-modern::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        transition: all 0.6s ease;
    }

    .btn-modern:hover::before {
        left: 100%;
    }

    .btn-modern:hover {
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
        color: white;
    }

    .btn-modern:active {
        transform: translateY(-1px) scale(1.02);
    }
    
    .input-group-text {
        background-color: #f8f9fa;
        border-width: 2px;
        border-color: #ced4da;
        color: #6c757d;
    }
    
    /* Contact info styles */
    .contact-info-card {
        border: none;
        border-radius: 1.5rem;
        overflow: hidden;
        box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.1);
        transition: transform 0.4s ease, box-shadow 0.4s ease;
        height: 100%;
    }
    
    .contact-info-card:hover {
        transform: translateY(-8px);
        box-shadow: 0 1.5rem 4rem rgba(0, 0, 0, 0.15);
    }
    
    .contact-info-item {
        text-align: center;
        transition: all 0.3s ease;
        padding: 1.5rem;
        border-radius: 1rem;
    }
    
    .contact-info-item:hover {
        background-color: rgba(57, 73, 171, 0.05);
    }
    
    .contact-info-icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 70px;
        height: 70px;
        margin: 0 auto;
        transition: all 0.4s ease;
        background-color: rgba(57, 73, 171, 0.1) !important;
        color: #3949ab !important;
    }
    
    .contact-info-item:hover .contact-info-icon {
        background-color: #3949ab !important;
        color: white !important;
        transform: scale(1.1) rotate(10deg);
        box-shadow: 0 0.5rem 1rem rgba(57, 73, 171, 0.3);
    }
    
    .contact-info-title {
        font-weight: 700;
        margin: 1rem 0 0.5rem;
        text-align: center;
        color: #3949ab;
    }
    
    .contact-info-text {
        text-align: center;
        color: #6c757d;
        font-size: 1.1rem;
    }
    
    .social-icons .btn {
        width: 45px;
        height: 45px;
        padding: 0;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        transition: all 0.4s ease;
        border-radius: 50%;
        margin: 0 0.5rem;
        background-color: rgba(57, 73, 171, 0.1);
        color: #3949ab;
        border: none;
    }
    
    /* وسائل التواصل الاجتماعي المحسنة */
    .social-links {
        display: flex;
        justify-content: center;
        gap: 1rem;
    }

    .social-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 45px;
        height: 45px;
        border-radius: 50%;
        text-decoration: none;
        transition: var(--transition);
        position: relative;
        overflow: hidden;
        font-size: 1.1rem;
        border: 2px solid transparent;
    }

    .social-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 50%;
        transform: scale(0);
        transition: all 0.4s ease;
        z-index: -1;
    }

    .social-btn:hover::before {
        transform: scale(1);
    }

    .social-btn.twitter {
        background: rgba(29, 161, 242, 0.1);
        color: #1da1f2;
        border-color: rgba(29, 161, 242, 0.3);
    }

    .social-btn.twitter::before {
        background: #1da1f2;
    }

    .social-btn.twitter:hover {
        color: white;
        transform: translateY(-3px) scale(1.1);
        box-shadow: 0 8px 20px rgba(29, 161, 242, 0.4);
    }

    .social-btn.facebook {
        background: rgba(24, 119, 242, 0.1);
        color: #1877f2;
        border-color: rgba(24, 119, 242, 0.3);
    }

    .social-btn.facebook::before {
        background: #1877f2;
    }

    .social-btn.facebook:hover {
        color: white;
        transform: translateY(-3px) scale(1.1);
        box-shadow: 0 8px 20px rgba(24, 119, 242, 0.4);
    }

    .social-btn.instagram {
        background: rgba(225, 48, 108, 0.1);
        color: #e1306c;
        border-color: rgba(225, 48, 108, 0.3);
    }

    .social-btn.instagram::before {
        background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
    }

    .social-btn.instagram:hover {
        color: white;
        transform: translateY(-3px) scale(1.1);
        box-shadow: 0 8px 20px rgba(225, 48, 108, 0.4);
    }

    .social-btn.youtube {
        background: rgba(255, 0, 0, 0.1);
        color: #ff0000;
        border-color: rgba(255, 0, 0, 0.3);
    }

    .social-btn.youtube::before {
        background: #ff0000;
    }

    .social-btn.youtube:hover {
        color: white;
        transform: translateY(-3px) scale(1.1);
        box-shadow: 0 8px 20px rgba(255, 0, 0, 0.4);
    }

    /* تحسين الأسئلة الشائعة */
    .accordion-item {
        border: none;
        border-radius: 15px !important;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        background: white;
        overflow: hidden;
        transition: all 0.3s ease;
    }

    .accordion-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.12);
    }

    .accordion-button {
        background: white;
        border: none;
        padding: 20px 25px;
        font-weight: 600;
        font-size: 1.1rem;
        border-radius: 15px !important;
        transition: all 0.3s ease;
    }

    .accordion-button:not(.collapsed) {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        box-shadow: none;
    }

    .accordion-button:focus {
        box-shadow: none;
        border: none;
    }

    .accordion-button::after {
        transition: all 0.3s ease;
    }

    .accordion-body {
        padding: 25px;
        background: rgba(102, 126, 234, 0.02);
        font-size: 1.05rem;
        line-height: 1.7;
        color: #555;
    }

    /* عنوان القسم */
    .section-title {
        font-size: 2.2rem;
        font-weight: 700;
        margin-bottom: 1rem;
        background: var(--primary-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        position: relative;
        display: inline-block;
    }

    .section-title::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 60px;
        height: 3px;
        background: var(--primary-gradient);
        border-radius: 2px;
    }

    /* تحسينات إضافية */
    .text-muted {
        color: #6c757d !important;
        font-size: 1.05rem;
    }

    /* تحسين الرسائل */
    .alert {
        border-radius: 15px;
        border: none;
        padding: 1rem 1.5rem;
        margin-bottom: 1.5rem;
        backdrop-filter: blur(10px);
    }

    .alert-success {
        background: rgba(40, 167, 69, 0.1);
        color: #155724;
        border-left: 4px solid #28a745;
    }

    .alert-error {
        background: rgba(220, 53, 69, 0.1);
        color: #721c24;
        border-left: 4px solid #dc3545;
    }

    /* تحسين التحقق من صحة النماذج */
    .invalid-feedback {
        display: block;
        width: 100%;
        margin-top: 0.25rem;
        font-size: 0.875rem;
        color: #dc3545;
        font-weight: 500;
    }

    .form-control.is-invalid {
        border-color: #dc3545;
        background: rgba(220, 53, 69, 0.05);
    }

    .form-control.is-valid {
        border-color: #28a745;
        background: rgba(40, 167, 69, 0.05);
    }

    /* تحسين الاستجابة */
    @media (max-width: 768px) {
        .hero-title {
            font-size: 2.5rem;
        }

        .hero-subtitle {
            font-size: 1.1rem;
        }

        .contact-info-grid {
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }

        .hero-stats .row {
            gap: 1rem;
        }

        .contact-form {
            padding: 2rem;
        }

        .section-title {
            font-size: 1.8rem;
        }
    }

    @media (max-width: 576px) {
        .hero-contact {
            padding: 4rem 0 2rem;
        }

        .hero-title {
            font-size: 2rem;
        }

        .info-icon {
            width: 60px;
            height: 60px;
            font-size: 1.5rem;
        }

        .social-btn {
            width: 40px;
            height: 40px;
            font-size: 1rem;
        }
    }
    
    /* تصميم الخريطة المحسن */
    .map-container {
        position: relative;
        border-radius: var(--border-radius);
        overflow: hidden;
        box-shadow: var(--shadow-light);
        transition: var(--transition);
    }

    .map-container:hover {
        box-shadow: var(--shadow-heavy);
        transform: translateY(-5px);
    }

    #contact-map {
        height: 400px;
        width: 100%;
        border: none;
        position: relative;
        z-index: 1;
        border-radius: 1rem;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 400px;
    }

    /* تحسين نافذة معلومات الخريطة */
    .gm-style-iw {
        border-radius: 10px !important;
        box-shadow: 0 5px 15px rgba(0,0,0,0.2) !important;
    }

    .gm-style-iw-d {
        overflow: hidden !important;
    }

    .map-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: var(--primary-gradient);
        opacity: 0.1;
        z-index: 2;
        pointer-events: none;
        transition: var(--transition);
    }

    .map-container:hover::before {
        opacity: 0.05;
    }
    
    /* Animation styles */
    .fade-in {
        animation: fadeIn 1s ease-in-out;
    }
    
    .slide-up {
        animation: slideUp 0.8s ease-in-out;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }
    
    @keyframes slideUp {
        from { transform: translateY(30px); opacity: 0; }
        to { transform: translateY(0); opacity: 1; }
    }

    @keyframes bounce {
        0%, 20%, 50%, 80%, 100% {
            transform: translateY(0);
        }
        40% {
            transform: translateY(-10px);
        }
        60% {
            transform: translateY(-5px);
        }
    }

    @keyframes pulse {
        0% {
            transform: translate(-50%, -50%) scale(0.8);
            opacity: 0.8;
        }
        50% {
            transform: translate(-50%, -50%) scale(1.2);
            opacity: 0.3;
        }
        100% {
            transform: translate(-50%, -50%) scale(1.5);
            opacity: 0;
        }
    }

    /* تحسين تأثيرات الأزرار */
    .map-actions .btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.3) !important;
    }

    .map-actions .btn-outline-light:hover {
        background: white;
        color: #667eea;
    }

    /* تحسين الخريطة التفاعلية */
    #interactive-map {
        opacity: 0;
        transform: translateY(30px);
        transition: all 0.8s ease;
    }

    #interactive-map.animate {
        opacity: 1;
        transform: translateY(0);
    }

    /* تحسين استجابة الخريطة */
    @media (max-width: 768px) {
        #interactive-map .location-details h2 {
            font-size: 2rem !important;
        }

        #interactive-map .location-details h3 {
            font-size: 1.5rem !important;
        }

        #interactive-map .location-details p {
            font-size: 1.1rem !important;
        }

        #interactive-map .contact-quick-info {
            flex-direction: column !important;
            gap: 15px !important;
        }

        #interactive-map .map-actions {
            flex-direction: column !important;
            gap: 10px !important;
        }

        #interactive-map .map-actions .btn {
            width: 100%;
            max-width: 250px;
        }
    }
    
    /* Button styles */
    .btn-primary {
        background: linear-gradient(135deg, #3949ab 0%, #1a237e 100%);
        border: none;
        border-radius: 0.75rem;
        font-weight: 600;
        padding: 0.75rem 1.5rem;
        transition: all 0.4s ease;
    }
    
    .btn-primary:hover {
        transform: translateY(-5px);
        box-shadow: 0 0.8rem 1.5rem rgba(0, 0, 0, 0.2);
        background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
    }
    
    /* Section styles */
    .section-title {
        position: relative;
        display: inline-block;
        margin-bottom: 1.5rem;
        font-weight: 700;
        color: #3949ab;
    }
    
    .section-title::after {
        content: '';
        position: absolute;
        bottom: -10px;
        left: 50%;
        transform: translateX(-50%);
        width: 50px;
        height: 3px;
        background: linear-gradient(135deg, #3949ab 0%, #1a237e 100%);
        border-radius: 3px;
    }
    
    /* Responsive Adjustments */
    @media (max-width: 768px) {
        .hero-mini {
            padding: 3rem 0;
        }
        
        .contact-info-icon {
            width: 70px;
            height: 70px;
        }
        
        #contact-map {
            height: 300px;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section الجديد -->
<section class="hero-contact">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto text-center">
                <div class="hero-icon">
                    <i class="fas fa-envelope-open"></i>
                </div>
                <h1 class="hero-title">{% trans "تواصل معنا" %}</h1>
                <p class="hero-subtitle">
                    {% trans "نحن هنا للإجابة على جميع استفساراتكم حول المؤتمر السنوي للسيرة النبوية" %}
                </p>
                <div class="hero-stats">
                    <div class="row g-4 justify-content-center">
                        <div class="col-md-3 col-6">
                            <div class="stat-item">
                                <div class="stat-number">24/7</div>
                                <div class="stat-label">{% trans "دعم متواصل" %}</div>
                            </div>
                        </div>
                        <div class="col-md-3 col-6">
                            <div class="stat-item">
                                <div class="stat-number">< 24h</div>
                                <div class="stat-label">{% trans "وقت الاستجابة" %}</div>
                            </div>
                        </div>
                        <div class="col-md-3 col-6">
                            <div class="stat-item">
                                <div class="stat-number">50+</div>
                                <div class="stat-label">{% trans "دولة" %}</div>
                            </div>
                        </div>
                        <div class="col-md-3 col-6">
                            <div class="stat-item">
                                <div class="stat-number">15+</div>
                                <div class="stat-label">{% trans "دورة" %}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Map Section -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-4 fade-in">
                <h2 class="section-title">{% trans "موقعنا" %}</h2>
                <p class="text-muted">{% trans "يمكنك زيارتنا في المكان التالي" %}</p>
            </div>
            <div class="col-12 slide-up">
                <div class="map-container">
                    <!-- خريطة تفاعلية مع معلومات الموقع -->
                    <div id="interactive-map" style="height: 400px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                               border-radius: 15px; position: relative; overflow: hidden; box-shadow: 0 10px 30px rgba(0,0,0,0.2);">

                        <!-- خلفية الخريطة -->
                        <div class="map-background" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0;
                                   background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 400 400\"><defs><pattern id=\"mapgrid\" width=\"20\" height=\"20\" patternUnits=\"userSpaceOnUse\"><path d=\"M 20 0 L 0 0 0 20\" fill=\"none\" stroke=\"rgba(255,255,255,0.1)\" stroke-width=\"1\"/></pattern></defs><rect width=\"400\" height=\"400\" fill=\"url(%23mapgrid)\"/></svg>');
                                   opacity: 0.3;"></div>

                        <!-- محتوى الخريطة -->
                        <div class="map-content" style="position: relative; z-index: 2; height: 100%; display: flex; align-items: center; justify-content: center; text-align: center; color: white; padding: 40px;">
                            <div class="location-info">
                                <!-- أيقونة الموقع المتحركة -->
                                <div class="location-marker" style="position: relative; margin-bottom: 30px;">
                                    <i class="fas fa-map-marker-alt" style="font-size: 5rem; color: #ff4757;
                                       text-shadow: 0 0 20px rgba(255, 71, 87, 0.5);
                                       animation: bounce 2s infinite;"></i>
                                    <div class="marker-pulse" style="position: absolute; top: 50%; left: 50%;
                                               transform: translate(-50%, -50%); width: 80px; height: 80px;
                                               border: 2px solid #ff4757; border-radius: 50%;
                                               animation: pulse 2s infinite; opacity: 0.6;"></div>
                                </div>

                                <!-- معلومات الموقع -->
                                <div class="location-details">
                                    <h2 style="font-size: 2.5rem; margin-bottom: 15px; font-weight: bold;
                                              text-shadow: 0 2px 10px rgba(0,0,0,0.3);">
                                        <i class="fas fa-mosque me-3"></i>
                                        موقعنا
                                    </h2>
                                    <h3 style="font-size: 1.8rem; margin-bottom: 20px; color: #ffd700;
                                              text-shadow: 0 2px 5px rgba(0,0,0,0.3);">
                                        التجمع الثقافي الإسلامي
                                    </h3>
                                    <p style="font-size: 1.3rem; margin-bottom: 25px; opacity: 0.9;
                                             text-shadow: 0 1px 3px rgba(0,0,0,0.3);">
                                        <i class="fas fa-city me-2"></i>
                                        نواكشوط - موريتانيا
                                    </p>

                                    <!-- معلومات إضافية -->
                                    <div class="contact-quick-info" style="display: flex; justify-content: center; gap: 30px; margin-bottom: 30px; flex-wrap: wrap;">
                                        <div class="info-item" style="text-align: center;">
                                            <i class="fas fa-phone" style="font-size: 1.5rem; color: #4caf50; margin-bottom: 8px; display: block;"></i>
                                            <span style="font-size: 1.1rem;">+222 32 81 67 79</span>
                                        </div>
                                        <div class="info-item" style="text-align: center;">
                                            <i class="fas fa-envelope" style="font-size: 1.5rem; color: #2196f3; margin-bottom: 8px; display: block;"></i>
                                            <span style="font-size: 1.1rem;"><EMAIL></span>
                                        </div>
                                    </div>

                                    <!-- أزرار التفاعل -->
                                    <div class="map-actions" style="display: flex; justify-content: center; gap: 15px; flex-wrap: wrap;">
                                        <a href="https://maps.google.com/?q=18.0735,-15.9582" target="_blank"
                                           class="btn btn-light btn-lg" style="border-radius: 25px; padding: 12px 25px;
                                                  font-weight: bold; box-shadow: 0 5px 15px rgba(0,0,0,0.2);
                                                  transition: all 0.3s ease;">
                                            <i class="fab fa-google me-2"></i>
                                            خرائط جوجل
                                        </a>
                                        <a href="https://www.openstreetmap.org/?mlat=18.0735&mlon=-15.9582&zoom=15" target="_blank"
                                           class="btn btn-outline-light btn-lg" style="border-radius: 25px; padding: 12px 25px;
                                                  font-weight: bold; border: 2px solid white;
                                                  transition: all 0.3s ease;">
                                            <i class="fas fa-map me-2"></i>
                                            OpenStreetMap
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تأثيرات بصرية إضافية -->
                        <div class="map-overlay" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0;
                                   background: radial-gradient(circle at center, transparent 30%, rgba(0,0,0,0.1) 100%);
                                   pointer-events: none;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- قسم معلومات الاتصال الجديد -->
<section class="py-5">
    <div class="container">
        <!-- معلومات الاتصال في شبكة -->
        <div class="contact-info-grid">
            <!-- العنوان -->
            <div class="glass-card">
                <div class="info-item">
                    <div class="info-icon" style="background: var(--danger-gradient);">
                        <i class="fas fa-map-marker-alt"></i>
                    </div>
                    <h4 class="info-title">{% trans "العنوان" %}</h4>
                    <p class="info-text">
                        <strong>{% trans "التجمع الثقافي الإسلامي" %}</strong><br>
                        نواكشوط - موريتانيا<br>
                        <small class="text-muted">{% trans "المقر الرئيسي للمؤتمر" %}</small>
                    </p>
                </div>
            </div>

            <!-- البريد الإلكتروني -->
            <div class="glass-card">
                <div class="info-item">
                    <div class="info-icon" style="background: var(--info-gradient);">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <h4 class="info-title">{% trans "البريد الإلكتروني" %}</h4>
                    <p class="info-text">
                        <a href="mailto:<EMAIL>">
                            <strong><EMAIL></strong>
                        </a><br>
                        <small class="text-muted">{% trans "للاستفسارات العامة" %}</small>
                    </p>
                </div>
            </div>

            <!-- الهاتف -->
            <div class="glass-card">
                <div class="info-item">
                    <div class="info-icon" style="background: var(--success-gradient);">
                        <i class="fas fa-phone"></i>
                    </div>
                    <h4 class="info-title">{% trans "الهاتف" %}</h4>
                    <p class="info-text">
                        <a href="tel:+22232816779">
                            <strong>+222 32 81 67 79</strong>
                        </a><br>
                        <small class="text-muted">{% trans "خط مباشر للمؤتمر" %}</small>
                    </p>
                </div>
            </div>

            <!-- ساعات العمل -->
            <div class="glass-card">
                <div class="info-item">
                    <div class="info-icon" style="background: var(--warning-gradient);">
                        <i class="fas fa-clock"></i>
                    </div>
                    <h4 class="info-title">{% trans "ساعات العمل" %}</h4>
                    <p class="info-text">
                        <strong>{% trans "الأحد - الخميس" %}</strong><br>
                        8:00 ص - 4:00 م<br>
                        <small class="text-muted">{% trans "بتوقيت نواكشوط" %}</small>
                    </p>
                </div>
            </div>

            <!-- موعد المؤتمر -->
            <div class="glass-card">
                <div class="info-item">
                    <div class="info-icon" style="background: var(--secondary-gradient);">
                        <i class="fas fa-calendar-alt"></i>
                    </div>
                    <h4 class="info-title">{% trans "المؤتمر القادم" %}</h4>
                    <p class="info-text">
                        <strong>{% trans "ربيع الأول 1446 هـ" %}</strong><br>
                        <small class="text-muted">{% trans "سبتمبر 2024 م" %}</small>
                    </p>
                </div>
            </div>

            <!-- وسائل التواصل -->
            <div class="glass-card">
                <div class="info-item">
                    <div class="info-icon" style="background: var(--dark-gradient);">
                        <i class="fas fa-share-alt"></i>
                    </div>
                    <h4 class="info-title">{% trans "تابعنا" %}</h4>
                    <div class="social-links mt-3">
                        <a href="#" class="social-btn twitter me-2" title="{% trans 'تويتر' %}">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="social-btn facebook me-2" title="{% trans 'فيسبوك' %}">
                            <i class="fab fa-facebook-f"></i>
                        </a>
                        <a href="#" class="social-btn instagram me-2" title="{% trans 'انستغرام' %}">
                            <i class="fab fa-instagram"></i>
                        </a>
                        <a href="#" class="social-btn youtube" title="{% trans 'يوتيوب' %}">
                            <i class="fab fa-youtube"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- نموذج الاتصال -->
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="contact-form">
                    <div class="text-center mb-4">
                        <h2 class="section-title">
                            <i class="fas fa-paper-plane me-3"></i>
                            {% trans "أرسل لنا رسالة" %}
                        </h2>
                        <p class="text-muted">{% trans "سنقوم بالرد عليك في أقرب وقت ممكن" %}</p>
                    </div>
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                <i class="fas fa-check-circle me-2"></i>{{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <form method="post" class="needs-validation" novalidate>
                        {% csrf_token %}

                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="text" class="form-control" id="id_name" name="name" placeholder="{% trans 'الاسم الكامل' %}" required>
                                    <label for="id_name">
                                        <i class="fas fa-user me-2"></i>{% trans "الاسم الكامل" %}
                                    </label>
                                    <div class="invalid-feedback">
                                        <i class="fas fa-exclamation-triangle me-1"></i>{% trans "يرجى إدخال اسمك الكامل" %}
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="email" class="form-control" id="id_email" name="email" placeholder="{% trans 'البريد الإلكتروني' %}" required>
                                    <label for="id_email">
                                        <i class="fas fa-envelope me-2"></i>{% trans "البريد الإلكتروني" %}
                                    </label>
                                    <div class="invalid-feedback">
                                        <i class="fas fa-exclamation-triangle me-1"></i>{% trans "يرجى إدخال بريد إلكتروني صحيح" %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-floating">
                            <input type="text" class="form-control" id="id_subject" name="subject" placeholder="{% trans 'موضوع الرسالة' %}" required>
                            <label for="id_subject">
                                <i class="fas fa-tag me-2"></i>{% trans "موضوع الرسالة" %}
                            </label>
                            <div class="invalid-feedback">
                                <i class="fas fa-exclamation-triangle me-1"></i>{% trans "يرجى إدخال موضوع الرسالة" %}
                            </div>
                        </div>

                        <div class="form-floating">
                            <textarea class="form-control" id="id_message" name="message" placeholder="{% trans 'نص الرسالة' %}" style="height: 150px;" required></textarea>
                            <label for="id_message">
                                <i class="fas fa-comment-alt me-2"></i>{% trans "نص الرسالة" %}
                            </label>
                            <div class="invalid-feedback">
                                <i class="fas fa-exclamation-triangle me-1"></i>{% trans "يرجى كتابة رسالتك" %}
                            </div>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="btn-modern">
                                <i class="fas fa-paper-plane me-2"></i>
                                {% trans "إرسال الرسالة" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- قسم الخريطة -->
<section class="py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center mb-4">
                <h2 class="section-title">
                    <i class="fas fa-map-marked-alt me-3"></i>
                    {% trans "موقعنا على الخريطة" %}
                </h2>
                <p class="text-muted">{% trans "يمكنك زيارتنا في المكان التالي" %}</p>
            </div>
            <div class="col-12 slide-up">
                <div class="map-container">
                    <!-- خريطة تفاعلية مع معلومات الموقع -->
                    <div id="interactive-map" style="height: 400px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                               border-radius: 15px; position: relative; overflow: hidden; box-shadow: 0 10px 30px rgba(0,0,0,0.2);">

                        <!-- خلفية الخريطة -->
                        <div class="map-background" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0;
                                   background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 400 400\"><defs><pattern id=\"mapgrid\" width=\"20\" height=\"20\" patternUnits=\"userSpaceOnUse\"><path d=\"M 20 0 L 0 0 0 20\" fill=\"none\" stroke=\"rgba(255,255,255,0.1)\" stroke-width=\"1\"/></pattern></defs><rect width=\"400\" height=\"400\" fill=\"url(%23mapgrid)\"/></svg>');
                                   opacity: 0.3;"></div>

                        <!-- محتوى الخريطة -->
                        <div class="map-content" style="position: relative; z-index: 2; height: 100%; display: flex; align-items: center; justify-content: center; text-align: center; color: white; padding: 40px;">
                            <div class="location-info">
                                <!-- أيقونة الموقع المتحركة -->
                                <div class="location-marker" style="position: relative; margin-bottom: 30px;">
                                    <i class="fas fa-map-marker-alt" style="font-size: 5rem; color: #ff4757;
                                       text-shadow: 0 0 20px rgba(255, 71, 87, 0.5);
                                       animation: bounce 2s infinite;"></i>
                                    <div class="marker-pulse" style="position: absolute; top: 50%; left: 50%;
                                               transform: translate(-50%, -50%); width: 80px; height: 80px;
                                               border: 2px solid #ff4757; border-radius: 50%;
                                               animation: pulse 2s infinite; opacity: 0.6;"></div>
                                </div>

                                <!-- معلومات الموقع -->
                                <div class="location-details">
                                    <h2 style="font-size: 2.5rem; margin-bottom: 15px; font-weight: bold;
                                              text-shadow: 0 2px 10px rgba(0,0,0,0.3);">
                                        <i class="fas fa-mosque me-3"></i>
                                        موقعنا
                                    </h2>
                                    <h3 style="font-size: 1.8rem; margin-bottom: 20px; color: #ffd700;
                                              text-shadow: 0 2px 5px rgba(0,0,0,0.3);">
                                        التجمع الثقافي الإسلامي
                                    </h3>
                                    <p style="font-size: 1.3rem; margin-bottom: 25px; opacity: 0.9;
                                             text-shadow: 0 1px 3px rgba(0,0,0,0.3);">
                                        <i class="fas fa-city me-2"></i>
                                        نواكشوط - موريتانيا
                                    </p>

                                    <!-- معلومات إضافية -->
                                    <div class="contact-quick-info" style="display: flex; justify-content: center; gap: 30px; margin-bottom: 30px; flex-wrap: wrap;">
                                        <div class="info-item" style="text-align: center;">
                                            <i class="fas fa-phone" style="font-size: 1.5rem; color: #4caf50; margin-bottom: 8px; display: block;"></i>
                                            <span style="font-size: 1.1rem;">+222 32 81 67 79</span>
                                        </div>
                                        <div class="info-item" style="text-align: center;">
                                            <i class="fas fa-envelope" style="font-size: 1.5rem; color: #2196f3; margin-bottom: 8px; display: block;"></i>
                                            <span style="font-size: 1.1rem;"><EMAIL></span>
                                        </div>
                                    </div>

                                    <!-- أزرار التفاعل -->
                                    <div class="map-actions" style="display: flex; justify-content: center; gap: 15px; flex-wrap: wrap;">
                                        <a href="https://maps.google.com/?q=18.0735,-15.9582" target="_blank"
                                           class="btn btn-light btn-lg" style="border-radius: 25px; padding: 12px 25px;
                                                  font-weight: bold; box-shadow: 0 5px 15px rgba(0,0,0,0.2);
                                                  transition: all 0.3s ease;">
                                            <i class="fab fa-google me-2"></i>
                                            خرائط جوجل
                                        </a>
                                        <a href="https://www.openstreetmap.org/?mlat=18.0735&mlon=-15.9582&zoom=15" target="_blank"
                                           class="btn btn-outline-light btn-lg" style="border-radius: 25px; padding: 12px 25px;
                                                  font-weight: bold; border: 2px solid white;
                                                  transition: all 0.3s ease;">
                                            <i class="fas fa-map me-2"></i>
                                            OpenStreetMap
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- تأثيرات بصرية إضافية -->
                        <div class="map-overlay" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0;
                                   background: radial-gradient(circle at center, transparent 30%, rgba(0,0,0,0.1) 100%);
                                   pointer-events: none;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تأثيرات الحركة للخريطة
        const mapContainer = document.getElementById('interactive-map');
        if (mapContainer) {
            // تأثير الظهور التدريجي
            setTimeout(() => {
                mapContainer.style.opacity = '1';
                mapContainer.style.transform = 'translateY(0)';
            }, 500);
        }
    });

    // تحقق من صحة النماذج
    (function() {
        'use strict';
        var forms = document.querySelectorAll('.needs-validation');

        Array.prototype.slice.call(forms).forEach(function(form) {
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                } else {
                    const submitBtn = form.querySelector('button[type="submit"]');
                    const originalText = submitBtn.innerHTML;
                    submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span> {% trans "جاري الإرسال..." %}';
                    submitBtn.disabled = true;

                    setTimeout(() => {
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    }, 3000);
                }

                form.classList.add('was-validated');
            }, false);
        });
    })();

    // تفعيل التلميحات
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // تأثيرات الحركة للعناصر
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    document.querySelectorAll('.glass-card, .slide-up').forEach(element => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(30px)';
        element.style.transition = 'all 0.6s ease';
        observer.observe(element);
    });

    // تأثير خاص للخريطة التفاعلية
    const interactiveMap = document.getElementById('interactive-map');
    if (interactiveMap) {
        observer.observe(interactiveMap);
    }

});
</script>

{% endblock %}
