{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "مؤتمر السيرة النبوية السنوي" %}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/hero-banner.css' %}">
<link rel="stylesheet" href="{% static 'css/fullscreen-mosque.css' %}">
<link rel="stylesheet" href="{% static 'css/modern-theme.css' %}">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
<style>
  /* تنسيقات إضافية للصفحة الرئيسية */
  .feature-icon {
    font-size: 3.5rem;
    background: linear-gradient(45deg, #3498db, #9b59b6);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    margin-bottom: 1rem;
    filter: drop-shadow(0 0 5px rgba(52, 152, 219, 0.3));
  }

  .highlight-card {
    border-top: 4px solid #3498db;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
  }

  .highlight-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  }

  .conference-quote {
    font-style: italic;
    font-size: 1.2rem;
    padding: 2rem;
    border-right: 4px solid #3498db;
    background: rgba(52, 152, 219, 0.05);
    margin: 2rem 0;
    border-radius: 0 10px 10px 0;
  }

  .timeline-item {
    position: relative;
    padding-right: 30px;
    margin-bottom: 1.5rem;
    text-align: right;
  }

  .timeline-item::before {
    content: '';
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    width: 3px;
    background: linear-gradient(to bottom, #3498db, #9b59b6);
    border-radius: 3px;
  }

  .timeline-item::after {
    content: '';
    position: absolute;
    right: -5px;
    top: 10px;
    height: 12px;
    width: 12px;
    border-radius: 50%;
    background: #3498db;
    box-shadow: 0 0 10px rgba(52, 152, 219, 0.5);
  }

  /* تحسين دعم RTL */
  [dir="rtl"] .card-body {
    text-align: right;
  }

  [dir="rtl"] .conference-quote {
    border-right: 4px solid #3498db;
    border-left: none;
    border-radius: 0 10px 10px 0;
    text-align: right;
  }

  /* تأثيرات إضافية جديدة */
  .floating-element {
    animation: float 3s ease-in-out infinite;
  }

  @keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0px); }
  }

  .gradient-text {
    background: linear-gradient(90deg, #3498db, #9b59b6, #e74c3c);
    background-size: 200% auto;
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: gradient-text 5s linear infinite;
  }

  @keyframes gradient-text {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
  }

  .card-shine {
    position: relative;
    overflow: hidden;
  }

  .card-shine::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(60deg, transparent, rgba(255,255,255,0.2), transparent);
    transform: rotate(30deg);
    animation: shine 6s linear infinite;
  }

  @keyframes shine {
    0% { transform: translateX(-100%) rotate(30deg); }
    100% { transform: translateX(100%) rotate(30deg); }
  }

  .shadow-pulse {
    animation: shadow-pulse 3s infinite;
  }

  @keyframes shadow-pulse {
    0% { box-shadow: 0 0 10px rgba(52, 152, 219, 0.5); }
    50% { box-shadow: 0 0 20px rgba(52, 152, 219, 0.8); }
    100% { box-shadow: 0 0 10px rgba(52, 152, 219, 0.5); }
  }
</style>
{% endblock %}

{% block content %}
<style>
/* تحسينات لمعالجة المساحة الشاسعة والمسافات بين العناصر */
.container {
    padding-top: 0;
    padding-bottom: 0;
}

.row {
    margin-top: 0;
}

/* تحسين تنظيم العناصر وتقليل المساحات الفارغة */
.mosque-content {
    height: auto;
    min-height: 80vh;
    padding-top: 2rem;
    padding-bottom: 2rem;
}

/* تحسين مظهر البطاقات وتقليل المساحات الفارغة */
.card {
    margin-bottom: 1.5rem;
}

/* تحسين مظهر الاقتباس */
.conference-quote {
    margin: 1.5rem 0;
}
</style>

<div class="mosque-content" style="direction: rtl; text-align: center;" id="conference-details">
    {% comment %} <div style="background-color: rgba(0, 0, 0, 0.4); padding: 20px; border-radius: 15px; display: inline-block;">
        <h1 class="display-4 mb-4 animate__animated animate__fadeIn gradient-text glow-text">{% trans "مؤتمر السيرة النبوية السنوي" %}</h1>
        <p class="lead mb-4 animate__animated animate__fadeIn animate__delay-1s" style="color: white; font-size: 1.5rem;">{% trans "تحت رعاية معالي وزير التعليم" %}</p>
        <div class="d-flex justify-content-center animate__animated animate__fadeIn animate__delay-2s">
            <a href="#conference-details" class="btn btn-primary btn-lg {% if LANGUAGE_CODE == 'ar' %}ms-2{% else %}me-2{% endif %} pulse-element">{% trans "تفاصيل المؤتمر" %}</a>
            <a href="{% url 'core:contact' %}" class="btn btn-outline-light btn-lg pulse-element">{% trans "تواصل معنا" %}</a>
        </div>
    </div> {% endcomment %}
</div>






{% endblock %}

{% block extra_js %}
<script src="{% static 'js/mosque-effects.js' %}"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // تفعيل تأثيرات المسجد النبوي
        if (typeof initMosqueEffects === 'function') {
            initMosqueEffects();
        }
    });
</script>
{% endblock %}