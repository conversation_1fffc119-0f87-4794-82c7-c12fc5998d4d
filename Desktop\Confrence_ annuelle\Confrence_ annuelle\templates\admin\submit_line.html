{% load i18n admin_urls %}

<div class="submit-row">
    {% if show_save %}
        <input type="submit" value="{% trans 'حفظ' %}" class="default" name="_save">
    {% endif %}
    
    {% if show_delete_link %}
        <p class="deletelink-box">
            <a href="{% url opts|admin_urlname:'delete' original.pk|admin_urlquote %}" class="deletelink">
                {% trans "حذف" %}
            </a>
        </p>
    {% endif %}
    
    {% if show_save_as_new %}
        <input type="submit" value="{% trans 'حفظ كجديد' %}" name="_saveasnew">
    {% endif %}
    
    {% if show_save_and_add_another %}
        <input type="submit" value="{% trans 'حفظ وإضافة آخر' %}" name="_addanother">
    {% endif %}
    
    {% if show_save_and_continue %}
        <input type="submit" value="{% trans 'حفظ ومتابعة التعديل' %}" name="_continue">
    {% endif %}
    
    {% if show_close %}
        <input type="submit" value="{% trans 'إغلاق' %}" class="default" name="_close">
    {% endif %}
</div>

<style>
    .submit-row {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        border-radius: 10px;
        padding: 1.5rem;
        margin: 2rem 0;
        display: flex;
        gap: 1rem;
        align-items: center;
        justify-content: flex-end;
        flex-wrap: wrap;
    }

    .submit-row input[type="submit"] {
        background: linear-gradient(135deg, var(--primary-color), var(--info-color));
        color: white;
        border: none;
        border-radius: 8px;
        padding: 12px 24px;
        font-weight: 600;
        font-size: 1rem;
        cursor: pointer;
        transition: all 0.3s ease;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        min-width: 120px;
        justify-content: center;
    }

    .submit-row input[type="submit"]:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(44, 90, 160, 0.4);
    }

    .submit-row input[type="submit"].default {
        background: linear-gradient(135deg, var(--success-color), #2ecc71);
    }

    .submit-row input[type="submit"].default:hover {
        box-shadow: 0 5px 15px rgba(39, 174, 96, 0.4);
    }

    .submit-row input[name="_addanother"] {
        background: linear-gradient(135deg, var(--info-color), #3498db);
    }

    .submit-row input[name="_addanother"]:hover {
        box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
    }

    .submit-row input[name="_continue"] {
        background: linear-gradient(135deg, var(--warning-color), #e67e22);
    }

    .submit-row input[name="_continue"]:hover {
        box-shadow: 0 5px 15px rgba(243, 156, 18, 0.4);
    }

    .submit-row input[name="_saveasnew"] {
        background: linear-gradient(135deg, #9b59b6, #8e44ad);
    }

    .submit-row input[name="_saveasnew"]:hover {
        box-shadow: 0 5px 15px rgba(155, 89, 182, 0.4);
    }

    .deletelink-box {
        margin: 0;
    }

    .deletelink {
        background: linear-gradient(135deg, var(--danger-color), #c0392b);
        color: white;
        border: none;
        border-radius: 8px;
        padding: 12px 24px;
        font-weight: 600;
        font-size: 1rem;
        cursor: pointer;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 8px;
        min-width: 120px;
        justify-content: center;
    }

    .deletelink:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
        color: white;
    }

    /* إضافة أيقونات للأزرار */
    .submit-row input[name="_save"]:before {
        content: '💾';
        margin-left: 5px;
    }

    .submit-row input[name="_addanother"]:before {
        content: '➕';
        margin-left: 5px;
    }

    .submit-row input[name="_continue"]:before {
        content: '✏️';
        margin-left: 5px;
    }

    .submit-row input[name="_saveasnew"]:before {
        content: '📋';
        margin-left: 5px;
    }

    .submit-row input[name="_close"]:before {
        content: '✅';
        margin-left: 5px;
    }

    .deletelink:before {
        content: '🗑️';
        margin-left: 5px;
    }

    /* Responsive */
    @media (max-width: 768px) {
        .submit-row {
            flex-direction: column;
            align-items: stretch;
        }
        
        .submit-row input[type="submit"],
        .deletelink {
            width: 100%;
            justify-content: center;
            margin: 0.25rem 0;
        }
    }
</style>
