# مؤتمر السيرة النبوية السنوي - نظام إدارة المؤتمرات

## 📋 وصف المشروع

نظام إدارة شامل للمؤتمرات مع نظام صلاحيات متقدم للمسؤولين، مصمم خصيصاً لإدارة مؤتمر السيرة النبوية السنوي.

## ✨ المميزات الرئيسية

### 🔐 نظام الصلاحيات المتقدم
- **مسؤول كبير (Super Admin)**: صلاحيات كاملة لإدارة جميع المسؤولين والبيانات
- **مسؤول عادي (Regular Admin)**: يمكنه رؤية وإدارة الجداول المخصصة له فقط
- عزل أمني كامل بين مستويات المسؤولين

### 🏢 إدارة المؤسسات والشخصيات
- إدارة المؤسسات الحكومية والخاصة
- إدارة العلماء والشخصيات البارزة
- إدارة المسؤولين المنتخبين والوزراء السابقين
- إدارة السلك الدبلوماسي

### 📧 نظام الدعوات
- إرسال دعوات فردية وجماعية
- تتبع حالة الدعوات (مرسلة، مؤكدة، مرفوضة، حضر)
- إدارة معلومات الاتصال

### 🌍 دعم متعدد اللغات
- العربية (الافتراضية)
- الإنجليزية
- الفرنسية

## 🛠️ التقنيات المستخدمة

- **Backend**: Django 5.1.2
- **Database**: SQLite3
- **Frontend**: Bootstrap 5.3.0 + HTML/CSS/JavaScript
- **Icons**: Font Awesome
- **Animations**: Animate.css

## 📦 التثبيت والتشغيل

### المتطلبات
- Python 3.8+
- pip

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone https://github.com/saadmeiloude/conference-management.git
cd conference-management
```

2. **إنشاء بيئة افتراضية**
```bash
python -m venv venv
venv\Scripts\activate  # Windows
# أو
source venv/bin/activate  # Linux/Mac
```

3. **تثبيت المتطلبات**
```bash
pip install django pillow
```

4. **تطبيق migrations**
```bash
python manage.py makemigrations
python manage.py migrate
```

5. **إنشاء المسؤول الكبير**
```bash
python manage.py create_super_admin
```

6. **تشغيل الخادم**
```bash
python manage.py runserver
```

7. **فتح المتصفح**
```
http://127.0.0.1:8000
```

## 👤 حسابات الاختبار

### المسؤول الكبير
- **اسم المستخدم**: Saad
- **كلمة المرور**: Saad127

### مسؤول عادي للاختبار
- **اسم المستخدم**: admin_test
- **كلمة المرور**: test123

## 📁 هيكل المشروع

```
conference/
├── core/                 # التطبيق الأساسي
│   ├── models.py        # نماذج البيانات الأساسية
│   ├── views.py         # العروض الأساسية
│   ├── admin_views.py   # عروض إدارة المسؤولين
│   └── templates/       # قوالب HTML
├── organizations/       # تطبيق إدارة المؤسسات
│   ├── models.py       # نماذج المؤسسات والشخصيات
│   ├── views.py        # عروض إدارة المؤسسات
│   └── templates/      # قوالب HTML
├── static/             # الملفات الثابتة
├── media/              # ملفات الوسائط المرفوعة
└── templates/          # القوالب المشتركة
```

## 🔧 الإعدادات

### إعدادات قاعدة البيانات
يمكن تغيير إعدادات قاعدة البيانات في `conference/settings.py`

### إعدادات اللغة
اللغة الافتراضية هي العربية، يمكن تغييرها من الإعدادات

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء branch جديد للميزة
3. عمل commit للتغييرات
4. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT

## 📞 التواصل

- **المطور**: سعد ميلود
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +222 32816779

---

**جميع الحقوق محفوظة © 2025 مؤتمر السيرة النبوية السنوي**
