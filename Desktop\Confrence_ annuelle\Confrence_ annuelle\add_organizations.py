import os
import django

# Configure Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'conference.settings')
django.setup()

# Import models
from organizations.models import Organization

# Define organizations data
organizations_data = [
    {
        'name': 'Ligue islamique mondiale',
        'contact_person': '<PERSON><PERSON>',
        'description': 'Conseiller spécial des institutions religieuses et scientifiques en charge des centres de l Association aux États-Unis et au Royaume-Uni',
        'phone': '+17033624446',
        'email': 'muath_al<PERSON><PERSON>@themwl.org',
        'website': 'themwl.org',
        'participation_status': 'invited',
    },
    {
        'name': '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        'contact_person': '<PERSON><PERSON><PERSON><PERSON><PERSON>  ',
        'description': 'Maitre de  conférencier et consultant Independant',
        'phone': '+2693335362',
        'email': 'abouba<PERSON><PERSON><PERSON>@yahoo.fr',
        'address': 'B.P 2066 Moroni -Union des  Comores',
        'participation_status': 'invited',
    },
    {
        'name': '<PERSON>',
        'contact_person': '<PERSON>',
        'description': 'Institutions gouvernementales et internationales Ancien président du Parlement (Mozambique) Membre du Parlement international pour la tolérance et la paix',
        'phone': '+258  822222370',
        'email': '<EMAIL>',
        'address': 'Av. 24 de Julho n° 3773, C.Postal 1516 - Maputo',
        'participation_status': 'invited',
    },
    {
        'name': 'Université des États arabes',
        'contact_person': 'Zaid Mohamed Reza Al Sabban',
        'description': 'المؤسسات الحكومية والدولية\nإدارة القرن الأفريقي والسودان',
        'phone': '+20225775409',
        'email': '<EMAIL>',
        'participation_status': 'invited',
    },
    {
        'name': 'Banque Al Amana',
        'contact_person': 'Fatima Zahra Bouna Moctar',
        'description': 'Institutions financières et économiques',
        'phone': '+22241426200',
        'email': '<EMAIL>',
        'address': '7653 rue Ali Bin Abi Talib, Nouakchott - Mauritania',
        'website': 'www.bea.mr',
        'participation_status': 'invited',
    },
    {
        'name': 'La Banque Générale Mauritanienne d Investissement et de Commerce',
        'contact_person': 'EL Moctar Mohamed vall',
        'description': 'Directeur général adjoint des institutions financières et économiques',
        'phone': '+222 33804343',
        'email': '<EMAIL>',
        'address': 'BP 444 , Nouadhibou, R.I.M',
        'website': 'www.gbm-banque.com',
        'participation_status': 'invited',
    },
    {
        'name': 'Turkish Airlines - Thayer',
        'contact_person': 'Abdoulah Tuncer Kececi',
        'description': 'General Manager , Nouakchott',
        'phone': '+222 44440849',
        'email': '<EMAIL>',
        'address': 'Tevraigh Zeina Avenue , Moktar Ould Daddah no:550',
        'participation_status': 'invited',
        'website': 'www.turkishairlines.com',
    },
    {
        'name': 'S K S Fish & Marine Animals',
        'contact_person': 'Khaled Selaiteen',
        'description': 'Grandes entreprises et secteur privé',
        'phone': '+971  503433377',
        'email': '<EMAIL>',
        'address': 'Dar AI Wuheida Building , Office# 209',
        'participation_status': 'invited',
    },
    {
        'name': 'مدرسة EDGE الخاصة للأعمال - المغرب',
        'contact_person': 'منير تريفيس',
        'description': 'المؤسسات التعليمية\nالمدير العام',
        'phone': '+212520001952',
        'email': '<EMAIL>',
        'address': '52 شارع رحال نشكرة، الدار البيضاء',
        'participation_status': 'invited',
    },
    {
        'name': 'EUROPEAN SOLIDARITY CORPS',
        'contact_person': 'Erasmus Plus',
        'description': 'Les organisations internationales soutiennent les activités éducatives et les échanges culturels.',
        'phone': '+995 574 271 127',
        'email': '<EMAIL>',
        'website': 'http://www.erasmusplusyouth.info',
        'participation_status': 'invited',
    },
    # Government organization
    {
            'name': 'الوكالة الوطنية للإحصاء والتحليل الديمغرافي والاقتصادي',
            'name_fr': 'Agence Nationale de la Statistique et de l\'Analyse Démographique et Économique (ANSADE)',
            'contact_person': 'محمد المختار أحمد سيدي',
            'description': 'تتبع الوكالة لوزارة الشؤون الاقتصادية وترقية القطاعات الإنتاجية، وتُعنى بجمع وتحليل البيانات الإحصائية والديمغرافية والاقتصادية.',
            'phone': '(+222) 45 25 30 70',
            'email': '<EMAIL>',
            'website': 'ansade.mr',
            'address': 'نواكشوط',
            'participation_status': 'invited',
        },
        {
            'name': 'الصندوق الوطني للتأمين الصحي',
            'name_fr': 'Caisse Nationale d\'Assurance Maladie (CNAM)',
            'contact_person': 'عبد الله ولد سليمان ولد الشيخ سيديا',
            'description': 'يتبع الصندوق لوزارة الصحة، ويُعنى بتوفير خدمات التأمين الصحي للموظفين والعاملين في القطاعين العام والخاص.',
            'phone': '',
            'email': '',
            'website': '',
            'address': 'نواكشوط',
            'participation_status': 'invited',
        },
        {
            'name': 'ميناء نواذيبو المستقل',
            'name_fr': 'Port Autonome de Nouadhibou',
            'contact_person': 'أحمد سالم ولد التكرور',
            'description': 'يتبع الميناء لسلطة منطقة نواذيبو الحرة، ويُعتبر من أهم الموانئ التجارية في البلاد، حيث يُسهم في حركة الاستيراد والتصدير.',
            'phone': '',
            'email': '',
            'website': '',
            'address': 'نواذيبو',
            'participation_status': 'invited',
        },
        {
            'name': 'الشركة الوطنية لتنمية البنى التحتية الرقمية',
            'name_fr': 'Société Nationale pour le Développement des Infrastructures Numériques',
            'contact_person': '',
            'description': 'تتبع الشركة لوزارة التحول الرقمي والابتكار وعصرنة الإدارة، وتُعنى بتطوير البنية التحتية الرقمية في البلاد، بما في ذلك شبكات الاتصالات والإنترنت.',
            'phone': '',
            'email': '',
            'website': '',
            'address': 'نواكشوط',
            'participation_status': 'invited',
        },
        {
            'name': 'وزارة التحول الرقمي والابتكار وعصرنة الإدارة',
            'name_fr': 'Ministère de la Transformation Numérique, de l\'Innovation et de la Modernisation de l\'Administration',
            'contact_person': 'أحمد سالم ولد بدّه',
            'description': 'تشرف الوزارة على عدة مؤسسات، منها الشركة الوطنية لتنمية البنى التحتية الرقمية، وتُعنى بتحديث الإدارة وتطوير الخدمات الرقمية.',
            'phone': '',
            'email': '',
            'website': 'mtnima.gov.mr',
            'address': 'نواكشوط',
            'participation_status': 'invited',
        },
        {
            'name': 'وزارة التهذيب الوطني وإصلاح النظام التعليمي',
            'name_fr': 'Ministère de l\'Éducation Nationale et de la Réforme du Système Éducatif',
            'contact_person': 'هدى باباه',
            'description': 'تشرف الوزارة على المؤسسات التعليمية في البلاد، بما في ذلك المدارس والمعاهد، وتُعنى بإصلاح النظام التعليمي وتطويره.',
            'phone': '',
            'email': '',
            'website': 'education.gov.mr',
            'address': 'نواكشوط',
            'participation_status': 'invited',
        },
        {
            'name': 'وزارة التجهيز والنقل',
            'name_fr': 'Ministère de l\'Equipement et des Transports',
            'contact_person': 'إعل ولد الفيرك',
            'description': 'تشرف الوزارة على مشارية البنية التحتية المتعلقة بالنقل، مثل الطرق والجسور، وتُعنى بتنظيم قطاع النقل البري والبحري والجوي.',
            'phone': '',
            'email': '',
            'website': 'transports.gov.mr',
            'address': 'نواكشوط',
            'participation_status': 'invited',
        },
        {
            'name': 'وزارة المعادن والصناعة',
            'name_fr': 'Ministère des Mines et de l\'Industrie',
            'contact_person': 'تيام تيجاني',
            'description': 'تشرف الوزارة على قطاع التعدين والصناعة، وتُعنى بوضع السياسات المتعلقة بالتعدين وتطوير الصناعات المحلية.',
            'phone': '',
            'email': '',
            'website': 'mmi.gov.mr',
            'address': 'نواكشوط',
            'participation_status': 'invited',
        },
        {
            'name': 'وزارة الداخلية واللامركزية',
            'name_fr': 'Ministère de l\'Intérieur et de la Décentralisation',
            'contact_person': 'محمد أحمد ولد محمد الأمين',
            'description': 'تشرف الوزارة على الإدارة المحلية، الأمن الداخلي، واللامركزية، وتُعنى بتنظيم العلاقة بين الحكومة المركزية والسلطات المحلية.',
            'phone': '',
            'email': '',
            'website': 'interieur.gov.mr',
            'address': 'نواكشوط',
            'participation_status': 'invited',
        },
        {
            'name': 'وزارة التجارة والصناعة والصناعة التقليدية والسياحة',
            'name_fr': 'Ministère du Commerce, de l\'Industrie, de l\'Artisanat et du Tourisme',
            'contact_person': 'زينب بنت أحمدناه',
            'description': 'تشرف الوزارة على قطاعات التجارة، الصناعة، الصناعة التقليدية، والسياحة، وتُعنى بتطوير هذه القطاعات وتعزيز مساهمتها في الاقتصاد الوطني.',
            'phone': '',
            'email': '',
            'website': '',
            'address': 'نواكشوط',
            'participation_status': 'invited',
        },
        {
            'name': 'المفتشية العامة للدولة',
            'name_fr': 'Inspection Générale d\'État (IGE)',
            'contact_person': '',
            'description': 'الرقابة على الإدارات العامة، مكافحة الفساد، تقييم السياسات العامة',
            'phone': '',
            'email': '',
            'website': '',
            'address': 'نواكشوط',
            'participation_status': 'invited',
        },
        {
            'name': 'المفتشية العامة للمالية',
            'name_fr': 'Inspection Générale des Finances (IGF)',
            'contact_person': '',
            'description': 'الرقابة المالية، التدقيق، تقييم الأداء المالي للإدارات العامة',
            'phone': '',
            'email': '',
            'website': '',
            'address': 'نواكشوط',
            'participation_status': 'invited',
        },
        {
            'name': 'وزارة الشؤون الخارجية والتعاون والموريتانيين في الخارج',
            'name_fr': 'Ministère des Affaires Étrangères, de la Coopération et des Mauritaniens de l\'Extérieur',
            'contact_person': 'محمد سالم ولد مرزوك',
            'description': 'العلاقات الخارجية، التعاون الدولي، شؤون الموريتانيين في الخارج',
            'phone': '',
            'email': '',
            'website': 'diplomatie.gov.mr',
            'address': 'نواكشوط',
            'participation_status': 'invited',
        },
        {
            'name': 'الوكالة الوطنية للبحوث الجيولوجية والتراث المعدني',
            'name_fr': 'Agence Nationale de Recherches Géologiques et du Patrimoine Minier (ANARPAM)',
            'contact_person': '',
            'description': 'البحث الجيولوجي، تطوير التراث المعدني',
            'phone': '',
            'email': '',
            'website': '',
            'address': 'نواكشوط',
            'participation_status': 'invited',
        },
        {
            'name': 'الوكالة الوطنية "معادن موريتانيا"',
            'name_fr': 'Agence Nationale "Maaden Mauritanie"',
            'contact_person': '',
            'description': 'تنظيم وتطوير قطاع التعدين الأهلي',
            'phone': '',
            'email': '',
            'website': '',
            'address': 'نواكشوط',
            'participation_status': 'invited',
        },
        {
            'name': 'الشركة الوطنية للصناعة والمناجم',
            'name_fr': 'Société Nationale Industrielle et Minière (SNIM)',
            'contact_person': '',
            'description': 'استغلال وتصدير خامات الحديد',
            'phone': '',
            'email': '',
            'website': '',
            'address': 'نواذيبو',
            'participation_status': 'invited',
        },
        {
            'name': 'المكتب الوطني للتقييس والمترولوجيا',
            'name_fr': 'Office National de Normalisation et de Métrologie (ONANOR)',
            'contact_person': '',
            'description': 'وضع المعايير والمقاييس الوطنية',
            'phone': '',
            'email': '',
            'website': '',
            'address': 'نواكشوط',
            'participation_status': 'invited',
        },
        {
            'name': 'وزارة الاقتصاد والمالية',
            'name_fr': 'Ministère de l\'Économie et des Finances',
            'contact_person': 'سيد أحمد أبوه',
            'description': 'السياسات الاقتصادية، الميزانية العامة، الضرائب',
            'phone': '',
            'email': '',
            'website': 'finances.gov.mr',
            'address': 'نواكشوط',
            'participation_status': 'invited',
        },
    ]

# Add more organizations to the main list
organizations_data.extend([
    {
        'name': 'Ambassade des États-Unis d\'Amérique',
        'contact_person': 'Michael J. Dodman',
        'description': 'Ambassadeur',
        'phone': '+222 4525-2650',
        'email': '<EMAIL>',
        'address': 'Route de Nouadhibou, Avenue Al Quds, Nouakchott, Mauritanie',
        'website': '',
        'participation_status': 'invited',
    },
    {
        'name': 'Ministère de l\'Habitat, de l\'Urbanisme et de l\'Aménagement du Territoire',
        'contact_person': 'Ahmed Bedahi Mokhtar',
        'description': 'Secrétaire Général\nRépublique Islamique de Mauritanie',
        'phone': '+222 45 25 30 96',
        'email': '<EMAIL>',
        'address': '',
        'website': 'www.habitat.gov.mr',
        'participation_status': 'invited',
    },
    {
        'name': 'Université Ain Shams',
        'contact_person': 'Dr. Hagger Abou Gabal',
        'description': 'Professeur en Immunologie\nFaculté de Médecine',
        'phone': '(+20) 1222 196733',
        'email': '<EMAIL>',
        'address': '',
        'website': '',
        'participation_status': 'invited',
    },
    {
        'name': 'Garware Technical Fibres Ltd.',
        'contact_person': 'Anith Kumar K P',
        'description': 'Directeur Général Adjoint (Affaires Internationales)',
        'phone': '+91 9449352591',
        'email': '<EMAIL>',
        'address': 'Plot No. 11, Block D-1, MIDC, Chinchwad, Pune-411 019, Inde',
        'website': 'www.garwareropes.com',
        'participation_status': 'invited',
    },
    {
        'name': 'Sidi Mohamed Ghadda',
        'contact_person': 'Sidi Mohamed Ghadda',
        'description': 'Président',
        'phone': '+222 36 41 49 57',
        'email': '<EMAIL>',
        'address': 'Rue ndeke hamagatta, zone P, Nouakchott, Mauritanie\nNIF : 40004890011',
        'website': '',
        'participation_status': 'invited',
    },
    {
        'name': 'Consulat Honoraire de Hongrie',
        'name_fr': 'Consulat Honoraire de Hongrie, Nouakchott',
        'contact_person': 'Mohamed Yahya El Moctar El Hassen',
        'description': 'Consul Honoraire',
        'phone': '+222 25 00 14 41',
        'email': '<EMAIL>',
        'address': 'Rue Sidi Mohamed Dighinely, Ilot D 181 BP: 4350',
        'website': '',
        'participation_status': 'invited',
    },
])

# Define government organizations names
government_orgs = [
    'Université des États arabes',
    'Ambassade des États-Unis d\'Amérique',
    'Ministère de l\'Habitat, de l\'Urbanisme et de l\'Aménagement du Territoire',
    'Université Ain Shams',
    'Consulat Honoraire de Hongrie',
    'وزارة التحول الرقمي والابتكار وعصرنة الإدارة',
    'وزارة التهذيب الوطني وإصلاح النظام التعليمي',
    'وزارة التجهيز والنقل',
    'وزارة المعادن والصناعة',
    'وزارة الداخلية واللامركزية',
    'وزارة التجارة والصناعة والصناعة التقليدية والسياحة',
    'المفتشية العامة للدولة',
    'المفتشية العامة للمالية',
    'وزارة الشؤون الخارجية والتعاون والموريتانيين في الخارج',
    'وزارة الاقتصاد والمالية'
]

# Add organizations to database
for org_data in organizations_data:
    # Check if organization already exists
    if not Organization.objects.filter(name=org_data['name']).exists():
        # Determine organization type
        org_type = 'government' if org_data['name'] in government_orgs else 'private'
        
        # Create new organization
        org = Organization(
            name=org_data['name'],
            name_fr=org_data.get('name_fr', ''),
            contact_person=org_data['contact_person'],
            phone=org_data['phone'],
            email=org_data['email'],
            description=org_data.get('description', ''),
            address=org_data.get('address', ''),
            website=org_data.get('website', ''),
            participation_status=org_data['participation_status'],
            organization_type=org_type
        )
        org.save()
        print(f"Added organization: {org_data['name']} (Type: {org_type})")
    else:
        # Update existing organization type
        org = Organization.objects.get(name=org_data['name'])
        org_type = 'government' if org_data['name'] in government_orgs else 'private'
        org.organization_type = org_type
        org.save()
        print(f"Updated organization type: {org_data['name']} (Type: {org_type})")

print("\nAll organizations have been added/updated in the database.")