/**
 * Organization Effects JavaScript
 * تأثيرات تفاعلية للمؤسسات
 */

document.addEventListener('DOMContentLoaded', function() {
    // إضافة عناصر الزخرفة الخلفية
    addBackgroundDecorations();
    
    // تفعيل تأثيرات البطاقات
    initializeCardEffects();
    
    // تفعيل تأثير التلميحات للأزرار
    initializeTooltips();
    
    // تفعيل تأثير Parallax للعناصر الزخرفية
    initializeParallaxEffect();
    
    // تفعيل تأثير الشعارات
    initializeLogoEffects();
    
    // تفعيل تأثير الشريط المتحرك للمؤسسات المميزة
    addFeaturedRibbons();
    
    // تفعيل تأثير الأزرار المتحركة
    initializeButtonEffects();
    
    // تفعيل تأثير الفلتر المتحرك
    initializeFilterEffects();
    
    // تفعيل أزرار عرض المؤسسات والعلماء
    initializeSectionButtons();
    
    console.log('تم تفعيل جميع التأثيرات والأزرار بنجاح');
});

/**
 * إضافة عناصر الزخرفة الخلفية
 */
function addBackgroundDecorations() {
    const container = document.querySelector('.organizations-container');
    if (!container) return;
    
    // إنشاء عناصر الزخرفة
    for (let i = 1; i <= 4; i++) {
        const decoration = document.createElement('div');
        decoration.className = `bg-decoration bg-decoration-${i}`;
        container.appendChild(decoration);
    }
}

/**
 * تفعيل تأثيرات البطاقات
 */
function initializeCardEffects() {
    const cards = document.querySelectorAll('.organization-card');
    if (!cards.length) return;
    
    // إضافة تأثير الظهور التدريجي للبطاقات
    cards.forEach((card, index) => {
        // تأخير ظهور البطاقات بشكل متتالي
        setTimeout(() => {
            card.classList.add('show');
        }, 100 * index);
        
        // إضافة تأثير الزجاج للبطاقات
        card.classList.add('card-glass');
        
        // إضافة تأثير الظل النابض
        card.classList.add('shadow-pulse');
    });
}

/**
 * تفعيل تأثير التلميحات للأزرار
 */
function initializeTooltips() {
    // تفعيل التلميحات باستخدام Bootstrap
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

/**
 * تفعيل تأثير Parallax للعناصر الزخرفية
 */
function initializeParallaxEffect() {
    const decorations = document.querySelectorAll('.bg-decoration');
    if (!decorations.length) return;
    
    // إضافة مستمع لحركة الماوس
    document.addEventListener('mousemove', function(e) {
        const x = e.clientX / window.innerWidth;
        const y = e.clientY / window.innerHeight;
        
        decorations.forEach((decoration, index) => {
            // تحريك العناصر بسرعات مختلفة حسب موقع المؤشر
            const speed = 30 * (index + 1);
            const xOffset = (x - 0.5) * speed;
            const yOffset = (y - 0.5) * speed;
            
            decoration.style.transform = `translate(${xOffset}px, ${yOffset}px) rotate(${xOffset / 2}deg)`;
        });
    });
}

/**
 * تفعيل تأثير الشعارات
 */
function initializeLogoEffects() {
    const logos = document.querySelectorAll('.organization-logo');
    if (!logos.length) return;
    
    logos.forEach(logo => {
        // إضافة مستمع لحدث التحويم
        logo.addEventListener('mouseenter', function() {
            this.style.transform = 'scale(1.1) rotate(5deg)';
        });
        
        logo.addEventListener('mouseleave', function() {
            this.style.transform = 'scale(1) rotate(0deg)';
        });
    });
}

/**
 * إضافة شريط متحرك للمؤسسات المميزة
 */
function addFeaturedRibbons() {
    // يمكن تحديد المؤسسات المميزة بناءً على معايير معينة
    // مثال: المؤسسات التي لديها حالة مشاركة "نشط"
    const activeCards = document.querySelectorAll('.organization-card .badge-success');
    
    activeCards.forEach(badge => {
        const card = badge.closest('.organization-card');
        if (card) {
            // إضافة شريط للمؤسسات النشطة
            const ribbon = document.createElement('div');
            ribbon.className = 'ribbon';
            ribbon.textContent = 'مميز';
            card.appendChild(ribbon);
            
            // جعل البطاقة ذات موقع نسبي لعرض الشريط بشكل صحيح
            card.style.position = 'relative';
        }
    });
}

/**
 * تأثير تحميل الصور بشكل متدرج
 */
function lazyLoadImages() {
    const images = document.querySelectorAll('.organization-logo img');
    
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.add('loaded');
                    imageObserver.unobserve(img);
                }
            });
        });
        
        images.forEach(img => {
            imageObserver.observe(img);
        });
    } else {
        // Fallback for browsers that don't support IntersectionObserver
        images.forEach(img => {
            img.src = img.dataset.src;
        });
    }
}

/**
 * تأثير الأزرار المتحركة
 */
function initializeButtonEffects() {
    const buttons = document.querySelectorAll('.btn-action');
    
    buttons.forEach(button => {
        button.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-3px)';
        });
        
        button.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
}

/**
 * تأثير الفلتر المتحرك
 */
function initializeFilterEffects() {
    const filterButtons = document.querySelectorAll('.organization-filters .btn');
    
    filterButtons.forEach(button => {
        button.addEventListener('click', function() {
            // إزالة الفئة النشطة من جميع الأزرار
            filterButtons.forEach(btn => btn.classList.remove('active'));
            
            // إضافة الفئة النشطة للزر المحدد
            this.classList.add('active');
            
            // تأثير انتقالي للبطاقات عند تغيير الفلتر
            const cards = document.querySelectorAll('.organization-card');
            cards.forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, 300);
            });
        });
    });
}

/**
 * تفعيل أزرار عرض المؤسسات والعلماء
 */
function initializeSectionButtons() {
    // تحديد جميع أزرار الأقسام
    const sectionButtons = document.querySelectorAll('.btn-hover-expand');
    
    // إضافة مستمع لحدث النقر لكل زر
    sectionButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            // منع السلوك الافتراضي للرابط مؤقتًا لإضافة التأثير البصري
            e.preventDefault();
            
            // التحقق من وجود الرابط
            const targetHref = button.getAttribute('href');
            if (!targetHref) {
                console.error('الرابط غير محدد للزر:', button.textContent.trim());
                return;
            }
            
            // إضافة تأثير بصري عند النقر
            this.classList.add('pulse-once');
            
            // طباعة رسالة تأكيد في وحدة التحكم
            console.log('تم النقر على زر القسم:', this.textContent.trim());
            console.log('الانتقال إلى:', targetHref);
            
            // الانتقال إلى الرابط بعد إظهار التأثير البصري
            setTimeout(() => {
                this.classList.remove('pulse-once');
                window.location.href = targetHref;
            }, 300);
        });
    });
    
    // تسجيل رسالة تأكيد في وحدة التحكم
    console.log('تم تفعيل أزرار الأقسام:', sectionButtons.length);
}