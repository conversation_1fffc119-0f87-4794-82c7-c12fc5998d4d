{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "إرسال دعوات جماعية" %}{% endblock %}

{% block extra_css %}
<style>
    /* Mini Hero Section */
    .mini-hero {
        background: linear-gradient(rgba(0, 0, 0, 0.6), rgba(0, 0, 0, 0.6)), url('{% static "img/pattern.png" %}');
        background-size: cover;
        background-position: center;
        color: white;
        padding: 3rem 0;
        position: relative;
        overflow: hidden;
        margin-bottom: 2rem;
    }
    
    .mini-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('{% static "img/pattern.png" %}');
        opacity: 0.2;
        animation: pulse 15s infinite;
    }
    
    @keyframes pulse {
        0% { opacity: 0.1; }
        50% { opacity: 0.3; }
        100% { opacity: 0.1; }
    }
    
    .mini-hero .container {
        position: relative;
        z-index: 1;
    }
    
    /* Form Card */
    .form-card {
        background-color: white;
        border-radius: 10px;
        box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
        padding: 2rem;
        margin-bottom: 2rem;
        transition: all 0.3s ease;
    }
    
    .form-card:hover {
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        transform: translateY(-5px);
    }
    
    /* Form Elements */
    .form-label {
        font-weight: 600;
        color: #495057;
        margin-bottom: 0.5rem;
    }
    
    .form-control {
        border-radius: 5px;
        border: 1px solid #ced4da;
        padding: 0.75rem 1rem;
        transition: all 0.2s;
    }
    
    .form-control:focus {
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
    
    /* Select2 Customization */
    .select2-container--default .select2-selection--multiple {
        border-radius: 5px;
        border: 1px solid #ced4da;
        min-height: 100px;
    }
    
    .select2-container--default.select2-container--focus .select2-selection--multiple {
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
    }
    
    /* Buttons */
    .btn-primary {
        background-color: #007bff;
        border-color: #007bff;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        border-radius: 5px;
        transition: all 0.3s;
    }
    
    .btn-primary:hover {
        background-color: #0069d9;
        border-color: #0062cc;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    
    .btn-secondary {
        background-color: #6c757d;
        border-color: #6c757d;
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        border-radius: 5px;
        transition: all 0.3s;
    }
    
    .btn-secondary:hover {
        background-color: #5a6268;
        border-color: #545b62;
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }
    
    /* Organizations Card */
    .organizations-card {
        max-height: 300px;
        overflow-y: auto;
        border: 1px solid #ced4da;
        border-radius: 5px;
        padding: 1rem;
    }
    
    /* Custom Scrollbar */
    .organizations-card::-webkit-scrollbar {
        width: 8px;
    }
    
    .organizations-card::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 10px;
    }
    
    .organizations-card::-webkit-scrollbar-thumb {
        background: #888;
        border-radius: 10px;
    }
    
    .organizations-card::-webkit-scrollbar-thumb:hover {
        background: #555;
    }
    
    /* Message Preview */
    .message-preview {
        background-color: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 5px;
        padding: 1.5rem;
        margin-top: 1.5rem;
        display: none;
    }
    
    .message-preview h5 {
        color: #495057;
        margin-bottom: 1rem;
        border-bottom: 1px solid #e9ecef;
        padding-bottom: 0.5rem;
    }
    
    .message-preview-content {
        white-space: pre-line;
    }
    
    /* Alert */
    .alert-info {
        background-color: #cce5ff;
        border-color: #b8daff;
        color: #004085;
        border-radius: 5px;
        padding: 1rem;
        margin-bottom: 1.5rem;
    }
    
    /* Checkbox */
    .form-check-input {
        margin-top: 0.3rem;
    }
    
    .form-check-label {
        margin-bottom: 0;
        cursor: pointer;
    }
    
    /* Loading State */
    .btn-loading {
        position: relative;
        pointer-events: none;
        color: transparent !important;
    }
    
    .btn-loading:after {
        content: '';
        position: absolute;
        width: 1rem;
        height: 1rem;
        top: calc(50% - 0.5rem);
        left: calc(50% - 0.5rem);
        border: 2px solid #fff;
        border-radius: 50%;
        border-right-color: transparent;
        animation: spin 0.75s linear infinite;
    }
    
    @keyframes spin {
        to { transform: rotate(360deg); }
    }
</style>
{% endblock %}

{% block content %}
<!-- Mini Hero Section -->
<section class="mini-hero">
    <div class="container text-center">
        <h1 class="display-4">{% trans "إرسال دعوات جماعية" %}</h1>
        <p class="lead">{% trans "أرسل دعوات إلى مجموعة من المؤسسات دفعة واحدة" %}</p>
    </div>
</section>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <!-- Alert -->
            <div class="alert alert-info" role="alert">
                <i class="fas fa-info-circle me-2"></i> {% trans "يمكنك إرسال دعوات إلى عدة مؤسسات في وقت واحد. حدد المؤسسات التي تريد دعوتها، وأدخل موضوع الرسالة ومحتواها." %}
            </div>
            
            <!-- Form Card -->
            <div class="form-card">
                <form method="post" id="bulk-invitation-form">
                    {% csrf_token %}
                    
                    <!-- Organizations Selection -->
                    <div class="mb-4">
                        <label for="id_organizations" class="form-label">{% trans "المؤسسات" %}</label>
                        <div class="d-flex align-items-center mb-2">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="select-all">
                                <label class="form-check-label" for="select-all">{% trans "تحديد الكل" %}</label>
                            </div>
                            <span class="ms-3 text-muted" id="selected-count-text">0 {% trans "مؤسسة محددة" %}</span>
                        </div>
                        <div class="organizations-card">
                            {{ form.organizations }}
                        </div>
                        {% if form.organizations.errors %}
                            <div class="text-danger mt-1">{{ form.organizations.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <!-- Subject -->
                    <div class="mb-4">
                        <label for="id_subject" class="form-label">{% trans "الموضوع" %}</label>
                        {{ form.subject }}
                        {% if form.subject.errors %}
                            <div class="text-danger mt-1">{{ form.subject.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <!-- Message -->
                    <div class="mb-4">
                        <label for="id_message" class="form-label">{% trans "الرسالة" %}</label>
                        {{ form.message }}
                        {% if form.message.errors %}
                            <div class="text-danger mt-1">{{ form.message.errors }}</div>
                        {% endif %}
                    </div>
                    
                    <!-- Preview Button -->
                    <div class="mb-4">
                        <button type="button" class="btn btn-outline-secondary" id="preview-button">
                            <i class="fas fa-eye me-2"></i> {% trans "معاينة الرسالة" %}
                        </button>
                    </div>
                    
                    <!-- Message Preview -->
                    <div class="message-preview" id="message-preview">
                        <h5>{% trans "معاينة الرسالة" %}</h5>
                        <div class="mb-3">
                            <strong>{% trans "الموضوع:" %}</strong>
                            <div id="preview-subject" class="mt-1"></div>
                        </div>
                        <div>
                            <strong>{% trans "المحتوى:" %}</strong>
                            <div id="preview-message" class="message-preview-content mt-1"></div>
                        </div>
                    </div>
                    
                    <!-- Form Actions -->
                    <div class="d-flex justify-content-between mt-4">
                        <a href="{% url 'organizations:organization_list' %}" class="btn btn-secondary">
                            <i class="fas fa-times me-2"></i> {% trans "إلغاء" %}
                        </a>
                        <button type="submit" class="btn btn-primary" id="submit-button">
                            <i class="fas fa-paper-plane me-2"></i> {% trans "إرسال الدعوات" %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // تحدية عداد المؤسسات المحددة
    function updateSelectedCount() {
        const selectedOrgs = document.querySelectorAll('#id_organizations option:checked').length;
        document.getElementById('selected-count-text').textContent = selectedOrgs + ' {% trans "مؤسسة محددة" %}';
    }

    // تحديد/إلغاء تحديد كل المؤسسات
    document.getElementById('select-all').addEventListener('change', function() {
        const options = document.querySelectorAll('#id_organizations option');
        options.forEach(option => {
            option.selected = this.checked;
        });
        updateSelectedCount();
    });

    // تحديث العداد عند تغيير الاختيار
    document.getElementById('id_organizations').addEventListener('change', updateSelectedCount);

    // معاينة الرسالة
    document.getElementById('preview-button').addEventListener('click', function() {
        const subject = document.getElementById('id_subject').value;
        const message = document.getElementById('id_message').value;
        
        if (subject || message) {
            document.getElementById('preview-subject').textContent = subject || '(لا يوجد موضوع)';
            document.getElementById('preview-message').textContent = message || '(لا توجد رسالة)';
            document.getElementById('message-preview').style.display = 'block';
        } else {
            alert('يرجى إدخال الموضوع والرسالة أولاً');
        }
    });

    // التحقق من صحة النموذج قبل الإرسال
    document.getElementById('bulk-invitation-form').addEventListener('submit', function(e) {
        const selectedOrgs = document.querySelectorAll('#id_organizations option:checked').length;
        const subject = document.getElementById('id_subject').value;
        const message = document.getElementById('id_message').value;
        
        if (selectedOrgs === 0) {
            e.preventDefault();
            alert('يرجى تحديد مؤسسة واحدة على الأقل');
            return false;
        }
        
        if (!subject || !message) {
            e.preventDefault();
            alert('يرجى إدخال الموضوع والرسالة');
            return false;
        }
        
        // إظهار حالة التحميل
        const submitButton = document.getElementById('submit-button');
        submitButton.classList.add('btn-loading');
        submitButton.innerHTML = '';
        
        return true;
    });

    // تحديث العداد عند تحميل الصفحة
    document.addEventListener('DOMContentLoaded', function() {
        updateSelectedCount();
    });
</script>
{% endblock %}