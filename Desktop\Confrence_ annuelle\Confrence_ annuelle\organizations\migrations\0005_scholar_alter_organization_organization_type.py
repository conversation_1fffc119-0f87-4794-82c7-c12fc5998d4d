# Generated by Django 5.1.2 on 2025-07-02 12:03

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('organizations', '0004_organization_organization_type'),
    ]

    operations = [
        migrations.CreateModel(
            name='Scholar',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(choices=[('sheikh', 'الشيخ'), ('doctor', 'الدكتور'), ('professor', 'الأستاذ'), ('imam', 'الإمام'), ('mufti', 'المفتي'), ('qadi', 'القاضي'), ('other', 'أخرى')], default='sheikh', max_length=20, verbose_name='اللقب')),
                ('name', models.CharField(max_length=255, verbose_name='الاسم')),
                ('full_name', models.CharField(blank=True, max_length=500, null=True, verbose_name='الاسم الكامل')),
                ('position', models.CharField(blank=True, max_length=255, null=True, verbose_name='الوظيفة/المنصب')),
                ('organization', models.CharField(blank=True, max_length=255, null=True, verbose_name='الجهة/المؤسسة')),
                ('country', models.CharField(blank=True, max_length=100, null=True, verbose_name='البلد')),
                ('city', models.CharField(blank=True, max_length=100, null=True, verbose_name='المدينة')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='الهاتف')),
                ('website', models.URLField(blank=True, null=True, verbose_name='الموقع الإلكتروني')),
                ('participation_status', models.CharField(choices=[('invited', 'مدعو'), ('confirmed', 'مؤكد'), ('declined', 'اعتذر'), ('attended', 'حضر')], default='invited', max_length=20, verbose_name='حالة المشاركة')),
                ('biography', models.TextField(blank=True, null=True, verbose_name='السيرة الذاتية')),
                ('specialization', models.CharField(blank=True, max_length=255, null=True, verbose_name='التخصص')),
                ('photo', models.ImageField(blank=True, null=True, upload_to='scholars/photos/', verbose_name='الصورة')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'عالم',
                'verbose_name_plural': 'العلماء',
                'ordering': ['name'],
            },
        ),
        migrations.AlterField(
            model_name='organization',
            name='organization_type',
            field=models.CharField(choices=[('government', 'Government'), ('private', 'Private'), ('scholar', 'Scholar')], default='private', max_length=20, verbose_name='Organization Type'),
        ),
    ]
