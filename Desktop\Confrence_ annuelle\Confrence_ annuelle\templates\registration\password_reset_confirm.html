{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card glass-effect fade-in-element">
            <div class="card-header">
                <h3 class="neon-text text-center">{% trans "تعيين كلمة مرور جديدة" %}</h3>
            </div>
            <div class="card-body">
                {% if validlink %}
                <p class="card-text">{% trans "الرجاء إدخال كلمة مرور جديدة مرتين حتى نتمكن من التحقق من صحة كتابتك." %}</p>
                
                <form method="post" class="fade-in-element">
                    {% csrf_token %}
                    <div class="mb-3">
                        <label for="id_new_password1" class="form-label">{% trans "كلمة المرور الجديدة" %}</label>
                        <input type="password" name="new_password1" id="id_new_password1" class="form-control glass-effect" required autofocus>
                        {% if form.new_password1.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.new_password1.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                        <small class="form-text text-muted">
                            {% trans "كلمة المرور الخاصة بك لا يمكن أن تكون مشابهة جدًا لمعلوماتك الشخصية الأخرى." %}<br>
                            {% trans "كلمة المرور الخاصة بك يجب أن تحتوي على 8 أحرف على الأقل." %}<br>
                            {% trans "كلمة المرور الخاصة بك لا يمكن أن تكون كلمة مرور شائعة الاستخدام." %}<br>
                            {% trans "كلمة المرور الخاصة بك لا يمكن أن تكون مكونة من أرقام فقط." %}
                        </small>
                    </div>
                    <div class="mb-3">
                        <label for="id_new_password2" class="form-label">{% trans "تأكيد كلمة المرور الجديدة" %}</label>
                        <input type="password" name="new_password2" id="id_new_password2" class="form-control glass-effect" required>
                        {% if form.new_password2.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.new_password2.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-3d btn-primary">{% trans "تغيير كلمة المرور" %}</button>
                    </div>
                </form>
                {% else %}
                <div class="alert alert-danger glass-effect animate__animated animate__fadeInDown">
                    <i class="fas fa-exclamation-triangle icon-float"></i> {% trans "رابط إعادة تعيين كلمة المرور غير صالح" %}
                </div>
                
                <p class="card-text">{% trans "رابط إعادة تعيين كلمة المرور غير صالح، ربما لأنه تم استخدامه بالفعل. يرجى طلب إعادة تعيين كلمة المرور مرة أخرى." %}</p>
                
                <div class="d-grid gap-2 mt-4">
                    <a href="{% url 'password_reset' %}" class="btn btn-3d btn-primary">{% trans "طلب إعادة تعيين جديد" %}</a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}