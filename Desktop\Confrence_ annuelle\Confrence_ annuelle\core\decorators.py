"""
Decorators للتحكم في صلاحيات المسؤولين
"""

from functools import wraps
from django.contrib.auth.decorators import login_required
from django.core.exceptions import PermissionDenied
from django.shortcuts import redirect
from django.contrib import messages
from django.utils.translation import gettext as _


def super_admin_required(view_func):
    """
    Decorator للتأكد من أن المستخدم مسؤول كبير
    """
    @wraps(view_func)
    @login_required
    def _wrapped_view(request, *args, **kwargs):
        # التحقق من وجود ملف المسؤول
        if not hasattr(request.user, 'admin_profile'):
            messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه الصفحة'))
            return redirect('core:home')
        
        # التحقق من نوع المسؤول
        if not request.user.admin_profile.is_super_admin():
            messages.error(request, _('هذه الصفحة مخصصة للمسؤولين الكبار فقط'))
            return redirect('core:home')
        
        # التحقق من أن الحساب نشط
        if not request.user.admin_profile.is_active:
            messages.error(request, _('حسابك غير نشط. يرجى التواصل مع الإدارة'))
            return redirect('core:home')
        
        return view_func(request, *args, **kwargs)
    
    return _wrapped_view


def regular_admin_required(view_func):
    """
    Decorator للتأكد من أن المستخدم مسؤول عادي أو كبير
    """
    @wraps(view_func)
    @login_required
    def _wrapped_view(request, *args, **kwargs):
        # التحقق من وجود ملف المسؤول
        if not hasattr(request.user, 'admin_profile'):
            messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه الصفحة'))
            return redirect('core:home')
        
        # التحقق من أن الحساب نشط
        if not request.user.admin_profile.is_active:
            messages.error(request, _('حسابك غير نشط. يرجى التواصل مع الإدارة'))
            return redirect('core:home')
        
        return view_func(request, *args, **kwargs)
    
    return _wrapped_view


def admin_owns_data(model_class, lookup_field='pk'):
    """
    Decorator للتأكد من أن المسؤول يملك البيانات التي يحاول الوصول إليها
    أو أنه مسؤول كبير
    """
    def decorator(view_func):
        @wraps(view_func)
        @regular_admin_required
        def _wrapped_view(request, *args, **kwargs):
            # المسؤول الكبير يمكنه الوصول لكل شيء
            if request.user.admin_profile.is_super_admin():
                return view_func(request, *args, **kwargs)
            
            # للمسؤولين العاديين، التحقق من ملكية البيانات
            object_id = kwargs.get(lookup_field)
            if object_id:
                try:
                    obj = model_class.objects.get(pk=object_id)
                    # التحقق من ملكية البيانات (سيتم تخصيصه حسب النموذج)
                    if hasattr(obj, 'created_by') and obj.created_by != request.user:
                        messages.error(request, _('ليس لديك صلاحية للوصول إلى هذه البيانات'))
                        return redirect('core:home')
                except model_class.DoesNotExist:
                    messages.error(request, _('البيانات المطلوبة غير موجودة'))
                    return redirect('core:home')
            
            return view_func(request, *args, **kwargs)
        
        return _wrapped_view
    return decorator


def check_admin_permissions(permission_name):
    """
    Decorator للتحقق من صلاحية معينة للمسؤول
    """
    def decorator(view_func):
        @wraps(view_func)
        @regular_admin_required
        def _wrapped_view(request, *args, **kwargs):
            # المسؤول الكبير لديه جميع الصلاحيات
            if request.user.admin_profile.is_super_admin():
                return view_func(request, *args, **kwargs)
            
            # التحقق من الصلاحية للمسؤولين العاديين
            if not request.user.has_perm(permission_name):
                messages.error(request, _('ليس لديك صلاحية لتنفيذ هذا الإجراء'))
                return redirect('core:home')
            
            return view_func(request, *args, **kwargs)
        
        return _wrapped_view
    return decorator


def filter_data_by_admin(queryset, user, owner_field='created_by'):
    """
    دالة مساعدة لفلترة البيانات حسب المسؤول
    """
    # المسؤول الكبير يرى جميع البيانات
    if hasattr(user, 'admin_profile') and user.admin_profile.is_super_admin():
        return queryset
    
    # المسؤولين العاديين يرون بياناتهم فقط
    filter_kwargs = {owner_field: user}
    return queryset.filter(**filter_kwargs)


def get_admin_context(user):
    """
    دالة مساعدة للحصول على معلومات المسؤول للـ context
    """
    context = {
        'is_super_admin': False,
        'is_regular_admin': False,
        'admin_profile': None,
    }
    
    if hasattr(user, 'admin_profile'):
        context['admin_profile'] = user.admin_profile
        context['is_super_admin'] = user.admin_profile.is_super_admin()
        context['is_regular_admin'] = user.admin_profile.is_regular_admin()
    
    return context
