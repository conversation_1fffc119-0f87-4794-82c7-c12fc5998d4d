from django.core.management.base import BaseCommand
from organizations.models import OfficialPerson


class Command(BaseCommand):
    help = 'إضافة المسؤولين الرسميين إلى قاعدة البيانات'

    def handle(self, *args, **options):
        # أعضاء المجلس الوطني للشباب
        youth_council_members = [
            ('حضرة الأستاذ الفاضل', 'إسماعيل اديانكو سيلا', 'عضو المجلس الوطني للشباب'),
            ('حضرة الأستاذ الفاضل', 'التهامي سيدي محمد اعبيدي', 'عضو المجلس الوطني للشباب'),
            ('حضرة الأستاذ الفاضل', 'الداده ولد الشيخ', 'عضو المجلس الوطني للشباب'),
            ('حضرة الأستاذة الفاضلة', 'السالكة المهدي مبارك', 'عضو المجلس الوطني للشباب'),
            ('حضرة الأستاذ الفاضل', 'آمادو تيجان با', 'عضو المجلس الوطني للشباب'),
            ('حضرة الأستاذ الفاضل', 'بامبي كمارا', 'عضو المجلس الوطني للشباب'),
            ('حضرة الأستاذة الفاضلة', 'بندا ديامو ديالو', 'عضو المجلس الوطني للشباب'),
            ('حضرة الأستاذ الفاضل', 'جدو يسلم عبد الرحمن', 'عضو المجلس الوطني للشباب'),
            ('حضرة الأستاذ الفاضل', 'محمد المختار إسماعيل', 'الأمين العام للمجلس الوطني للشباب'),
            ('حضرة الأستاذة الفاضلة', 'زينب أحمد سالم', 'عضو المجلس الوطني للشباب'),
            ('حضرة الأستاذة الفاضلة', 'زينب بنت عبد الجليل', 'رئيسة المجلس الوطني للشباب'),
            ('حضرة الأستاذة الفاضلة', 'زينب فضيلي الرايس', 'عضو المجلس الوطني للشباب'),
            ('حضرة الأستاذة الفاضلة', 'سلوكة أحمد محمود اسويلم', 'عضو المجلس الوطني للشباب'),
            ('حضرة الأستاذ الفاضل', 'عبد الله الميمه', 'عضو المجلس الوطني للشباب'),
            ('حضرة الأستاذ الفاضل', 'عثمان أحمد معط الله', 'عضو المجلس الوطني للشباب'),
            ('حضرة الأستاذة الفاضلة', 'فاطمة أحمد بوسات', 'عضو المجلس الوطني للشباب'),
            ('حضرة الأستاذ الفاضل', 'محمد المالحة', 'عضو المجلس الوطني للشباب'),
            ('حضرة الأستاذ الفاضل', 'محمد خطري', 'عضو المجلس الوطني للشباب'),
            ('حضرة الأستاذ الفاضل', 'محمد خونه محمد خليفه', 'عضو المجلس الوطني للشباب'),
            ('حضرة الأستاذ الفاضل', 'محمد فال محمدن خالنا', 'عضو المجلس الوطني للشباب'),
            ('حضرة الأستاذ الفاضل', 'محمد يحي محمد المصطفى', 'عضو المجلس الوطني للشباب'),
            ('حضرة الأستاذة الفاضلة', 'مريم اعمر', 'عضو المجلس الوطني للشباب'),
            ('حضرة الأستاذة الفاضلة', 'مريم عمر بال', 'عضو المجلس الوطني للشباب'),
        ]

        # مسؤولون في وزارة الصيد
        fishing_ministry_officials = [
            ('حضرة الأستاذ الفاضل', 'المدير الإداري والمالي', 'المدير الإداري والمالي'),
            ('حضرة الأستاذ الفاضل', 'المستشار الإعلامي', 'المستشار الإعلامي'),
            ('حضرة الأستاذ الفاضل', 'قائد خفر السواحل', 'قائد خفر السواحل'),
            ('صاحب المعالي', 'المدير العام للشركة الموريتانية لتسويق الأسماك', 'المدير العام للشركة الموريتانية لتسويق الأسماك'),
            ('حضرة الأستاذ الفاضل', 'المدير العام للشركة الموريتانية لتوزيع الأسماك', 'المدير العام للشركة الموريتانية لتوزيع الأسماك'),
            ('صاحب المعالي', 'المدير العام للمكتب الوطني لموانئ الصيد', 'المدير العام للمكتب الوطني لموانئ الصيد'),
            ('حضرة الأستاذ الفاضل', 'المدير العام للمكتب الوطني للتفتيش الصحي لمنتجات الصيد وزراعة الأسماك', 'المدير العام للمكتب الوطني للتفتيش الصحي لمنتجات الصيد وزراعة الأسماك'),
            ('حضرة الأستاذ الفاضل', 'المدير العام للمعهد الموريتاني لبحوث المحيطات والصيد', 'المدير العام للمعهد الموريتاني لبحوث المحيطات والصيد'),
            ('حضرة الأستاذ الفاضل', 'المدير العام لميناء تانيت', 'المدير العام لميناء تانيت'),
            ('صاحب المعالي', 'المدير العام لميناء انواكشوط المستقل', 'المدير العام لميناء انواكشوط المستقل'),
            ('حضرة الأستاذ الفاضل', 'المدير العام لسوق السمك انواكشوط', 'المدير العام لسوق السمك انواكشوط'),
            ('حضرة الأستاذ الفاضل', 'المدير العام لميناء انجاكو', 'المدير العام لميناء انجاكو'),
            ('حضرة الأستاذ الفاضل', 'المدير العام لميناء انواذيبو المستقل', 'المدير العام لميناء انواذيبو المستقل'),
            ('حضرة الأستاذ الفاضل', 'المدير العام لميناء شاطئ الراحة', 'المدير العام لميناء شاطئ الراحة'),
            ('حضرة الأستاذ الفاضل', 'المدير العام للصيد التقليدي', 'المدير العام للصيد التقليدي'),
            ('حضرة الأستاذ الفاضل', 'حمادي حمادي', 'رئيس الاتحادية الوطنية للصيد'),
            ('حضرة الأستاذ الفاضل', 'محمد محمود الصادق', 'الأمين العام للاتحادية الوطنية للصيد'),
            ('حضرة الأستاذ الفاضل', 'إسلم ولد كربالي', 'رئيس الصيد التقليدي بالاتحادية الوطنية للصيد'),
        ]

        # إضافة أعضاء المجلس الوطني للشباب
        for title, name, position in youth_council_members:
            official, created = OfficialPerson.objects.get_or_create(
                name=name,
                position=position,
                category='youth_council',
                defaults={
                    'title': title,
                }
            )
            if created:
                self.stdout.write(
                    self.style.SUCCESS(f'تم إضافة {official.get_full_title()}')
                )

        # مسؤولون في وزارة الداخلية
        interior_ministry_officials = [
            ('صاحب السعادة', 'عبد الرحمن ولد الحسن', 'الأمين العام'),
            ('حضرة الأستاذ الفاضل', 'أبو بكر مكريكا', 'مدير الديوان'),
            ('صاحب المعالي', 'المدير العام للأمن الوطني', 'المدير العام للأمن الوطني'),
            ('حضرة الأستاذ الفاضل', 'المدير العام للصياغة والشؤون السياسية والحريات العامة', 'المدير العام للصياغة والشؤون السياسية والحريات العامة'),
            ('حضرة الأستاذ الفاضل', 'الإداري المدير العام لسجل السكان والوثائق المؤمنة', 'المدير العام لسجل السكان والوثائق المؤمنة'),
            ('حضرة الأستاذ الفاضل', 'سيس سيدي عبد القادر الجيلاني', 'المدير الإداري والمالي'),
            ('صاحب المعالي', 'القائد العام للحرس الوطني', 'القائد العام للحرس الوطني'),
            ('صاحب المعالي', 'المندوب العام للأمن المدني وتسيير الأزمات', 'المندوب العام للأمن المدني وتسيير الأزمات'),
            ('صاحب المعالي', 'محمد أحمد ولد محمد الأمين', 'وزير الداخلية وترقية اللامركزية والتنمية المحلية'),
            ('حضرة الأستاذ الفاضل', 'المدير العام للإدارة الإقليمية', 'المدير العام للإدارة الإقليمية'),
            ('حضرة الأستاذ الفاضل', 'المستشار الإعلامي', 'المستشار الإعلامي'),
            ('صاحب المعالي', 'يعقوب ولد سالم فال', 'الوزير المنتدب لدى وزير الداخلية وترقية اللامركزية والتنمية المحلية المكلف باللامركزية والتنمية المحلية'),
            ('حضرة الأستاذ الفاضل', 'المدير العام للبرمجة والتعاون', 'المدير العام للبرمجة والتعاون'),
            ('حضرة الأستاذ الفاضل', 'المدير العام للمجموعات الإقليمية', 'المدير العام للمجموعات الإقليمية'),
            ('حضرة الأستاذ الفاضل', 'مدير التعاون والبرمجة والدراسة', 'مدير التعاون والبرمجة والدراسة'),
            ('حضرة الأستاذ الفاضل', 'المدير العام لنظم المعلوماتية', 'المدير العام لنظم المعلوماتية'),
            ('صاحب المعالي', 'الوزير المنتدب لدى وزير الداخلية المكلف باللامركزية والتنمية المحلية', 'الوزير المنتدب لدى وزير الداخلية المكلف باللامركزية والتنمية المحلية'),
            ('حضرة الأستاذ الفاضل', 'مدير ديوان الوزير المنتدب لدى وزير الداخلية وترقية اللامركزية والتنمية المحلية المكلف باللامركزية والتنمية المحلية', 'مدير ديوان الوزير المنتدب لدى وزير الداخلية وترقية اللامركزية والتنمية المحلية المكلف باللامركزية والتنمية المحلية'),
        ]

        # مسؤولون في وزارة الدفاع
        defense_ministry_officials = [
            ('صاحب المعالي', 'اللواء صيدو صمبا جا', 'الأمين العام'),
            ('صاحب المعالي', 'قائد الأركان العامة للجيوش', 'قائد الأركان العامة للجيوش'),
            ('صاحب المعالي', 'قائد أركان الدرك الوطني', 'قائد أركان الدرك الوطني'),
            ('حضرة الأستاذ الفاضل', 'الكاتب الخاص لوزير الدفاع وشؤون المتقاعدين وأولاد الشهداء', 'الكاتب الخاص لوزير الدفاع وشؤون المتقاعدين وأولاد الشهداء'),
            ('صاحب المعالي', 'حننه ولد سيدي', 'وزير الدفاع وشؤون المتقاعدين وأولاد الشهداء'),
            ('صاحب المعالي', 'محمد أحمد ولد محمد الأمين', 'وزير الداخلية وترقية اللامركزية والتنمية المحلية'),
            ('صاحب المعالي', 'الفضيل ولد سيداتي ولد أحمد لولي', 'وزير الصيد والبنى التحتية البحرية والمينائية'),
        ]

        # إضافة مسؤولي وزارة الصيد
        for title, name, position in fishing_ministry_officials:
            official, created = OfficialPerson.objects.get_or_create(
                name=name,
                position=position,
                category='fishing_ministry',
                defaults={
                    'title': title,
                }
            )
            if created:
                self.stdout.write(
                    self.style.SUCCESS(f'تم إضافة {official.get_full_title()}')
                )

        # إضافة مسؤولي وزارة الداخلية
        for title, name, position in interior_ministry_officials:
            official, created = OfficialPerson.objects.get_or_create(
                name=name,
                position=position,
                category='interior_ministry',
                defaults={
                    'title': title,
                }
            )
            if created:
                self.stdout.write(
                    self.style.SUCCESS(f'تم إضافة {official.get_full_title()}')
                )

        # إضافة مسؤولي وزارة الدفاع
        for title, name, position in defense_ministry_officials:
            official, created = OfficialPerson.objects.get_or_create(
                name=name,
                position=position,
                category='defense_ministry',
                defaults={
                    'title': title,
                }
            )
            if created:
                self.stdout.write(
                    self.style.SUCCESS(f'تم إضافة {official.get_full_title()}')
                )

        self.stdout.write(
            self.style.SUCCESS('تم إضافة جميع المسؤولين الرسميين بنجاح!')
        )
