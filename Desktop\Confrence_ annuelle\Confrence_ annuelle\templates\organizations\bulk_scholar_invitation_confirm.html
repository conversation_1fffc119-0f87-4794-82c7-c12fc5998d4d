{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .hero-mini {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        position: relative;
        overflow: hidden;
    }

    .hero-mini::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('{% static "img/pattern.png" %}') repeat;
        opacity: 0.1;
    }

    .confirmation-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        border: none;
        overflow: hidden;
        margin-top: -50px;
        position: relative;
        z-index: 2;
    }

    .confirmation-card .card-header {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        padding: 2rem;
        text-align: center;
    }

    .confirmation-card .card-body {
        padding: 3rem;
    }

    .scholar-invitation-item {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        border: 2px solid #28a745;
        transition: all 0.3s ease;
    }

    .scholar-invitation-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }

    .scholar-name {
        font-size: 1.25rem;
        font-weight: 700;
        color: #155724;
        margin-bottom: 0.5rem;
    }

    .scholar-details {
        color: #6c757d;
        margin-bottom: 0.25rem;
        font-size: 0.9rem;
    }

    .whatsapp-btn {
        background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
        border: none;
        border-radius: 25px;
        padding: 0.75rem 2rem;
        font-size: 1rem;
        font-weight: 600;
        color: white;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
        box-shadow: 0 5px 15px rgba(37, 211, 102, 0.3);
    }

    .whatsapp-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(37, 211, 102, 0.4);
        color: white;
        text-decoration: none;
    }

    .whatsapp-btn i {
        margin-right: 0.5rem;
    }

    .action-buttons {
        text-align: center;
        margin-top: 3rem;
        padding-top: 2rem;
        border-top: 2px solid #e9ecef;
    }

    .btn-secondary {
        background: #6c757d;
        border: none;
        border-radius: 12px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-secondary:hover {
        background: #5a6268;
        transform: translateY(-2px);
    }

    .back-link {
        color: white;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .back-link:hover {
        color: #f8f9fa;
        text-decoration: none;
    }

    .success-icon {
        font-size: 4rem;
        color: #28a745;
        margin-bottom: 1rem;
    }

    .stats-section {
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        text-align: center;
        border: 2px solid #28a745;
    }

    .stat-number {
        font-size: 2.5rem;
        font-weight: 700;
        color: #155724;
        display: block;
    }

    .stat-label {
        color: #6c757d;
        font-weight: 600;
        margin-top: 0.5rem;
    }

    .bulk-actions {
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        text-align: center;
        border: 2px solid #ffc107;
    }

    .bulk-whatsapp-btn {
        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        border: none;
        border-radius: 25px;
        padding: 1rem 2.5rem;
        font-size: 1.1rem;
        font-weight: 600;
        color: white;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
        box-shadow: 0 8px 20px rgba(23, 162, 184, 0.3);
        margin: 0.5rem;
    }

    .bulk-whatsapp-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 12px 25px rgba(23, 162, 184, 0.4);
        color: white;
        text-decoration: none;
    }

    .phone-info {
        background: #e7f3ff;
        border-radius: 8px;
        padding: 0.5rem 1rem;
        margin-top: 0.5rem;
        border-left: 3px solid #007bff;
        font-size: 0.9rem;
    }

    .copy-btn {
        background: #007bff;
        border: none;
        border-radius: 6px;
        padding: 0.25rem 0.75rem;
        color: white;
        font-size: 0.75rem;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .copy-btn:hover {
        background: #0056b3;
    }

    .invitation-counter {
        position: sticky;
        top: 20px;
        background: white;
        border-radius: 10px;
        padding: 1rem;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
        text-align: center;
        border: 2px solid #28a745;
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="hero-mini text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-md-8 mx-auto text-center">
                <h1 class="display-5 fw-bold mb-3">{{ title }}</h1>
                <p class="lead">دعوات جاهزة للإرسال عبر WhatsApp</p>
                <a href="{% url 'organizations:scholar_list' %}" class="back-link">
                    <i class="fas fa-arrow-right me-2"></i> العودة إلى قائمة العلماء
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-12">
            <div class="card confirmation-card">
                <div class="card-header">
                    <div class="success-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h4 class="mb-0 text-white">تم إعداد الدعوات بنجاح!</h4>
                </div>
                <div class="card-body">
                    <!-- Statistics -->
                    <div class="stats-section">
                        <div class="row">
                            <div class="col-md-4">
                                <span class="stat-number">{{ invitation_links|length }}</span>
                                <div class="stat-label">إجمالي الدعوات</div>
                            </div>
                            <div class="col-md-4">
                                <span class="stat-number">{{ invitation_links|length }}</span>
                                <div class="stat-label">جاهزة للإرسال</div>
                            </div>
                            <div class="col-md-4">
                                <span class="stat-number">0</span>
                                <div class="stat-label">تم الإرسال</div>
                            </div>
                        </div>
                    </div>

                    <!-- Bulk Actions -->
                    <div class="bulk-actions">
                        <h5 class="mb-3">
                            <i class="fas fa-rocket me-2"></i>إجراءات سريعة
                        </h5>
                        <p class="mb-3">يمكنك إرسال جميع الدعوات بنقرة واحدة أو إرسالها واحدة تلو الأخرى</p>
                        
                        <button class="bulk-whatsapp-btn" onclick="openAllWhatsApp()">
                            <i class="fab fa-whatsapp me-2"></i>
                            إرسال جميع الدعوات
                        </button>
                        
                        <button class="bulk-whatsapp-btn" onclick="copyAllPhones()">
                            <i class="fas fa-copy me-2"></i>
                            نسخ جميع الأرقام
                        </button>
                    </div>

                    <!-- Invitation Counter -->
                    <div class="invitation-counter">
                        <strong>تقدم الإرسال:</strong>
                        <span id="sent-count">0</span> من {{ invitation_links|length }}
                        <div class="progress mt-2">
                            <div class="progress-bar bg-success" role="progressbar" style="width: 0%" id="progress-bar"></div>
                        </div>
                    </div>

                    <!-- Individual Invitations -->
                    <div class="row">
                        {% for link in invitation_links %}
                        <div class="col-md-6 col-lg-4">
                            <div class="scholar-invitation-item" data-scholar-id="{{ link.scholar.id }}">
                                <div class="scholar-name">{{ link.scholar.get_full_title_name }}</div>
                                
                                {% if link.scholar.position %}
                                    <div class="scholar-details">
                                        <i class="fas fa-briefcase me-1"></i> {{ link.scholar.position }}
                                    </div>
                                {% endif %}
                                
                                {% if link.scholar.organization %}
                                    <div class="scholar-details">
                                        <i class="fas fa-building me-1"></i> {{ link.scholar.organization }}
                                    </div>
                                {% endif %}
                                
                                {% if link.scholar.country %}
                                    <div class="scholar-details">
                                        <i class="fas fa-map-marker-alt me-1"></i> {{ link.scholar.country }}
                                        {% if link.scholar.city %}, {{ link.scholar.city }}{% endif %}
                                    </div>
                                {% endif %}

                                <div class="phone-info">
                                    <strong>الهاتف:</strong> {{ link.invitation.phone_number }}
                                    <button class="copy-btn ms-2" onclick="copyToClipboard('{{ link.invitation.phone_number }}')">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>

                                <div class="mt-3 text-center">
                                    <a href="{{ link.whatsapp_url }}" target="_blank" class="whatsapp-btn" onclick="markAsSent(this)">
                                        <i class="fab fa-whatsapp"></i>
                                        إرسال الدعوة
                                    </a>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>

                    <!-- Action Buttons -->
                    <div class="action-buttons">
                        <a href="{% url 'organizations:scholar_list' %}" class="btn btn-secondary btn-lg me-3">
                            <i class="fas fa-list me-2"></i>العودة إلى القائمة
                        </a>
                        <a href="{% url 'organizations:send_bulk_scholar_invitation' %}" class="btn btn-outline-primary btn-lg">
                            <i class="fas fa-plus me-2"></i>إرسال دعوات أخرى
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let sentCount = 0;
const totalCount = {{ invitation_links|length }};

function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        const btn = event.target.closest('.copy-btn');
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-check"></i>';
        btn.style.background = '#28a745';
        
        setTimeout(function() {
            btn.innerHTML = originalText;
            btn.style.background = '#007bff';
        }, 2000);
    }).catch(function(err) {
        console.error('فشل في نسخ النص: ', err);
        alert('فشل في نسخ النص');
    });
}

function markAsSent(button) {
    // Mark this invitation as sent
    const item = button.closest('.scholar-invitation-item');
    if (!item.classList.contains('sent')) {
        item.classList.add('sent');
        item.style.opacity = '0.7';
        button.innerHTML = '<i class="fas fa-check"></i> تم الإرسال';
        button.style.background = '#28a745';
        button.onclick = null;
        
        sentCount++;
        updateProgress();
    }
}

function updateProgress() {
    document.getElementById('sent-count').textContent = sentCount;
    const percentage = (sentCount / totalCount) * 100;
    document.getElementById('progress-bar').style.width = percentage + '%';
    
    if (sentCount === totalCount) {
        setTimeout(function() {
            alert('تم إرسال جميع الدعوات بنجاح! 🎉');
        }, 500);
    }
}

function openAllWhatsApp() {
    if (!confirm(`هل أنت متأكد من إرسال جميع الدعوات (${totalCount} دعوة)؟\n\nسيتم فتح ${totalCount} نافذة WhatsApp.`)) {
        return;
    }
    
    const whatsappLinks = document.querySelectorAll('.whatsapp-btn');
    let delay = 0;
    
    whatsappLinks.forEach(function(link, index) {
        if (!link.closest('.scholar-invitation-item').classList.contains('sent')) {
            setTimeout(function() {
                window.open(link.href, '_blank');
                markAsSent(link);
            }, delay);
            delay += 1000; // تأخير ثانية واحدة بين كل رابط
        }
    });
}

function copyAllPhones() {
    const phones = [];
    {% for link in invitation_links %}
    phones.push('{{ link.invitation.phone_number }}');
    {% endfor %}
    
    const phoneList = phones.join('\n');
    
    navigator.clipboard.writeText(phoneList).then(function() {
        alert(`تم نسخ ${phones.length} رقم هاتف إلى الحافظة!`);
    }).catch(function(err) {
        console.error('فشل في نسخ الأرقام: ', err);
        alert('فشل في نسخ الأرقام');
    });
}

// Track individual WhatsApp clicks
document.querySelectorAll('.whatsapp-btn').forEach(function(btn) {
    btn.addEventListener('click', function() {
        const scholarName = this.closest('.scholar-invitation-item').querySelector('.scholar-name').textContent;
        console.log('WhatsApp invitation sent for scholar:', scholarName);
    });
});
</script>
{% endblock %}
