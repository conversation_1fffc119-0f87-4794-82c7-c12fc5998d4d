# Generated by Django 5.2.4 on 2025-07-05 17:06

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('organizations', '0011_diplomaticcorps'),
    ]

    operations = [
        migrations.CreateModel(
            name='Notable',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200, verbose_name='الاسم الكامل')),
                ('title', models.CharField(blank=True, max_length=100, null=True, verbose_name='اللقب/المنصب')),
                ('category', models.CharField(choices=[('tribal_leader', 'زعيم قبلي'), ('religious_leader', 'زعيم ديني'), ('business_leader', 'رجل أعمال'), ('intellectual', 'مثقف'), ('social_leader', 'زعيم اجتماعي'), ('traditional_leader', 'زعيم تقليدي'), ('community_leader', 'زعيم مجتمعي'), ('elder', 'كبير السن'), ('other', 'أخرى')], max_length=30, verbose_name='الفئة')),
                ('region', models.CharField(choices=[('nouakchott', 'نواكشوط'), ('nouadhibou', 'نواذيبو'), ('adrar', 'أدرار'), ('dakhlet_nouadhibou', 'داخلت نواذيبو'), ('trarza', 'ترارزة'), ('brakna', 'البراكنة'), ('assaba', 'العصابة'), ('gorgol', 'كوركول'), ('guidimaka', 'كيديماغا'), ('hodh_ech_chargui', 'الحوض الشرقي'), ('hodh_el_gharbi', 'الحوض الغربي'), ('inchiri', 'إنشيري'), ('tagant', 'تكانت'), ('tiris_zemmour', 'تيرس زمور'), ('other', 'أخرى')], max_length=30, verbose_name='المنطقة')),
                ('tribe', models.CharField(blank=True, max_length=100, null=True, verbose_name='القبيلة')),
                ('birth_year', models.IntegerField(blank=True, null=True, verbose_name='سنة الميلاد')),
                ('phone', models.CharField(blank=True, max_length=20, null=True, verbose_name='رقم الهاتف')),
                ('email', models.EmailField(blank=True, max_length=254, null=True, verbose_name='البريد الإلكتروني')),
                ('address', models.TextField(blank=True, null=True, verbose_name='العنوان')),
                ('current_position', models.CharField(blank=True, max_length=200, null=True, verbose_name='المنصب الحالي')),
                ('status', models.CharField(choices=[('active', 'نشط'), ('inactive', 'غير نشط'), ('deceased', 'متوفى')], default='active', max_length=20, verbose_name='الحالة')),
                ('achievements', models.TextField(blank=True, null=True, verbose_name='الإنجازات والمساهمات')),
                ('social_influence', models.TextField(blank=True, null=True, verbose_name='التأثير الاجتماعي')),
                ('education', models.TextField(blank=True, null=True, verbose_name='التعليم')),
                ('family_info', models.TextField(blank=True, null=True, verbose_name='معلومات عائلية')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='ملاحظات')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإضافة')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
            ],
            options={
                'verbose_name': 'عين',
                'verbose_name_plural': 'الأعيان',
                'ordering': ['name'],
            },
        ),
    ]
