{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 15px;
        color: white;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }
    
    .stats-card h3 {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .stats-card p {
        margin: 0;
        opacity: 0.9;
    }
    
    .minister-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        margin-bottom: 20px;
    }
    
    .minister-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.15);
    }
    
    .minister-header {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        padding: 15px 20px;
        border-radius: 15px 15px 0 0;
    }
    
    .minister-name {
        font-size: 1.2rem;
        font-weight: bold;
        margin: 0;
    }
    
    .minister-ministry {
        font-size: 0.9rem;
        opacity: 0.9;
        margin: 0;
    }
    
    .status-badge {
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
    }
    
    .status-active {
        background-color: #d4edda;
        color: #155724;
    }
    
    .status-inactive {
        background-color: #f8d7da;
        color: #721c24;
    }
    
    .status-deceased {
        background-color: #e2e3e5;
        color: #383d41;
    }
    
    .service-duration {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 8px 15px;
        border-radius: 20px;
        font-size: 0.85rem;
        display: inline-block;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="text-center mb-4">
                <h1 class="display-4 mb-3">{{ title }}</h1>
                <p class="lead text-muted">إدارة وعرض بيانات الوزراء السابقين</p>
                <div class="mt-4">
                    <a href="{% url 'organizations:add_former_minister' %}" class="btn btn-primary me-2">
                        <i class="fas fa-plus"></i> إضافة وزير سابق
                    </a>
                    <a href="{% url 'organizations:export_former_ministers' %}" class="btn btn-success me-2">
                        <i class="fas fa-file-excel"></i> تصدير Excel
                    </a>

                    <!-- WhatsApp Invitation Buttons -->
                    <div class="btn-group me-2" role="group">
                        <button type="button" class="btn btn-outline-success dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fab fa-whatsapp"></i> دعوات WhatsApp
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#whatsappInvitationModal">
                                <i class="fab fa-whatsapp text-success"></i> دعوة فردية
                            </a></li>
                            <li><a class="dropdown-item" href="#" id="send-bulk-whatsapp-invitations">
                                <i class="fab fa-whatsapp text-success"></i> دعوات جماعية
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" id="send-selected-whatsapp-invitations">
                                <i class="fas fa-check-circle text-success"></i> دعوة المحددين (<span id="selected-whatsapp-count">0</span>)
                            </a></li>
                        </ul>
                    </div>

                    <!-- Email Invitation Buttons -->
                    <div class="btn-group me-2" role="group">
                        <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-envelope"></i> دعوات البريد الإلكتروني
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#emailInvitationModal">
                                <i class="fas fa-envelope text-primary"></i> دعوة فردية
                            </a></li>
                            <li><a class="dropdown-item" href="#" id="send-bulk-email-invitations">
                                <i class="fas fa-envelope text-primary"></i> دعوات جماعية
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" id="send-selected-email-invitations">
                                <i class="fas fa-envelope text-primary"></i> دعوة المحددين (<span id="selected-email-count">0</span>)
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الإحصائيات -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card text-center">
                <h3>{{ stats.total }}</h3>
                <p>إجمالي الوزراء السابقين</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card text-center">
                <h3>{{ stats.active }}</h3>
                <p>نشط</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card text-center">
                <h3>{{ stats.inactive }}</h3>
                <p>غير نشط</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card text-center">
                <h3>{{ stats.deceased }}</h3>
                <p>متوفى</p>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label">البحث</label>
                    <input type="text" name="search" class="form-control" value="{{ search_query }}" placeholder="البحث بالاسم أو الوزارة">
                </div>
                <div class="col-md-3">
                    <label class="form-label">الوزارة</label>
                    <select name="ministry" class="form-select">
                        <option value="">جميع الوزارات</option>
                        {% for ministry in ministries %}
                            <option value="{{ ministry }}" {% if ministry_filter == ministry %}selected{% endif %}>{{ ministry }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">الحكومة</label>
                    <select name="government" class="form-select">
                        <option value="">جميع الحكومات</option>
                        {% for government in governments %}
                            {% if government %}
                            <option value="{{ government }}" {% if government_filter == government %}selected{% endif %}>{{ government }}</option>
                            {% endif %}
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label">الحالة</label>
                    <select name="status" class="form-select">
                        <option value="">جميع الحالات</option>
                        {% for value, label in status_choices %}
                            <option value="{{ value }}" {% if status_filter == value %}selected{% endif %}>{{ label }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">بحث</button>
                    <a href="{% url 'organizations:former_ministers_list' %}" class="btn btn-secondary">إعادة تعيين</a>
                </div>
            </form>
        </div>
    </div>

    <!-- قائمة الوزراء السابقين -->
    <div class="row">
        {% if ministers %}
            {% for minister in ministers %}
            <div class="col-lg-6 col-xl-4">
                <div class="card minister-card">
                    <div class="minister-header">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0 me-2">
                                <input type="checkbox" class="form-check-input minister-checkbox" name="selected_ministers" value="{{ minister.id }}" id="minister-{{ minister.id }}">
                            </div>
                            <div class="flex-grow-1">
                                <h5 class="minister-name mb-1">{{ minister.name }}</h5>
                                <p class="minister-ministry mb-0">وزير {{ minister.ministry }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        {% if minister.government %}
                        <p class="mb-2">
                            <i class="fas fa-building text-muted me-2"></i>
                            <strong>الحكومة:</strong> {{ minister.government }}
                        </p>
                        {% endif %}
                        
                        <p class="mb-2">
                            <i class="fas fa-calendar text-muted me-2"></i>
                            <strong>فترة الخدمة:</strong> 
                            {{ minister.start_date|date:"Y-m-d" }}
                            {% if minister.end_date %} - {{ minister.end_date|date:"Y-m-d" }}{% endif %}
                        </p>
                        
                        <p class="mb-3">
                            <i class="fas fa-clock text-muted me-2"></i>
                            <span class="service-duration">{{ minister.get_service_duration }}</span>
                        </p>
                        
                        {% if minister.current_position %}
                        <p class="mb-2">
                            <i class="fas fa-briefcase text-muted me-2"></i>
                            <strong>المنصب الحالي:</strong> {{ minister.current_position }}
                        </p>
                        {% endif %}
                        
                        <div class="mb-3">
                            <span class="status-badge status-{{ minister.status }}">
                                {{ minister.get_status_display }}
                            </span>
                        </div>
                        
                        <!-- Invitation Buttons -->
                        <div class="mb-3">
                            <div class="d-flex gap-2 flex-wrap">
                                {% if minister.phone %}
                                <button class="btn btn-sm btn-success send-whatsapp-btn"
                                        data-minister-id="{{ minister.id }}"
                                        data-minister-name="{{ minister.name }}"
                                        data-minister-phone="{{ minister.phone }}"
                                        data-bs-toggle="modal"
                                        data-bs-target="#whatsappInvitationModal">
                                    <i class="fab fa-whatsapp"></i> دعوة WhatsApp
                                </button>
                                {% endif %}

                                {% if minister.email %}
                                <button class="btn btn-sm btn-primary send-email-btn"
                                        data-minister-id="{{ minister.id }}"
                                        data-minister-name="{{ minister.name }}"
                                        data-minister-email="{{ minister.email }}"
                                        data-bs-toggle="modal"
                                        data-bs-target="#emailInvitationModal">
                                    <i class="fas fa-envelope"></i> دعوة بريد إلكتروني
                                </button>
                                {% endif %}
                            </div>
                        </div>

                        <div class="d-flex gap-2">
                            <a href="{% url 'organizations:edit_former_minister' minister.pk %}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-edit"></i> تعديل
                            </a>
                            <a href="{% url 'organizations:delete_former_minister' minister.pk %}" class="btn btn-sm btn-outline-danger">
                                <i class="fas fa-trash"></i> حذف
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-user-tie fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد بيانات وزراء سابقين</h5>
                    <p class="text-muted">ابدأ بإضافة وزراء سابقين جدد</p>
                    <a href="{% url 'organizations:add_former_minister' %}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> إضافة وزير سابق
                    </a>
                </div>
            </div>
        {% endif %}
    </div>
</div>

<!-- WhatsApp Invitation Modal -->
<div class="modal fade" id="whatsappInvitationModal" tabindex="-1" aria-labelledby="whatsappInvitationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="whatsappInvitationModalLabel">
                    <i class="fab fa-whatsapp me-2"></i>إرسال دعوة WhatsApp
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'organizations:send_minister_whatsapp_invitation' %}">
                {% csrf_token %}
                <input type="hidden" id="whatsapp-minister-id" name="minister_id">
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fab fa-whatsapp me-2"></i>
                        سيتم إرسال دعوة WhatsApp إلى: <strong id="whatsapp-minister-name"></strong>
                        <br>رقم الهاتف: <strong id="whatsapp-minister-phone"></strong>
                    </div>

                    <div class="mb-3">
                        <label for="whatsapp-subject" class="form-label">موضوع الرسالة</label>
                        <input type="text" class="form-control" id="whatsapp-subject" name="subject"
                               value="دعوة للمشاركة في مؤتمر السيرة النبوية السنوي" required>
                    </div>

                    <div class="mb-3">
                        <label for="whatsapp-message" class="form-label">نص الرسالة</label>
                        <textarea class="form-control" id="whatsapp-message" name="message" rows="8" required>بسم الله الرحمن الرحيم

معالي الوزير السابق المحترم

السلام عليكم ورحمة الله وبركاته

يسعدنا أن ندعوكم للمشاركة في مؤتمر السيرة النبوية السنوي الذي يهدف إلى نشر العلم والمعرفة حول سيرة النبي محمد صلى الله عليه وسلم.

نتطلع إلى مشاركتكم القيمة وإثراء المؤتمر بخبرتكم الوزارية والإدارية.

وفقكم الله وبارك فيكم

مع أطيب التحيات
لجنة تنظيم المؤتمر</textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fab fa-whatsapp me-2"></i>إرسال الدعوة
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Email Invitation Modal -->
<div class="modal fade" id="emailInvitationModal" tabindex="-1" aria-labelledby="emailInvitationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="emailInvitationModalLabel">
                    <i class="fas fa-envelope me-2"></i>إرسال دعوة بريد إلكتروني
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'organizations:send_minister_email_invitation' %}">
                {% csrf_token %}
                <input type="hidden" id="email-minister-id" name="minister_id">
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-envelope me-2"></i>
                        سيتم إرسال دعوة بريد إلكتروني إلى: <strong id="email-minister-name"></strong>
                        <br>البريد الإلكتروني: <strong id="email-minister-email"></strong>
                    </div>

                    <div class="mb-3">
                        <label for="email-subject" class="form-label">موضوع الرسالة</label>
                        <input type="text" class="form-control" id="email-subject" name="subject"
                               value="دعوة للمشاركة في مؤتمر السيرة النبوية السنوي" required>
                    </div>

                    <div class="mb-3">
                        <label for="email-message" class="form-label">نص الرسالة</label>
                        <textarea class="form-control" id="email-message" name="message" rows="8" required>بسم الله الرحمن الرحيم

معالي الوزير السابق المحترم

السلام عليكم ورحمة الله وبركاته

يسعدنا أن ندعوكم للمشاركة في مؤتمر السيرة النبوية السنوي الذي يهدف إلى نشر العلم والمعرفة حول سيرة النبي محمد صلى الله عليه وسلم.

نتطلع إلى مشاركتكم القيمة وإثراء المؤتمر بخبرتكم الوزارية والإدارية.

وفقكم الله وبارك فيكم

مع أطيب التحيات
لجنة تنظيم المؤتمر</textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-envelope me-2"></i>إرسال الدعوة
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Bulk WhatsApp Invitation Modal -->
<div class="modal fade" id="bulkWhatsappInvitationModal" tabindex="-1" aria-labelledby="bulkWhatsappInvitationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bulkWhatsappInvitationModalLabel">
                    <i class="fab fa-whatsapp me-2"></i>إرسال دعوات جماعية WhatsApp
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'organizations:send_bulk_minister_whatsapp_invitation' %}">
                {% csrf_token %}
                <input type="hidden" id="bulk-whatsapp-minister-ids" name="minister_ids">
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fab fa-whatsapp me-2"></i>
                        سيتم إرسال دعوات WhatsApp إلى <strong id="bulk-whatsapp-count">جميع</strong> الوزراء السابقين المحددين
                    </div>

                    <div class="mb-3">
                        <label for="bulk-whatsapp-subject" class="form-label">موضوع الرسالة</label>
                        <input type="text" class="form-control" id="bulk-whatsapp-subject" name="subject"
                               value="دعوة للمشاركة في مؤتمر السيرة النبوية السنوي" required>
                    </div>

                    <div class="mb-3">
                        <label for="bulk-whatsapp-message" class="form-label">نص الرسالة</label>
                        <textarea class="form-control" id="bulk-whatsapp-message" name="message" rows="8" required>بسم الله الرحمن الرحيم

معالي الوزراء السابقين المحترمين

السلام عليكم ورحمة الله وبركاته

يسعدنا أن ندعوكم للمشاركة في مؤتمر السيرة النبوية السنوي الذي يهدف إلى نشر العلم والمعرفة حول سيرة النبي محمد صلى الله عليه وسلم.

نتطلع إلى مشاركتكم القيمة وإثراء المؤتمر بخبرتكم الوزارية والإدارية.

وفقكم الله وبارك فيكم

مع أطيب التحيات
لجنة تنظيم المؤتمر</textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fab fa-whatsapp me-2"></i>إرسال الدعوات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Bulk Email Invitation Modal -->
<div class="modal fade" id="bulkEmailInvitationModal" tabindex="-1" aria-labelledby="bulkEmailInvitationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bulkEmailInvitationModalLabel">
                    <i class="fas fa-envelope me-2"></i>إرسال دعوات جماعية بالبريد الإلكتروني
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'organizations:send_bulk_minister_email_invitation' %}">
                {% csrf_token %}
                <input type="hidden" id="bulk-email-minister-ids" name="minister_ids">
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-envelope me-2"></i>
                        سيتم إرسال دعوات بريد إلكتروني إلى <strong id="bulk-email-count">جميع</strong> الوزراء السابقين المحددين
                    </div>

                    <div class="mb-3">
                        <label for="bulk-email-subject" class="form-label">موضوع الرسالة</label>
                        <input type="text" class="form-control" id="bulk-email-subject" name="subject"
                               value="دعوة للمشاركة في مؤتمر السيرة النبوية السنوي" required>
                    </div>

                    <div class="mb-3">
                        <label for="bulk-email-message" class="form-label">نص الرسالة</label>
                        <textarea class="form-control" id="bulk-email-message" name="message" rows="8" required>بسم الله الرحمن الرحيم

معالي الوزراء السابقين المحترمين

السلام عليكم ورحمة الله وبركاته

يسعدنا أن ندعوكم للمشاركة في مؤتمر السيرة النبوية السنوي الذي يهدف إلى نشر العلم والمعرفة حول سيرة النبي محمد صلى الله عليه وسلم.

نتطلع إلى مشاركتكم القيمة وإثراء المؤتمر بخبرتكم الوزارية والإدارية.

وفقكم الله وبارك فيكم

مع أطيب التحيات
لجنة تنظيم المؤتمر</textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-envelope me-2"></i>إرسال الدعوات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Checkbox functionality
    const ministerCheckboxes = document.querySelectorAll('.minister-checkbox');
    const selectedCountSpan = document.getElementById('selected-whatsapp-count');
    const selectedEmailCountSpan = document.getElementById('selected-email-count');

    function updateSelectedCount() {
        const selectedCheckboxes = document.querySelectorAll('.minister-checkbox:checked');
        const count = selectedCheckboxes.length;

        if (selectedCountSpan) selectedCountSpan.textContent = count;
        if (selectedEmailCountSpan) selectedEmailCountSpan.textContent = count;
    }

    // Add event listeners to all checkboxes
    ministerCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedCount);
    });

    // Add "Select All" functionality
    const selectAllBtn = document.createElement('button');
    selectAllBtn.className = 'btn btn-sm btn-outline-secondary me-2';
    selectAllBtn.innerHTML = '<i class="fas fa-check-square"></i> تحديد الكل';
    selectAllBtn.type = 'button';

    // Insert select all button before the invitation dropdown
    const invitationDropdown = document.querySelector('.btn-group');
    if (invitationDropdown && ministerCheckboxes.length > 0) {
        invitationDropdown.parentNode.insertBefore(selectAllBtn, invitationDropdown);
    }

    let allSelected = false;
    selectAllBtn.addEventListener('click', function() {
        allSelected = !allSelected;

        ministerCheckboxes.forEach(checkbox => {
            checkbox.checked = allSelected;
        });

        if (allSelected) {
            this.innerHTML = '<i class="fas fa-square"></i> إلغاء التحديد';
            this.classList.remove('btn-outline-secondary');
            this.classList.add('btn-secondary');
        } else {
            this.innerHTML = '<i class="fas fa-check-square"></i> تحديد الكل';
            this.classList.remove('btn-secondary');
            this.classList.add('btn-outline-secondary');
        }

        updateSelectedCount();
    });

    // Initial count update
    updateSelectedCount();

    // معالجة أزرار WhatsApp
    $('.send-whatsapp-btn').on('click', function() {
        const ministerId = $(this).data('minister-id');
        const ministerName = $(this).data('minister-name');
        const ministerPhone = $(this).data('minister-phone');

        $('#whatsapp-minister-id').val(ministerId);
        $('#whatsapp-minister-name').text(ministerName);
        $('#whatsapp-minister-phone').text(ministerPhone);
    });

    // معالجة أزرار البريد الإلكتروني
    $('.send-email-btn').on('click', function() {
        const ministerId = $(this).data('minister-id');
        const ministerName = $(this).data('minister-name');
        const ministerEmail = $(this).data('minister-email');

        $('#email-minister-id').val(ministerId);
        $('#email-minister-name').text(ministerName);
        $('#email-minister-email').text(ministerEmail);
    });

    // إرسال دعوات WhatsApp للمحددين
    $('#send-selected-whatsapp-invitations').on('click', function(e) {
        e.preventDefault();

        const selectedMinisters = $('.minister-checkbox:checked');

        if (selectedMinisters.length === 0) {
            alert('يرجى تحديد وزير سابق واحد على الأقل');
            return;
        }

        const ministerIds = selectedMinisters.map(function() {
            return this.value;
        }).get();

        $('#bulk-whatsapp-minister-ids').val(ministerIds.join(','));
        $('#bulk-whatsapp-count').text(ministerIds.length);
        $('#bulkWhatsappInvitationModal').modal('show');
    });

    // إرسال دعوات WhatsApp جماعية
    $('#send-bulk-whatsapp-invitations').on('click', function(e) {
        e.preventDefault();

        $('#bulk-whatsapp-minister-ids').val('');
        $('#bulk-whatsapp-count').text('جميع');
        $('#bulkWhatsappInvitationModal').modal('show');
    });

    // إرسال دعوات بريد إلكتروني للمحددين
    $('#send-selected-email-invitations').on('click', function(e) {
        e.preventDefault();

        const selectedMinisters = $('.minister-checkbox:checked');

        if (selectedMinisters.length === 0) {
            alert('يرجى تحديد وزير سابق واحد على الأقل');
            return;
        }

        const ministerIds = selectedMinisters.map(function() {
            return this.value;
        }).get();

        $('#bulk-email-minister-ids').val(ministerIds.join(','));
        $('#bulk-email-count').text(ministerIds.length);
        $('#bulkEmailInvitationModal').modal('show');
    });

    // إرسال دعوات بريد إلكتروني جماعية
    $('#send-bulk-email-invitations').on('click', function(e) {
        e.preventDefault();

        $('#bulk-email-minister-ids').val('');
        $('#bulk-email-count').text('جميع');
        $('#bulkEmailInvitationModal').modal('show');
    });
});
</script>
{% endblock %}
