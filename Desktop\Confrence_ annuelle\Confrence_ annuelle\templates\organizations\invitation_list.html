{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}قائمة الدعوات{% endblock %}

{% block extra_css %}
<style>
    .hero-mini {
        background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
        color: white;
        padding: 3rem 0;
        text-align: center;
        position: relative;
        overflow: hidden;
        margin-bottom: 2rem;
    }
    
    .hero-mini::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url('{% static "img/pattern.png" %}');
        opacity: 0.1;
    }
    
    .hero-mini .container {
        position: relative;
        z-index: 1;
    }
    
    .invitation-card {
        border: none;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
        border-radius: 0.5rem;
        overflow: hidden;
        transition: all 0.3s ease;
        margin-bottom: 1.5rem;
    }
    
    .invitation-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.15);
    }
    
    .invitation-card .card-header {
        background: linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%);
        border-bottom: none;
        padding: 1rem 1.5rem;
    }
    
    .invitation-card .card-body {
        padding: 1.5rem;
    }
    
    .invitation-filters {
        background-color: #f8f9fa;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.05);
    }
    
    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 50px;
        font-weight: 600;
        font-size: 0.8rem;
    }
    
    .status-draft {
        background-color: #e9ecef;
        color: #495057;
    }
    
    .status-sent {
        background-color: #d4edda;
        color: #155724;
    }
    
    .status-cancelled {
        background-color: #f8d7da;
        color: #721c24;
    }
    
    .status-responded {
        background-color: #cce5ff;
        color: #004085;
    }
    
    .invitation-actions .btn {
        padding: 0.375rem 0.75rem;
        font-size: 0.875rem;
        border-radius: 0.25rem;
        margin-right: 0.25rem;
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="hero-mini text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-md-8 mx-auto text-center">
                <h1 class="display-5 fw-bold mb-3">قائمة الدعوات</h1>
                <p class="lead">إدارة دعوات المؤسسات للمشاركة في المؤتمر</p>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Action Buttons -->
    <div class="row mb-4">
        <div class="col-12 text-center">
            <a href="{% url 'organizations:send_invitation' %}" class="btn btn-primary me-2">
                <i class="fas fa-paper-plane me-2"></i>إرسال دعوة فردية
            </a>
            <a href="{% url 'organizations:send_bulk_invitation' %}" class="btn btn-success me-2">
                <i class="fas fa-envelope-open-text me-2"></i>إرسال دعوات جماعية
            </a>
            <button type="button" id="bulk-delete-btn" class="btn btn-danger" disabled>
                <i class="fas fa-trash me-2"></i>حذف الدعوات المحددة
            </button>
        </div>
    </div>
    
    <!-- Search Form -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <form method="get" action="{% url 'organizations:invitation_list' %}" class="row g-3">
                        <div class="col-md-4">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" name="search" class="form-control" placeholder="بحث في الموضوع أو المؤسسة" value="{{ request.GET.search }}">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select name="status" class="form-select">
                                <option value="" {% if not request.GET.status %}selected{% endif %}>جميع الحالات</option>
                                <option value="draft" {% if request.GET.status == 'draft' %}selected{% endif %}>مسودة</option>
                                <option value="sent" {% if request.GET.status == 'sent' %}selected{% endif %}>مرسلة</option>
                                <option value="cancelled" {% if request.GET.status == 'cancelled' %}selected{% endif %}>ملغاة</option>
                                <option value="responded" {% if request.GET.status == 'responded' %}selected{% endif %}>تم الرد</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <select name="sort" class="form-select">
                                <option value="-sent_at" {% if request.GET.sort == '-sent_at' %}selected{% endif %}>الأحدث أولاً</option>
                                <option value="sent_at" {% if request.GET.sort == 'sent_at' %}selected{% endif %}>الأقدم أولاً</option>
                                <option value="subject" {% if request.GET.sort == 'subject' %}selected{% endif %}>الموضوع (أ-ي)</option>
                                <option value="-subject" {% if request.GET.sort == '-subject' %}selected{% endif %}>الموضوع (ي-أ)</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-primary w-100">تطبيق</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="invitation-filters">
                <h5 class="mb-3"><i class="fas fa-filter me-2"></i>تصفية الدعوات</h5>
                <div class="row">
                    <div class="col-md-12">
                        <div class="btn-group w-100" role="group">
                            <a href="{% url 'organizations:invitation_list' %}" class="btn {% if not request.GET.status %}btn-primary{% else %}btn-outline-primary{% endif %}">
                                <i class="fas fa-list-ul me-1"></i> الكل
                            </a>
                            <a href="{% url 'organizations:invitation_list' %}?status=draft" class="btn {% if request.GET.status == 'draft' %}btn-primary{% else %}btn-outline-primary{% endif %}">
                                <i class="fas fa-file me-1"></i> مسودة
                            </a>
                            <a href="{% url 'organizations:invitation_list' %}?status=sent" class="btn {% if request.GET.status == 'sent' %}btn-primary{% else %}btn-outline-primary{% endif %}">
                                <i class="fas fa-paper-plane me-1"></i> مرسلة
                            </a>
                            <a href="{% url 'organizations:invitation_list' %}?status=cancelled" class="btn {% if request.GET.status == 'cancelled' %}btn-primary{% else %}btn-outline-primary{% endif %}">
                                <i class="fas fa-ban me-1"></i> ملغاة
                            </a>
                            <a href="{% url 'organizations:invitation_list' %}?status=responded" class="btn {% if request.GET.status == 'responded' %}btn-primary{% else %}btn-outline-primary{% endif %}">
                                <i class="fas fa-reply me-1"></i> تم الرد
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Invitations List -->
    {% if invitations %}
    <form id="bulk-delete-form" method="post" action="{% url 'organizations:bulk_invitation_delete' %}">
        {% csrf_token %}
        <div class="row">
            {% for invitation in invitations %}
            <div class="col-md-6">
                <div class="card invitation-card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <div class="form-check me-2">
                                <input class="form-check-input invitation-checkbox" type="checkbox" name="invitation_ids" value="{{ invitation.id }}" id="invitation-{{ invitation.id }}">
                                <label class="form-check-label" for="invitation-{{ invitation.id }}"></label>
                            </div>
                            <h5 class="mb-0">{{ invitation.subject|truncatechars:40 }}</h5>
                        </div>
                        <span class="status-badge status-{{ invitation.status }}">
                            {% if invitation.status == 'draft' %}
                            <i class="fas fa-file me-1"></i> مسودة
                            {% elif invitation.status == 'sent' %}
                            <i class="fas fa-paper-plane me-1"></i> مرسلة
                            {% elif invitation.status == 'cancelled' %}
                            <i class="fas fa-ban me-1"></i> ملغاة
                            {% elif invitation.status == 'responded' %}
                            <i class="fas fa-reply me-1"></i> تم الرد
                            {% endif %}
                        </span>
                    </div>
                    <div class="card-body">
                    <div class="mb-3">
                        <strong><i class="fas fa-building me-2 text-primary"></i>المؤسسة:</strong>
                        <a href="{% url 'organizations:organization_detail' invitation.organization.pk %}" class="text-decoration-none">
                            {{ invitation.organization.name }}
                        </a>
                    </div>
                    
                    <div class="mb-3">
                        <strong><i class="fas fa-calendar-alt me-2 text-primary"></i>تاريخ الإرسال:</strong>
                        {% if invitation.is_sent %}
                        {{ invitation.sent_at|date:"Y-m-d H:i" }}
                        {% else %}
                        <span class="text-muted">لم يتم الإرسال بعد</span>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        <strong><i class="fas fa-envelope-open-text me-2 text-primary"></i>محتوى الرسالة:</strong>
                        <p class="text-muted mt-1">{{ invitation.message|truncatechars:100 }}</p>
                    </div>
                    
                    <div class="invitation-actions text-end">
                        <a href="{% url 'organizations:invitation_detail' invitation.pk %}" class="btn btn-sm btn-primary">
                            <i class="fas fa-eye me-1"></i> عرض
                        </a>
                        
                        {% if invitation.status == 'draft' %}
                        <a href="{% url 'organizations:invitation_update' invitation.pk %}" class="btn btn-sm btn-info">
                            <i class="fas fa-edit me-1"></i> تعديل
                        </a>
                        <a href="{% url 'organizations:invitation_send' invitation.pk %}" class="btn btn-sm btn-success">
                            <i class="fas fa-paper-plane me-1"></i> إرسال
                        </a>
                        {% elif invitation.status == 'sent' %}
                        <a href="{% url 'organizations:invitation_cancel' invitation.pk %}" class="btn btn-sm btn-warning">
                            <i class="fas fa-ban me-1"></i> إلغاء
                        </a>
                        {% endif %}
                        
                        <a href="{% url 'organizations:invitation_delete' invitation.pk %}" class="btn btn-sm btn-danger">
                            <i class="fas fa-trash me-1"></i> حذف
                        </a>
                    </div>
                </div>
            </div>
        </div>
        {% endfor %}
        </div>
    </form>
    {% else %}
    <div class="row">
        <div class="col-12 text-center py-5">
            <div class="mb-4">
                <i class="fas fa-envelope-open text-muted fa-5x"></i>
            </div>
            <h3 class="text-muted mb-3">لا توجد دعوات</h3>
            <p class="text-muted mb-4">لم يتم إنشاء أي دعوات حتى الآن. يمكنك إنشاء دعوة جديدة باستخدام الأزرار أعلاه.</p>
            <div class="mt-3">
                <a href="{% url 'organizations:send_invitation' %}" class="btn btn-primary me-2">
                    <i class="fas fa-paper-plane me-2"></i>إرسال دعوة فردية
                </a>
                <a href="{% url 'organizations:send_bulk_invitation' %}" class="btn btn-success">
                    <i class="fas fa-envelope-open-text me-2"></i>إرسال دعوات جماعية
                </a>
            </div>
        </div>
    </div>
    {% endif %}
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // الحصول على جميع خانات الاختيار والزر
        const checkboxes = document.querySelectorAll('.invitation-checkbox');
        const bulkDeleteBtn = document.getElementById('bulk-delete-btn');
        const bulkDeleteForm = document.getElementById('bulk-delete-form');
        
        // تحديث حالة زر الحذف الجماعي
        function updateBulkDeleteButton() {
            const checkedBoxes = document.querySelectorAll('.invitation-checkbox:checked');
            bulkDeleteBtn.disabled = checkedBoxes.length === 0;
            
            // إظهار عدد العناصر المحددة
            if (checkedBoxes.length > 0) {
                bulkDeleteBtn.innerHTML = `<i class="fas fa-trash me-2"></i>حذف العناصر المحددة (${checkedBoxes.length})`;
            } else {
                bulkDeleteBtn.innerHTML = `<i class="fas fa-trash me-2"></i>حذف الدعوات المحددة`;
            }
        }
        
        // إضافة مستمع أحداث لكل خانة اختيار
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updateBulkDeleteButton);
        });
        
        // إضافة مستمع أحداث لزر الحذف الجماعي
        bulkDeleteBtn.addEventListener('click', function() {
            if (confirm('هل أنت متأكد من حذف الدعوات المحددة؟')) {
                bulkDeleteForm.submit();
            }
        });
        
        // إضافة خانة اختيار الكل
        const selectAllContainer = document.createElement('div');
        selectAllContainer.className = 'mb-3';
        selectAllContainer.innerHTML = `
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="select-all-invitations">
                <label class="form-check-label" for="select-all-invitations">تحديد الكل</label>
            </div>
        `;
        
        // إضافة خانة اختيار الكل قبل صف الدعوات
        const invitationsRow = document.querySelector('#bulk-delete-form .row');
        if (invitationsRow) {
            invitationsRow.parentNode.insertBefore(selectAllContainer, invitationsRow);
        }
        
        // إضافة وظيفة تحديد الكل
        const selectAllCheckbox = document.getElementById('select-all-invitations');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                checkboxes.forEach(checkbox => {
                    checkbox.checked = selectAllCheckbox.checked;
                });
                updateBulkDeleteButton();
            });
        }
    });
</script>
{% endblock %}
{% endblock %}