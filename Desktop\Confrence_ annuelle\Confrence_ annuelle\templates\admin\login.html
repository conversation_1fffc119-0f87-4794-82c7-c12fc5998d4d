{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block extrahead %}
{{ block.super }}
<style>
    body.login {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 20px;
    }

    .login-container {
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 60px rgba(0,0,0,0.2);
        padding: 3rem;
        width: 100%;
        max-width: 450px;
        text-align: center;
    }

    .login-header {
        margin-bottom: 2rem;
    }

    .login-header h1 {
        color: var(--primary-color);
        font-weight: 700;
        font-size: 2rem;
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
    }

    .login-header p {
        color: #6c757d;
        font-size: 1.1rem;
        margin: 0;
    }

    .login-form {
        text-align: right;
    }

    .form-row {
        margin-bottom: 1.5rem;
    }

    .form-row label {
        display: block;
        font-weight: 600;
        color: var(--secondary-color);
        margin-bottom: 0.5rem;
        font-size: 1rem;
    }

    .form-row input[type="text"],
    .form-row input[type="password"] {
        width: 100%;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 15px 20px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: #f8f9fa;
        color: var(--dark-color);
        box-sizing: border-box;
    }

    .form-row input[type="text"]:focus,
    .form-row input[type="password"]:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(44, 90, 160, 0.1);
        outline: none;
        background: white;
    }

    .submit-row {
        margin-top: 2rem;
    }

    .submit-row input[type="submit"] {
        width: 100%;
        background: linear-gradient(135deg, var(--primary-color), var(--info-color));
        color: white;
        border: none;
        border-radius: 10px;
        padding: 15px 20px;
        font-weight: 600;
        font-size: 1.1rem;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
    }

    .submit-row input[type="submit"]:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 30px rgba(44, 90, 160, 0.4);
    }

    .submit-row input[type="submit"]:before {
        content: '🔐';
        font-size: 1.2rem;
    }

    .errorlist {
        background: rgba(231, 76, 60, 0.1);
        border: 2px solid var(--danger-color);
        border-radius: 10px;
        padding: 1rem 1.5rem;
        margin: 1rem 0;
        color: var(--danger-color);
        list-style: none;
        text-align: center;
    }

    .errorlist li {
        margin: 0.5rem 0;
        font-weight: 500;
    }

    .errorlist:before {
        content: '⚠️';
        margin-left: 10px;
        font-size: 1.2rem;
    }

    .login-footer {
        margin-top: 2rem;
        padding-top: 2rem;
        border-top: 1px solid #e9ecef;
        color: #6c757d;
        font-size: 0.9rem;
    }

    .login-footer a {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500;
    }

    .login-footer a:hover {
        color: var(--info-color);
    }

    /* إضافة أيقونات للحقول */
    .form-row {
        position: relative;
    }

    .form-row.username:before {
        content: '👤';
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 1.2rem;
        z-index: 1;
        margin-top: 12px;
    }

    .form-row.password:before {
        content: '🔒';
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 1.2rem;
        z-index: 1;
        margin-top: 12px;
    }

    .form-row.username input,
    .form-row.password input {
        padding-left: 50px;
    }

    /* تأثيرات إضافية */
    .login-container {
        animation: slideUp 0.6s ease-out;
    }

    @keyframes slideUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .form-row input[type="text"],
    .form-row input[type="password"] {
        animation: fadeIn 0.8s ease-out;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
        }
        to {
            opacity: 1;
        }
    }

    /* Responsive */
    @media (max-width: 768px) {
        .login-container {
            padding: 2rem 1.5rem;
            margin: 10px;
        }
        
        .login-header h1 {
            font-size: 1.5rem;
        }
        
        .login-header p {
            font-size: 1rem;
        }
    }

    /* إخفاء العناصر غير المرغوب فيها */
    #header {
        display: none;
    }
    
    .breadcrumbs {
        display: none;
    }
    
    #footer {
        display: none;
    }
</style>
{% endblock %}

{% block bodyclass %}{{ block.super }} login{% endblock %}

{% block usertools %}{% endblock %}

{% block nav-global %}{% endblock %}

{% block content %}
<div class="login-container">
    <div class="login-header">
        <h1>
            <i class="fas fa-graduation-cap"></i>
            لوحة إدارة المؤتمر
        </h1>
        <p>مؤتمر السيرة النبوية السنوي</p>
    </div>

    {% if form.errors and not form.non_field_errors %}
        <div class="errorlist">
            <li>{% trans 'يرجى تصحيح الأخطاء أدناه.' %}</li>
        </div>
    {% endif %}

    {% if form.non_field_errors %}
        <div class="errorlist">
            {% for error in form.non_field_errors %}
                <li>{{ error }}</li>
            {% endfor %}
        </div>
    {% endif %}

    <form action="{{ app_path }}" method="post" id="login-form" class="login-form">{% csrf_token %}
        <div class="form-row username">
            {{ form.username.label_tag }}
            {{ form.username }}
            {% if form.username.errors %}
                <div class="errorlist">
                    {% for error in form.username.errors %}
                        <li>{{ error }}</li>
                    {% endfor %}
                </div>
            {% endif %}
        </div>

        <div class="form-row password">
            {{ form.password.label_tag }}
            {{ form.password }}
            {% if form.password.errors %}
                <div class="errorlist">
                    {% for error in form.password.errors %}
                        <li>{{ error }}</li>
                    {% endfor %}
                </div>
            {% endif %}
        </div>

        {% url 'admin_password_reset' as password_reset_url %}
        {% if password_reset_url %}
            <div class="password-reset-link">
                <a href="{{ password_reset_url }}">{% trans 'نسيت كلمة المرور؟' %}</a>
            </div>
        {% endif %}

        <div class="submit-row">
            <input type="submit" value="{% trans 'تسجيل الدخول' %}">
            <input type="hidden" name="next" value="{{ next }}">
        </div>
    </form>

    <div class="login-footer">
        <p>© 2024 مؤتمر السيرة النبوية السنوي. جميع الحقوق محفوظة.</p>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تركيز على حقل اسم المستخدم
    const usernameField = document.querySelector('#id_username');
    if (usernameField) {
        usernameField.focus();
    }
    
    // إضافة تأثيرات تفاعلية
    const inputs = document.querySelectorAll('input[type="text"], input[type="password"]');
    inputs.forEach(function(input) {
        input.addEventListener('focus', function() {
            this.parentElement.style.transform = 'scale(1.02)';
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.style.transform = 'scale(1)';
        });
    });
});
</script>
{% endblock %}
