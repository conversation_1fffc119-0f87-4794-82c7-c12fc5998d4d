{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .admin-profile-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }
    
    .admin-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        text-align: center;
    }
    
    .admin-avatar {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 20px;
        font-size: 3rem;
        overflow: hidden;
        position: relative;
    }

    .admin-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 50%;
    }

    .admin-avatar-initials {
        font-weight: bold;
        font-size: 2.5rem;
    }
    
    .admin-name {
        font-size: 1.8rem;
        font-weight: bold;
        margin-bottom: 10px;
    }
    
    .admin-type {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-bottom: 15px;
    }
    
    .status-badge {
        padding: 8px 20px;
        border-radius: 25px;
        font-size: 0.9rem;
        font-weight: bold;
        display: inline-block;
    }
    
    .status-active {
        background-color: rgba(40, 167, 69, 0.2);
        color: #28a745;
        border: 2px solid #28a745;
    }
    
    .status-inactive {
        background-color: rgba(220, 53, 69, 0.2);
        color: #dc3545;
        border: 2px solid #dc3545;
    }
    
    .info-section {
        padding: 25px;
    }
    
    .info-section h5 {
        color: #667eea;
        font-weight: bold;
        margin-bottom: 20px;
        padding-bottom: 10px;
        border-bottom: 2px solid #f0f0f0;
    }
    
    .info-item {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        padding: 10px;
        background: #f8f9fa;
        border-radius: 10px;
    }
    
    .info-icon {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 15px;
        flex-shrink: 0;
    }
    
    .info-content {
        flex-grow: 1;
    }
    
    .info-label {
        font-weight: bold;
        color: #495057;
        font-size: 0.9rem;
        margin-bottom: 2px;
    }
    
    .info-value {
        color: #6c757d;
        font-size: 1rem;
    }
    
    .action-buttons {
        padding: 25px;
        background: #f8f9fa;
        border-top: 1px solid #e9ecef;
    }
    
    .btn-3d {
        border-radius: 10px;
        padding: 12px 25px;
        font-weight: bold;
        transition: all 0.3s ease;
        border: none;
        position: relative;
        overflow: hidden;
        margin: 5px;
    }
    
    .btn-3d:before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }
    
    .btn-3d:hover:before {
        left: 100%;
    }
    
    .btn-primary.btn-3d {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    
    .btn-warning.btn-3d {
        background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%);
        color: white;
    }
    
    .btn-info.btn-3d {
        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
        color: white;
    }
    
    .btn-danger.btn-3d {
        background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
        color: white;
    }
    
    .btn-secondary.btn-3d {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="section-header mb-4 text-center">
                <h2 class="text-primary"><i class="fas fa-user-shield me-2"></i> {{ title }}</h2>
                <p class="text-muted">عرض تفاصيل المسؤول في النظام</p>
            </div>

            <div class="admin-profile-card">
                <!-- Header with Avatar -->
                <div class="admin-header">
                    <div class="admin-avatar">
                        {% if admin_profile.profile_image %}
                            <img src="{{ admin_profile.profile_image.url }}" alt="{{ admin_profile.get_display_name }}">
                        {% else %}
                            {% if admin_profile.get_admin_type_display == 'مسؤول كبير' %}
                                <i class="fas fa-crown"></i>
                            {% else %}
                                <span class="admin-avatar-initials">{{ admin_profile.get_initials }}</span>
                            {% endif %}
                        {% endif %}
                    </div>
                    <div class="admin-name">{{ admin_profile.get_display_name }}</div>
                    <div class="admin-type">{{ admin_profile.get_admin_type_display }}</div>
                    {% if admin_profile.is_active %}
                        <span class="status-badge status-active">نشط</span>
                    {% else %}
                        <span class="status-badge status-inactive">غير نشط</span>
                    {% endif %}
                </div>

                <!-- معلومات الحساب -->
                <div class="info-section">
                    <h5><i class="fas fa-user me-2"></i> معلومات الحساب</h5>
                    
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-user-tag"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">اسم المستخدم</div>
                            <div class="info-value">{{ admin_profile.user.username }}</div>
                        </div>
                    </div>
                    
                    {% if admin_profile.user.email %}
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">البريد الإلكتروني</div>
                            <div class="info-value">{{ admin_profile.user.email }}</div>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if admin_profile.user.first_name or admin_profile.user.last_name %}
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-id-card"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">الاسم في النظام</div>
                            <div class="info-value">{{ admin_profile.user.first_name }} {{ admin_profile.user.last_name }}</div>
                        </div>
                    </div>
                    {% endif %}
                </div>

                <!-- المعلومات الشخصية -->
                <div class="info-section">
                    <h5><i class="fas fa-id-card me-2"></i> المعلومات الشخصية</h5>
                    
                    {% if admin_profile.phone %}
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">رقم الهاتف</div>
                            <div class="info-value">{{ admin_profile.phone }}</div>
                        </div>
                    </div>
                    {% endif %}
                    
                    {% if admin_profile.department %}
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-building"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">القسم</div>
                            <div class="info-value">{{ admin_profile.department }}</div>
                        </div>
                    </div>
                    {% endif %}
                </div>

                <!-- معلومات النظام -->
                <div class="info-section">
                    <h5><i class="fas fa-cog me-2"></i> معلومات النظام</h5>
                    
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">نوع المسؤول</div>
                            <div class="info-value">{{ admin_profile.get_admin_type_display }}</div>
                        </div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-calendar-plus"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">تاريخ الإنشاء</div>
                            <div class="info-value">{{ admin_profile.created_at|date:"Y/m/d H:i" }}</div>
                        </div>
                    </div>
                    
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-calendar-check"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">آخر تحديث</div>
                            <div class="info-value">{{ admin_profile.updated_at|date:"Y/m/d H:i" }}</div>
                        </div>
                    </div>
                    
                    {% if admin_profile.user.last_login %}
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-sign-in-alt"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-label">آخر دخول</div>
                            <div class="info-value">{{ admin_profile.user.last_login|date:"Y/m/d H:i" }}</div>
                        </div>
                    </div>
                    {% endif %}
                </div>

                <!-- ملاحظات -->
                {% if admin_profile.notes %}
                <div class="info-section">
                    <h5><i class="fas fa-sticky-note me-2"></i> ملاحظات</h5>
                    
                    <div class="info-item">
                        <div class="info-icon">
                            <i class="fas fa-comment"></i>
                        </div>
                        <div class="info-content">
                            <div class="info-value">{{ admin_profile.notes|linebreaks }}</div>
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- أزرار الإجراءات -->
                <div class="action-buttons text-center">
                    <a href="{% url 'core:admin_edit' admin_profile.pk %}" class="btn btn-warning btn-3d">
                        <i class="fas fa-edit me-2"></i>تعديل البيانات
                    </a>
                    
                    {% if admin_profile.user.username != 'Saad' and admin_profile.user != user %}
                    <button class="btn btn-info btn-3d" onclick="toggleAdminStatus({{ admin_profile.pk }})">
                        <i class="fas fa-toggle-on me-2"></i>
                        {% if admin_profile.is_active %}إلغاء التفعيل{% else %}تفعيل الحساب{% endif %}
                    </button>
                    
                    <button class="btn btn-danger btn-3d" onclick="deleteAdmin({{ admin_profile.pk }}, '{{ admin_profile.get_display_name }}')">
                        <i class="fas fa-trash me-2"></i>حذف الحساب
                    </button>
                    {% endif %}
                    
                    <a href="{% url 'core:admin_list' %}" class="btn btn-secondary btn-3d">
                        <i class="fas fa-arrow-left me-2"></i>العودة للقائمة
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function toggleAdminStatus(adminId) {
    if (confirm('هل أنت متأكد من تغيير حالة هذا المسؤول؟')) {
        fetch(`/admin-management/${adminId}/toggle-status/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('خطأ: ' + data.message);
            }
        })
        .catch(error => {
            alert('حدث خطأ في الاتصال');
        });
    }
}

function deleteAdmin(adminId, adminName) {
    if (confirm(`هل أنت متأكد من حذف المسؤول "${adminName}"؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        fetch(`/admin-management/${adminId}/delete/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                'Content-Type': 'application/json',
            },
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                window.location.href = '/admin-management/';
            } else {
                alert('خطأ: ' + data.message);
            }
        })
        .catch(error => {
            alert('حدث خطأ في الاتصال');
        });
    }
}
</script>
{% endblock %}
