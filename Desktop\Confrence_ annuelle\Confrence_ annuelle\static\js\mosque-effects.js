/**
 * mosque-effects.js
 * سكريبت خاص بتأثيرات خلفية المسجد النبوي المتحركة
 */

document.addEventListener('DOMContentLoaded', function() {
    // إضافة عناصر الخلفية المتحركة للمسجد النبوي إلى الصفحة
    const createMosqueBackground = () => {
        // التحقق من وجود العناصر بالفعل لتجنب التكرار
        if (document.querySelector('.fullscreen-mosque-bg')) return;
        
        // إنشاء عناصر الخلفية
        const mosqueContainer = document.createElement('div');
        mosqueContainer.className = 'mosque-background-container';
        
        // إضافة الخلفية الرئيسية
        const mosqueBg = document.createElement('div');
        mosqueBg.className = 'fullscreen-mosque-bg';
        
        // إضافة تأثير الموجة
        const mosqueWave = document.createElement('div');
        mosqueWave.className = 'mosque-wave';
        
        // إضافة تأثير الضوء
        const mosqueLight = document.createElement('div');
        mosqueLight.className = 'mosque-light';
        
        // تجميع العناصر
        mosqueContainer.appendChild(mosqueBg);
        mosqueContainer.appendChild(mosqueWave);
        mosqueContainer.appendChild(mosqueLight);
        
        // إضافة العناصر إلى بداية الجسم
        document.body.prepend(mosqueContainer);
        
        // إضافة الكلاس للعناصر الأخرى
        document.querySelector('.navbar').classList.add('navbar-mosque');
        
        // تحسين تأثيرات الحركة عند التمرير
        enhanceScrollEffects();
    };
    
    // تحسين تأثيرات الحركة عند التمرير
    const enhanceScrollEffects = () => {
        window.addEventListener('scroll', () => {
            const scrollPosition = window.scrollY;
            const documentHeight = Math.max(
                document.body.scrollHeight,
                document.body.offsetHeight,
                document.documentElement.clientHeight,
                document.documentElement.scrollHeight,
                document.documentElement.offsetHeight
            );
            const windowHeight = window.innerHeight;
            
            // التأكد من أن التمرير لا يتجاوز ارتفاع الصفحة
            if (scrollPosition <= documentHeight - windowHeight) {
                // تأثير التوازي عند التمرير (Parallax) مع تحديد قيمة قصوى
                const mosqueBg = document.querySelector('.fullscreen-mosque-bg');
                if (mosqueBg) {
                    // تحديد قيمة قصوى للتحريك لمنع التمرير المستمر
                    const maxTranslate = 100; // قيمة قصوى للتحريك بالبكسل
                    const translateY = Math.min(scrollPosition * 0.1, maxTranslate);
                    mosqueBg.style.transform = `translateY(${translateY}px)`;
                }
                
                // تغيير شفافية الخلفية عند التمرير
                const mosqueWave = document.querySelector('.mosque-wave');
                if (mosqueWave) {
                    const opacity = Math.max(0.3, 1 - (scrollPosition * 0.001));
                    mosqueWave.style.opacity = opacity;
                }
            }
        });
    };
    
    // تحسين أداء الصفحة عند التحميل
    const optimizePageLoad = () => {
        // إضافة فئة loaded للجسم بعد اكتمال التحميل
        document.body.classList.add('page-loaded');
        
        // تحريك العناصر بشكل تدريجي
        const contentElements = document.querySelectorAll('.mosque-content > *');
        contentElements.forEach((el, index) => {
            setTimeout(() => {
                el.classList.add('fade-in');
            }, 100 * index);
        });
    };
    
    // تهيئة التأثيرات المتحركة
    const initMosqueEffects = () => {
        createMosqueBackground();
        optimizePageLoad();
        
        // إضافة تأثيرات إضافية للعناصر التفاعلية
        document.querySelectorAll('.btn').forEach(btn => {
            btn.addEventListener('mouseover', function() {
                this.classList.add('pulse-effect');
            });
            
            btn.addEventListener('mouseout', function() {
                this.classList.remove('pulse-effect');
            });
        });
    };
    
    // تشغيل التأثيرات
    initMosqueEffects();
});