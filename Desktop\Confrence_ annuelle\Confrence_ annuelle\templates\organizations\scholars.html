{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "العلماء" %}{% endblock %}

{% block extra_css %}
<style>
    .hero-section {
        background: linear-gradient(135deg, #5e35b1 0%, #3949ab 100%);
        padding: 80px 0;
        color: white;
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .organization-card {
        border-radius: 15px;
        overflow: hidden;
        margin-bottom: 30px;
        transition: all 0.3s ease;
        position: relative;
        background-color: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        box-shadow: 0 8px 32px rgba(94, 53, 177, 0.1);
    }

    .organization-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(94, 53, 177, 0.2);
    }

    .default-logo {
        width: 80px;
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f5f5f5;
        border-radius: 50%;
        margin: 0 auto 15px;
        transition: all 0.3s ease;
    }

    .default-logo:hover {
        transform: scale(1.1);
        box-shadow: 0 0 20px rgba(94, 53, 177, 0.4);
    }

    .scholar-icon {
        transition: all 0.3s ease;
        filter: drop-shadow(0 2px 5px rgba(94, 53, 177, 0.3));
    }

    .default-logo:hover .scholar-icon {
        transform: scale(1.1);
        filter: drop-shadow(0 0 8px rgba(94, 53, 177, 0.6));
    }

    .bg-decoration {
        position: absolute;
        opacity: 0.1;
        z-index: 0;
    }

    .bg-circle-1 {
        width: 300px;
        height: 300px;
        border-radius: 50%;
        background: #5e35b1;
        top: -100px;
        right: -100px;
        animation: float 8s ease-in-out infinite;
    }

    .bg-circle-2 {
        width: 200px;
        height: 200px;
        border-radius: 50%;
        background: #3949ab;
        bottom: -50px;
        left: 10%;
        animation: float 10s ease-in-out infinite reverse;
    }

    .bg-wave {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 50px;
        background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%23ffffff' fill-opacity='1' d='M0,96L48,112C96,128,192,160,288,160C384,160,480,128,576,112C672,96,768,96,864,112C960,128,1056,160,1152,160C1248,160,1344,128,1392,112L1440,96L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z'%3E%3C/path%3E%3C/svg%3E");
        background-size: cover;
    }

    .back-link {
        display: inline-block;
        margin-top: 20px;
        color: white;
        text-decoration: none;
        font-weight: bold;
        transition: all 0.3s ease;
    }

    .back-link:hover {
        transform: translateX(-5px);
        color: #e0e0e0;
    }

    .btn-action {
        margin: 5px;
        border-radius: 20px;
        padding: 8px 15px;
        font-size: 0.9rem;
        transition: all 0.3s ease;
    }

    .btn-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    @keyframes float {
        0% { transform: translateY(0px); }
        50% { transform: translateY(-20px); }
        100% { transform: translateY(0px); }
    }

    .bg-decoration-3 {
        position: absolute;
        width: 150px;
        height: 150px;
        background: #5e35b1;
        opacity: 0.1;
        border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
        top: 20%;
        left: 5%;
        animation: morph 15s linear infinite alternate;
    }

    .bg-decoration-4 {
        position: absolute;
        width: 100px;
        height: 100px;
        background: #3949ab;
        opacity: 0.1;
        border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%;
        bottom: 20%;
        right: 5%;
        animation: morph 12s linear infinite alternate-reverse;
    }

    @keyframes morph {
        0% { border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%; }
        25% { border-radius: 58% 42% 75% 25% / 76% 46% 54% 24%; }
        50% { border-radius: 50% 50% 33% 67% / 55% 27% 73% 45%; }
        75% { border-radius: 33% 67% 58% 42% / 63% 68% 32% 37%; }
        100% { border-radius: 30% 70% 70% 30% / 30% 30% 70% 70%; }
    }

    .icon-pulse {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }

    .wavy-underline {
        position: relative;
        display: inline-block;
    }

    .wavy-underline::after {
        content: '';
        position: absolute;
        left: 0;
        bottom: -5px;
        width: 100%;
        height: 8px;
        background: url("data:image/svg+xml,%3Csvg width='100' height='8' viewBox='0 0 100 8' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M0.5 4C8.5 4 8.5 7.5 16.5 7.5C24.5 7.5 24.5 4 32.5 4C40.5 4 40.5 7.5 48.5 7.5C56.5 7.5 56.5 4 64.5 4C72.5 4 72.5 7.5 80.5 7.5C88.5 7.5 88.5 4 96.5 4' stroke='white' stroke-opacity='0.5' stroke-width='2'/%3E%3C/svg%3E");
        background-size: 100px 8px;
        background-repeat: repeat-x;
        animation: wave 2s linear infinite;
    }

    @keyframes wave {
        0% { background-position-x: 0; }
        100% { background-position-x: 100px; }
    }

    .invitation-actions {
        background-color: rgba(255, 255, 255, 0.9);
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 30px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    }
</style>
{% endblock %}

{% block content %}
<div class="hero-section">
    <div class="container">
        <h1 class="display-4">{% trans "العلماء" %}</h1>
        <p class="lead">{% trans "استعراض جميع العلماء المشاركين في المؤتمر" %}</p>
        <a href="{% url 'organizations:organization_list' %}" class="back-link"><i class="fas fa-arrow-right ml-2"></i> {% trans "العودة إلى قائمة المؤسسات" %}</a>
    </div>
    <div class="bg-decoration bg-circle-1"></div>
    <div class="bg-decoration bg-circle-2"></div>
    <div class="bg-decoration bg-decoration-3"></div>
    <div class="bg-decoration bg-decoration-4"></div>
    <div class="bg-wave"></div>
</div>

<!-- Background Decorations -->
<div class="bg-decoration bg-decoration-1"></div>
<div class="bg-decoration bg-decoration-2"></div>
<div class="bg-decoration bg-decoration-3"></div>
<div class="bg-decoration bg-decoration-4"></div>

<!-- Organizations Section -->
<div class="container mb-5">
    <div class="row">
        <!-- Scholars -->
        <div class="col-12">
            <div class="section-header mb-4 text-center">
                <h2 class="text-primary"><i class="fas fa-user-graduate me-2 icon-pulse"></i> {% trans "العلماء" %}</h2>
                <p class="text-muted">{% trans "العلماء المشاركون في المؤتمر" %}</p>
            </div>

            <!-- Management Actions -->
            <div class="invitation-actions mb-4">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5 class="mb-0"><i class="fas fa-cogs me-2"></i> {% trans "إدارة العلماء" %}</h5>
                    <div>
                        <a href="{% url 'organizations:scholar_create' %}" class="btn btn-success btn-sm me-2">
                            <i class="fas fa-plus me-1"></i> إضافة عالم جديد
                        </a>
                        <a href="{% url 'organizations:scholar_export_excel' %}" class="btn btn-outline-primary btn-sm me-2">
                            <i class="fas fa-download me-1"></i> تصدير Excel
                        </a>
                        <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#inviteModal">
                            <i class="fas fa-paper-plane me-1"></i> {% trans "إرسال دعوة فردية" %}
                        </button>
                        <button class="btn btn-info btn-sm ms-2" id="bulk-invite-btn" disabled>
                            <i class="fas fa-envelope-open me-1"></i> {% trans "إرسال دعوات متعددة" %}
                            (<span id="selected-count">0</span>)
                        </button>
                    </div>
                </div>
                <div class="form-check mb-2">
                    <input class="form-check-input" type="checkbox" id="select-all">
                    <label class="form-check-label" for="select-all">
                        {% trans "تحديد الكل" %}
                    </label>
                </div>
                <div class="selected-info small text-muted">
                    <span id="selected-text">{% trans "لم يتم تحديد أي مؤسسة" %}</span>
                </div>
            </div>
            
            {% if organizations %}
            <div class="row">
                {% for organization in organizations %}
                <div class="col-md-4 mb-4" id="organization-{{ organization.id }}">
                    <div class="card organization-card h-100 shadow-hover multi-layer-shadow">
                        <div class="card-body text-center">
                            <div class="organization-logo mb-3">
                                {% if organization.logo %}
                                <img src="{{ organization.logo.url }}" alt="{{ organization.name }}" class="img-fluid icon-float" style="max-height: 80px;">
                                {% else %}
                                <div class="default-logo multi-layer-shadow">
                                    {% with forloop.counter|divisibleby:5 as icon_type %}
                                    {% if icon_type == 1 %}
                                    <!-- عالم -->
                                    <svg class="scholar-icon" width="80" height="80" viewBox="0 0 24 24">
                                        <path fill="#5e35b1" d="M12,3L1,9L12,15L21,10.09V17H23V9M5,13.18V17.18L12,21L19,17.18V13.18L12,17L5,13.18Z"/>
                                    </svg>
                                    {% elif icon_type == 2 %}
                                    <!-- باحث -->
                                    <svg class="scholar-icon" width="80" height="80" viewBox="0 0 24 24">
                                        <path fill="#5e35b1" d="M12,5A3.5,3.5 0 0,0 8.5,8.5A3.5,3.5 0 0,0 12,12A3.5,3.5 0 0,0 15.5,8.5A3.5,3.5 0 0,0 12,5M12,7A1.5,1.5 0 0,1 13.5,8.5A1.5,1.5 0 0,1 12,10A1.5,1.5 0 0,1 10.5,8.5A1.5,1.5 0 0,1 12,7M5.5,8A2.5,2.5 0 0,0 3,10.5C3,11.44 3.53,12.25 4.29,12.68C4.65,12.88 5.06,13 5.5,13C5.94,13 6.35,12.88 6.71,12.68C7.08,12.47 7.39,12.17 7.62,11.81C6.89,10.86 6.5,9.7 6.5,8.5C6.5,8.41 6.5,8.31 6.5,8.22C6.2,8.08 5.86,8 5.5,8M18.5,8C18.14,8 17.8,8.08 17.5,8.22C17.5,8.31 17.5,8.41 17.5,8.5C17.5,9.7 17.11,10.86 16.38,11.81C16.5,12 16.63,12.15 16.78,12.3C16.94,12.45 17.1,12.58 17.29,12.68C17.65,12.88 18.06,13 18.5,13C18.94,13 19.35,12.88 19.71,12.68C20.47,12.25 21,11.44 21,10.5A2.5,2.5 0 0,0 18.5,8M12,14C9.66,14 5,15.17 5,17.5V19H19V17.5C19,15.17 14.34,14 12,14M4.71,14.55C2.78,14.78 0,15.76 0,17.5V19H3V17.07C3,16.06 3.69,15.22 4.71,14.55M19.29,14.55C20.31,15.22 21,16.06 21,17.07V19H24V17.5C24,15.76 21.22,14.78 19.29,14.55M12,16C13.53,16 15.24,16.5 16.23,17H7.77C8.76,16.5 10.47,16 12,16Z"/>
                                    </svg>
                                    {% elif icon_type == 3 %}
                                    <!-- أستاذ -->
                                    <svg class="scholar-icon" width="80" height="80" viewBox="0 0 24 24">
                                        <path fill="#5e35b1" d="M20,17A2,2 0 0,0 22,15V4A2,2 0 0,0 20,2H9.46C9.81,2.61 10,3.3 10,4H20V15H11V17M15,7V9H9V22H7V16H5V22H3V14H1.5V9A2,2 0 0,1 3.5,7H15M8,4A2,2 0 0,1 6,6A2,2 0 0,1 4,4A2,2 0 0,1 6,2A2,2 0 0,1 8,4Z"/>
                                    </svg>
                                    {% elif icon_type == 4 %}
                                    <!-- مفكر -->
                                    <svg class="scholar-icon" width="80" height="80" viewBox="0 0 24 24">
                                        <path fill="#5e35b1" d="M13,3V9H21V3M13,21H21V11H13M3,21H11V15H3M3,13H11V3H3V13Z"/>
                                    </svg>
                                    {% else %}
                                    <!-- عالم عام -->
                                    <svg class="scholar-icon" width="80" height="80" viewBox="0 0 24 24">
                                        <path fill="#5e35b1" d="M12,8A4,4 0 0,1 16,12A4,4 0 0,1 12,16A4,4 0 0,1 8,12A4,4 0 0,1 12,8M12,10A2,2 0 0,0 10,12A2,2 0 0,0 12,14A2,2 0 0,0 14,12A2,2 0 0,0 12,10M10,22C9.75,22 9.54,21.82 9.5,21.58L9.13,18.93C8.5,18.68 7.96,18.34 7.44,17.94L4.95,18.95C4.73,19.03 4.46,18.95 4.34,18.73L2.34,15.27C2.21,15.05 2.27,14.78 2.46,14.63L4.57,12.97L4.5,12L4.57,11L2.46,9.37C2.27,9.22 2.21,8.95 2.34,8.73L4.34,5.27C4.46,5.05 4.73,4.96 4.95,5.05L7.44,6.05C7.96,5.66 8.5,5.32 9.13,5.07L9.5,2.42C9.54,2.18 9.75,2 10,2H14C14.25,2 14.46,2.18 14.5,2.42L14.87,5.07C15.5,5.32 16.04,5.66 16.56,6.05L19.05,5.05C19.27,4.96 19.54,5.05 19.66,5.27L21.66,8.73C21.79,8.95 21.73,9.22 21.54,9.37L19.43,11L19.5,12L19.43,13L21.54,14.63C21.73,14.78 21.79,15.05 21.66,15.27L19.66,18.73C19.54,18.95 19.27,19.04 19.05,18.95L16.56,17.95C16.04,18.34 15.5,18.68 14.87,18.93L14.5,21.58C14.46,21.82 14.25,22 14,22H10M11.25,4L10.88,6.61C9.68,6.86 8.62,7.5 7.85,8.39L5.44,7.35L4.69,8.65L6.8,10.2C6.4,11.37 6.4,12.64 6.8,13.8L4.68,15.36L5.43,16.66L7.86,15.62C8.63,16.5 9.68,17.14 10.87,17.38L11.24,20H12.76L13.13,17.39C14.32,17.14 15.37,16.5 16.14,15.62L18.57,16.66L19.32,15.36L17.2,13.81C17.6,12.64 17.6,11.37 17.2,10.2L19.31,8.65L18.56,7.35L16.15,8.39C15.38,7.5 14.32,6.86 13.12,6.62L12.75,4H11.25Z"/>
                                    </svg>
                                    {% endif %}
                                    {% endwith %}
                                </div>
                                {% endif %}
                            </div>
                            <h5 class="card-title wavy-underline">{{ organization.name }}</h5>
                            <p class="card-text small text-muted list-item-hover">{{ organization.description|truncatechars:100 }}</p>
                            <div class="mt-2">
                                <p class="card-text small mb-1"><i class="fas fa-map-marker-alt text-secondary me-1"></i> {{ organization.address }}</p>
                                <p class="card-text small mb-1"><i class="fas fa-envelope text-secondary me-1"></i> {{ organization.email }}</p>
                                <p class="card-text small"><i class="fas fa-phone text-secondary me-1"></i> {{ organization.phone }}</p>
                            </div>
                            
                            <div class="mt-3 d-flex flex-wrap justify-content-center gap-2">
                                <a href="{% url 'organizations:organization_detail' organization.pk %}" class="btn btn-primary btn-sm btn-action btn-3d btn-hover-expand" data-bs-toggle="tooltip" data-bs-placement="top" title="{% trans "عرض تفاصيل العالم" %}">
                                    <i class="fas fa-eye me-1 icon-float"></i> {% trans "عرض التفاصيل" %}
                                </a>
                                
                                {% if organization.website %}
                                <a href="{{ organization.website }}" target="_blank" class="btn btn-info btn-sm btn-action btn-3d btn-hover-expand" data-bs-toggle="tooltip" data-bs-placement="top" title="{% trans "زيارة الموقع الرسمي" %}">
                                    <i class="fas fa-globe me-1 icon-float"></i> {% trans "زيارة الموقع" %}
                                </a>
                                {% endif %}
                                
                                <a href="{% url 'organizations:contact_organization' organization.pk %}" class="btn btn-success btn-sm btn-action btn-3d btn-hover-expand" data-bs-toggle="tooltip" data-bs-placement="top" title="{% trans "التواصل مع العالم" %}">
                                    <i class="fas fa-envelope me-1 icon-float"></i> {% trans "اتصل بنا" %}
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="alert alert-info glass-effect multi-layer-shadow">
                <i class="fas fa-info-circle me-2 pulse-element"></i> {% trans "لا يوجد علماء مسجلين حتى الآن." %}
            </div>
            {% endif %}
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
<script src="{% static 'js/organizations.js' %}"></script>
<script src="{% static 'js/organization_effects.js' %}"></script>
<script>
    // تحديث عداد المؤسسات المحددة
    function updateSelectedCount() {
        const selectedCount = document.querySelectorAll('.organization-checkbox:checked').length;
        document.getElementById('selected-count').textContent = selectedCount;
        
        const selectedText = document.getElementById('selected-text');
        if (selectedCount === 0) {
            selectedText.textContent = "{% trans "لم يتم تحديد أي مؤسسة" %}";
        } else if (selectedCount === 1) {
            selectedText.textContent = "{% trans "تم تحديد مؤسسة واحدة" %}";
        } else {
            selectedText.textContent = `{% trans "تم تحديد" %} ${selectedCount} {% trans "مؤسسات" %}`;
        }
    }

    // تحديد الكل أو إلغاء تحديد الكل
    document.getElementById('select-all').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('.organization-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateSelectedCount();
    });

    // تحديث العداد عند تغيير أي اختيار
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('organization-checkbox')) {
            updateSelectedCount();
            // تمكين أو تعطيل زر الدعوات الجماعية بناءً على وجود عناصر محددة
            const selectedCount = document.querySelectorAll('.organization-checkbox:checked').length;
            document.getElementById('bulk-invite-btn').disabled = selectedCount === 0;
        }
    });

    // زر إرسال الدعوات المتعددة
    document.getElementById('bulk-invite-btn').addEventListener('click', function() {
        const selectedIds = [];
        document.querySelectorAll('.organization-checkbox:checked').forEach(checkbox => {
            selectedIds.push(checkbox.value);
        });
        
        if (selectedIds.length === 0) {
            alert("{% trans "الرجاء تحديد مؤسسة واحدة على الأقل" %}");
            return;
        }
        
        // إرسال الطلب لإرسال الدعوات المتعددة
        window.location.href = `{% url 'organizations:send_bulk_invitation' %}?selected=${selectedIds.join(',')}`;
    });
</script>
{% endblock %}