{% extends 'base.html' %}
{% load static %}
{% load i18n %}
{% block title %}{% if organization.name_fr %}{{ organization.name_fr }}{% else %}{{ organization.name }}{% endif %}{% endblock %}

{% block extra_css %}
<style>
    /* Hero Mini Styles */
    .hero-mini {
        background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
        color: white;
        padding: 4rem 0;
        text-align: center;
        position: relative;
        overflow: hidden;
    }
    
    .hero-mini::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url('{% static "img/pattern.png" %}');
        opacity: 0.1;
    }
    
    .organization-logo-large {
        width: 150px;
        height: 150px;
        object-fit: contain;
        margin: 0 auto 1.5rem;
        border-radius: 50%;
        padding: 0.5rem;
        background-color: white;
        border: 5px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        animation: float 6s ease-in-out infinite;
    }
    
    .organization-info-card {
        border: none;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
        border-radius: 0.5rem;
        overflow: hidden;
        transition: all 0.3s ease;
        background-color: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .organization-info-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.15);
        border: 1px solid rgba(26, 35, 126, 0.3);
    }
    
    .info-row {
        padding: 0.75rem 0;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }
    
    .info-row:last-child {
        border-bottom: none;
    }
    
    .info-label {
        color: #1a237e;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .info-row:hover .info-label {
        transform: translateX(5px);
        text-shadow: 0 0 5px rgba(26, 35, 126, 0.3);
    }
    
    .action-buttons {
        position: relative;
        z-index: 10;
        margin-top: -2rem;
    }
    
    .action-buttons .btn {
        border-radius: 50px;
        padding: 0.5rem 1.5rem;
        margin: 0 0.25rem;
        box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    
    .action-buttons .btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.25);
    }
    
    .action-buttons .btn::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 5px;
        height: 5px;
        background: rgba(255, 255, 255, 0.5);
        opacity: 0;
        border-radius: 100%;
        transform: scale(1, 1) translate(-50%);
        transform-origin: 50% 50%;
    }
    
    .action-buttons .btn:hover::after {
        animation: ripple 1s ease-out;
    }
    
    @keyframes ripple {
        0% {
            transform: scale(0, 0);
            opacity: 0.5;
        }
        100% {
            transform: scale(20, 20);
            opacity: 0;
        }
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="hero-mini text-white py-5 mb-5 animated-bg">
    <div class="container">
        <div class="row">
            <div class="col-md-8 mx-auto text-center fade-in-element">
                {% if organization.logo %}
                <img src="{{ organization.logo.url }}" alt="{{ organization.name }}" class="organization-logo-large multi-layer-shadow">
                {% else %}
                <div class="organization-logo-large d-flex align-items-center justify-content-center bg-light mx-auto multi-layer-shadow">
                    <i class="fas fa-building fa-4x text-primary icon-pulse"></i>
                </div>
                {% endif %}
                
                <h1 class="display-4 fw-bold mb-3 neon-text">{% if organization.name_fr %}{{ organization.name_fr }}{% else %}{{ organization.name }}{% endif %}</h1>
                
                <div class="mb-3 scroll-animate-right">
                    {% if organization.participation_status == 'invited' %}
                    <span class="badge bg-warning rounded-pill px-3 py-2 fs-6 glow-border">
                        <i class="fas fa-envelope me-1 icon-float"></i> {% trans "مدعو" %}
                    </span>
                    {% elif organization.participation_status == 'confirmed' %}
                    <span class="badge bg-success rounded-pill px-3 py-2 fs-6 glow-border">
                        <i class="fas fa-check-circle me-1 icon-float"></i> {% trans "مؤكد" %}
                    </span>
                    {% elif organization.participation_status == 'declined' %}
                    <span class="badge bg-danger rounded-pill px-3 py-2 fs-6 glow-border">
                        <i class="fas fa-times-circle me-1 icon-float"></i> {% trans "مرفوض" %}
                    </span>
                    {% elif organization.participation_status == 'attended' %}
                    <span class="badge bg-primary rounded-pill px-3 py-2 fs-6 glow-border">
                        <i class="fas fa-user-check me-1 icon-float"></i> {% trans "حاضر" %}
                    </span>
                    {% endif %}
                </div>
                
                {% if organization.website %}
                <a href="{{ organization.website }}" target="_blank" class="btn btn-light btn-lg btn-3d btn-hover-expand">
                    <i class="fas fa-globe me-2 icon-float"></i> {% trans "زيارة الموقع الإلكتروني" %}
                </a>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row mb-4">
        <div class="col-12 text-center action-buttons fade-in-element">
            {% if user.is_authenticated %}
            <a href="{% url 'organizations:organization_update' organization.pk %}" class="btn btn-primary btn-glow">
                <i class="fas fa-edit me-2 icon-float"></i> {% trans "تعديل المعلومات" %}
            </a>
            <a href="{% url 'organizations:send_invitation' %}?organization={{ organization.pk }}" class="btn btn-success btn-glow">
                <i class="fas fa-envelope me-2 icon-float"></i> {% trans "إرسال دعوة" %}
            </a>
            <div class="btn-group">
                <button type="button" class="btn btn-info dropdown-toggle btn-glow" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-check-circle me-2 icon-float"></i> {% trans "تغيير الحالة" %}
                </button>
                <ul class="dropdown-menu glass-effect">
                    <li>
                        <form action="{% url 'organizations:change_organization_status' organization.pk %}" method="post" class="status-form">
                            {% csrf_token %}
                            <input type="hidden" name="status" value="invited">
                            <button type="submit" class="dropdown-item list-item-hover">{% trans "مدعو" %}</button>
                        </form>
                    </li>
                    <li>
                        <form action="{% url 'organizations:change_organization_status' organization.pk %}" method="post" class="status-form">
                            {% csrf_token %}
                            <input type="hidden" name="status" value="confirmed">
                            <button type="submit" class="dropdown-item list-item-hover">{% trans "مؤكد" %}</button>
                        </form>
                    </li>
                    <li>
                        <form action="{% url 'organizations:change_organization_status' organization.pk %}" method="post" class="status-form">
                            {% csrf_token %}
                            <input type="hidden" name="status" value="declined">
                            <button type="submit" class="dropdown-item list-item-hover">{% trans "مرفوض" %}</button>
                        </form>
                    </li>
                    <li>
                        <form action="{% url 'organizations:change_organization_status' organization.pk %}" method="post" class="status-form">
                            {% csrf_token %}
                            <input type="hidden" name="status" value="attended">
                            <button type="submit" class="dropdown-item list-item-hover">{% trans "حاضر" %}</button>
                        </form>
                    </li>
                </ul>
            </div>
            <a href="{% url 'organizations:organization_delete' organization.pk %}" class="btn btn-danger btn-delete btn-glow">
                <i class="fas fa-trash me-2 icon-float"></i> {% trans "حذف المؤسسة" %}
            </a>
            {% endif %}
            <a href="{% url 'organizations:contact_organization' organization.pk %}" class="btn btn-warning btn-glow">
                <i class="fas fa-paper-plane me-2 icon-float"></i> {% trans "اتصل بالمؤسسة" %}
            </a>
        </div>
    </div>

<div class="row">
    <div class="col-md-8 fade-in-element">
        <div class="card mb-4 organization-info-card glass-effect">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2 icon-pulse"></i>{% trans "معلومات المؤسسة" %}</h5>
            </div>
            <div class="card-body p-4">
                <div class="info-row">
                    <div class="row align-items-center">
                        <div class="col-md-4">
                            <div class="info-label"><i class="fas fa-envelope text-primary me-2"></i>{% trans "البريد الإلكتروني:" %}</div>
                        </div>
                        <div class="col-md-8">
                            <a href="mailto:{{ organization.email }}" class="text-decoration-none">{{ organization.email }}</a>
                        </div>
                    </div>
                </div>
                
                {% if organization.phone %}
                <div class="info-row">
                    <div class="row align-items-center">
                        <div class="col-md-4">
                            <div class="info-label"><i class="fas fa-phone-alt text-primary me-2"></i>{% trans "الهاتف:" %}</div>
                        </div>
                        <div class="col-md-8">
                            <a href="tel:{{ organization.phone }}" class="text-decoration-none">{{ organization.phone }}</a>
                        </div>
                    </div>
                </div>
                {% endif %}
                
                {% if organization.contact_person %}
                <div class="info-row">
                    <div class="row align-items-center">
                        <div class="col-md-4">
                            <div class="info-label"><i class="fas fa-user text-primary me-2"></i>{% trans "الشخص المسؤول:" %}</div>
                        </div>
                        <div class="col-md-8">
                            {{ organization.contact_person }}
                        </div>
                    </div>
                </div>
                {% endif %}
                
                {% if organization.website %}
                <div class="info-row">
                    <div class="row align-items-center">
                        <div class="col-md-4">
                            <div class="info-label"><i class="fas fa-globe text-primary me-2"></i>{% trans "الموقع الإلكتروني:" %}</div>
                        </div>
                        <div class="col-md-8">
                            <a href="{{ organization.website }}" target="_blank" class="text-decoration-none">{{ organization.website }}</a>
                        </div>
                    </div>
                </div>
                {% endif %}
                
                {% if organization.address %}
                <div class="info-row">
                    <div class="row align-items-center">
                        <div class="col-md-4">
                            <div class="info-label"><i class="fas fa-map-marker-alt text-primary me-2"></i>{% trans "العنوان:" %}</div>
                        </div>
                        <div class="col-md-8">
                            {{ organization.address }}
                        </div>
                    </div>
                </div>
                {% endif %}
                
                {% if organization.description %}
                <div class="info-row">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="info-label"><i class="fas fa-align-left text-primary me-2"></i>{% trans "الوصف:" %}</div>
                        </div>
                        <div class="col-md-8">
                            <p class="mb-0">{{ organization.description }}</p>
                        </div>
                    </div>
                </div>
                {% endif %}
                
                <div class="info-row">
                    <div class="row align-items-center">
                        <div class="col-md-4">
                            <div class="info-label"><i class="fas fa-check-circle text-primary me-2"></i>{% trans "حالة المشاركة:" %}</div>
                        </div>
                        <div class="col-md-8">
                            {% if organization.participation_status == 'invited' %}
                            <span class="badge rounded-pill bg-warning text-dark"><i class="fas fa-envelope me-1"></i>{% trans "مدعو" %}</span>
                            {% elif organization.participation_status == 'confirmed' %}
                            <span class="badge rounded-pill bg-success"><i class="fas fa-check me-1"></i>{% trans "مؤكد" %}</span>
                            {% elif organization.participation_status == 'declined' %}
                            <span class="badge rounded-pill bg-danger"><i class="fas fa-times me-1"></i>{% trans "مرفوض" %}</span>
                            {% elif organization.participation_status == 'attended' %}
                            <span class="badge rounded-pill bg-primary"><i class="fas fa-user-check me-1"></i>{% trans "حاضر" %}</span>
                            {% endif %}
                        </div>
                    </div>
                </div>
                
                <div class="info-row">
                    <div class="row align-items-center">
                        <div class="col-md-4">
                            <div class="info-label"><i class="fas fa-calendar-alt text-primary me-2"></i>{% trans "تاريخ التسجيل:" %}</div>
                        </div>
                        <div class="col-md-8">
                            {{ organization.created_at|date:"Y-m-d" }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4 fade-in-element">
        {% if organization.logo %}
        <div class="card mb-4 organization-info-card glass-effect">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-image me-2 icon-pulse"></i>{% trans "شعار المؤسسة" %}</h5>
            </div>
            <div class="card-body text-center p-4">
                <img src="{{ organization.logo.url }}" alt="{{ organization.name }}" class="img-fluid rounded shadow-sm multi-layer-shadow icon-float" style="max-height: 200px;">
            </div>
        </div>
        {% endif %}
        
        {% if user.is_authenticated %}
        <div class="card organization-info-card glass-effect scroll-animate-right">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-envelope-open-text me-2 icon-pulse"></i>{% trans "الدعوات المرسلة" %}</h5>
            </div>
            <div class="card-body p-0">
                {% if invitations %}
                <ul class="list-group list-group-flush">
                    {% for invitation in invitations %}
                    <li class="list-group-item p-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <div class="fw-bold mb-1">{{ invitation.subject }}</div>
                                <div class="text-muted small">
                                    <i class="far fa-calendar-alt me-1"></i> {{ invitation.sent_at|date:"Y-m-d" }}
                                    <i class="far fa-clock ms-2 me-1"></i> {{ invitation.sent_at|date:"H:i" }}
                                </div>
                            </div>
                            <span class="badge rounded-pill {% if invitation.is_sent %}bg-success{% else %}bg-warning text-dark{% endif %} px-3 py-2">
                                {% if invitation.is_sent %}
                                <i class="fas fa-check me-1"></i> {% trans "مرسلة" %}
                                {% else %}
                                <i class="fas fa-clock me-1"></i> {% trans "قيد الانتظار" %}
                                {% endif %}
                            </span>
                        </div>
                    </li>
                    {% endfor %}
                </ul>
                {% else %}
                <div class="text-center p-4">
                    <div class="mb-3">
                        <i class="fas fa-envelope-open text-muted fa-3x"></i>
                    </div>
                    <p class="text-muted mb-0">{% trans "لا توجد دعوات مرسلة حالياً." %}</p>
                    <a href="{% url 'organizations:send_invitation' %}?organization={{ organization.pk }}" class="btn btn-sm btn-primary mt-3">
                        <i class="fas fa-paper-plane me-1"></i> {% trans "إرسال دعوة جديدة" %}
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}