{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/modern-theme.css' %}">
<style>
    .position-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
        text-align: center;
    }
    
    .position-badge {
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: bold;
    }
    
    .badge-mayor { background-color: #28a745; color: white; }
    .badge-deputy { background-color: #007bff; color: white; }
    .badge-head { background-color: #6f42c1; color: white; }
    
    .status-badge {
        padding: 3px 8px;
        border-radius: 15px;
        font-size: 0.8rem;
    }
    
    .status-active { background-color: #d4edda; color: #155724; }
    .status-inactive { background-color: #f8d7da; color: #721c24; }
    .status-retired { background-color: #fff3cd; color: #856404; }
    
    .official-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
        margin-bottom: 20px;
    }
    
    .official-card:hover {
        transform: translateY(-5px);
    }
    
    .official-avatar {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        font-weight: bold;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid" dir="rtl">
    <!-- Header -->
    <div class="position-header">
        <h1 class="mb-3">{{ title }}</h1>
        <p class="mb-3">إدارة وعرض قائمة {{ position_type }}</p>

        <!-- Action Buttons -->
        <div class="mt-4">
            <a href="{% url 'organizations:add_elected_official' %}" class="btn btn-success btn-lg me-3">
                <i class="fas fa-plus"></i> إضافة {{ position_type }} جديد
            </a>
            <a href="{% url 'organizations:export_elected_officials' %}" class="btn btn-outline-light btn-lg">
                <i class="fas fa-download"></i> تصدير Excel
            </a>
        </div>
    </div>

    <!-- Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'organizations:elected_officials_list' %}">المنتخبون</a></li>
                    <li class="breadcrumb-item active">{{ title }}</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <a href="{% url 'organizations:elected_officials_list' %}" class="btn btn-secondary">
                        <i class="fas fa-arrow-right"></i> العودة للقائمة الرئيسية
                    </a>
                </div>
                <div>
                    <a href="{% url 'organizations:add_elected_official' %}" class="btn btn-primary me-2">
                        <i class="fas fa-plus"></i> إضافة {{ position_type }} جديد
                    </a>
                    <a href="{% url 'organizations:export_elected_officials' %}" class="btn btn-success me-2">
                        <i class="fas fa-file-excel"></i> تصدير Excel
                    </a>

                    <!-- WhatsApp Invitation Buttons -->
                    <div class="btn-group me-2" role="group">
                        <button type="button" class="btn btn-outline-success dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fab fa-whatsapp"></i> دعوات WhatsApp
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#whatsappInvitationModal">
                                <i class="fab fa-whatsapp text-success"></i> دعوة فردية
                            </a></li>
                            <li><a class="dropdown-item" href="#" id="send-bulk-whatsapp-invitations">
                                <i class="fab fa-whatsapp text-success"></i> دعوات جماعية
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" id="send-selected-whatsapp-invitations">
                                <i class="fas fa-check-circle text-success"></i> دعوة المحددين (<span id="selected-whatsapp-count">0</span>)
                            </a></li>
                        </ul>
                    </div>

                    <!-- Email Invitation Buttons -->
                    <div class="btn-group me-2" role="group">
                        <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-envelope"></i> دعوات البريد الإلكتروني
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#emailInvitationModal">
                                <i class="fas fa-envelope text-primary"></i> دعوة فردية
                            </a></li>
                            <li><a class="dropdown-item" href="#" id="send-bulk-email-invitations">
                                <i class="fas fa-envelope text-primary"></i> دعوات جماعية
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" id="send-selected-email-invitations">
                                <i class="fas fa-envelope text-primary"></i> دعوة المحددين (<span id="selected-email-count">0</span>)
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-8">
                    <label class="form-label">البحث</label>
                    <input type="text" name="search" class="form-control" value="{{ search_query }}" placeholder="البحث بالاسم أو المنطقة">
                </div>
                <div class="col-md-4">
                    <label class="form-label">&nbsp;</label>
                    <div>
                        <button type="submit" class="btn btn-primary">بحث</button>
                        <a href="{{ request.path }}" class="btn btn-secondary">إعادة تعيين</a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Officials List -->
    {% if officials %}
        <div class="row">
            {% for official in officials %}
            <div class="col-md-6 col-lg-4">
                <div class="card official-card">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="flex-shrink-0 me-2">
                                <input type="checkbox" class="form-check-input official-checkbox" name="selected_officials" value="{{ official.id }}" id="official-{{ official.id }}">
                            </div>
                            <div class="official-avatar me-3">
                                {{ official.name|first }}
                            </div>
                            <div>
                                <h5 class="card-title mb-1">{{ official.name }}</h5>
                                <span class="position-badge badge-{{ official.position }}">
                                    {{ official.get_position_display }}
                                </span>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <div class="row">
                                <div class="col-6">
                                    <small class="text-muted">المنطقة:</small>
                                    <div><strong>{{ official.region }}</strong></div>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">الحالة:</small>
                                    <div>
                                        <span class="status-badge status-{{ official.status }}">
                                            {{ official.get_status_display }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        {% if official.party %}
                        <div class="mb-3">
                            <small class="text-muted">الحزب:</small>
                            <div><strong>{{ official.party }}</strong></div>
                        </div>
                        {% endif %}
                        
                        {% if official.phone %}
                        <div class="mb-3">
                            <small class="text-muted">الهاتف:</small>
                            <div><strong>{{ official.phone }}</strong></div>
                        </div>
                        {% endif %}
                        
                        <div class="mb-3">
                            <small class="text-muted">تاريخ البداية:</small>
                            <div><strong>{{ official.start_date|date:"Y-m-d" }}</strong></div>
                        </div>
                        
                        <!-- Invitation Buttons -->
                        <div class="mb-3">
                            <div class="d-flex gap-2 flex-wrap">
                                {% if official.phone %}
                                <button class="btn btn-sm btn-success send-whatsapp-btn"
                                        data-official-id="{{ official.id }}"
                                        data-official-name="{{ official.name }}"
                                        data-official-phone="{{ official.phone }}"
                                        data-bs-toggle="modal"
                                        data-bs-target="#whatsappInvitationModal">
                                    <i class="fab fa-whatsapp"></i> دعوة WhatsApp
                                </button>
                                {% endif %}

                                {% if official.email %}
                                <button class="btn btn-sm btn-primary send-email-btn"
                                        data-official-id="{{ official.id }}"
                                        data-official-name="{{ official.name }}"
                                        data-official-email="{{ official.email }}"
                                        data-bs-toggle="modal"
                                        data-bs-target="#emailInvitationModal">
                                    <i class="fas fa-envelope"></i> دعوة بريد إلكتروني
                                </button>
                                {% endif %}
                            </div>
                        </div>

                        <div class="d-flex justify-content-between">
                            <a href="{% url 'organizations:edit_elected_official' official.pk %}" class="btn btn-sm btn-outline-primary">
                                <i class="fas fa-edit"></i> تعديل
                            </a>
                            <a href="{% url 'organizations:delete_elected_official' official.pk %}" class="btn btn-sm btn-outline-danger">
                                <i class="fas fa-trash"></i> حذف
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="card">
            <div class="card-body text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا يوجد {{ position_type }} مسجلين</h5>
                <p class="text-muted">ابدأ بإضافة {{ position_type }} جدد</p>
                <a href="{% url 'organizations:add_elected_official' %}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> إضافة {{ position_type }} جديد
                </a>
            </div>
        </div>
    {% endif %}
</div>

<!-- WhatsApp Invitation Modal -->
<div class="modal fade" id="whatsappInvitationModal" tabindex="-1" aria-labelledby="whatsappInvitationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="whatsappInvitationModalLabel">
                    <i class="fab fa-whatsapp me-2"></i>إرسال دعوة WhatsApp
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'organizations:send_official_whatsapp_invitation' %}">
                {% csrf_token %}
                <input type="hidden" id="whatsapp-official-id" name="official_id">
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fab fa-whatsapp me-2"></i>
                        سيتم إرسال دعوة WhatsApp إلى: <strong id="whatsapp-official-name"></strong>
                        <br>رقم الهاتف: <strong id="whatsapp-official-phone"></strong>
                    </div>

                    <div class="mb-3">
                        <label for="whatsapp-subject" class="form-label">موضوع الرسالة</label>
                        <input type="text" class="form-control" id="whatsapp-subject" name="subject"
                               value="دعوة للمشاركة في مؤتمر السيرة النبوية السنوي" required>
                    </div>

                    <div class="mb-3">
                        <label for="whatsapp-message" class="form-label">نص الرسالة</label>
                        <textarea class="form-control" id="whatsapp-message" name="message" rows="8" required>بسم الله الرحمن الرحيم

معالي النائب المحترم

السلام عليكم ورحمة الله وبركاته

يسعدنا أن ندعوكم للمشاركة في مؤتمر السيرة النبوية السنوي الذي يهدف إلى نشر العلم والمعرفة حول سيرة النبي محمد صلى الله عليه وسلم.

نتطلع إلى مشاركتكم القيمة وإثراء المؤتمر بخبرتكم.

وفقكم الله وبارك فيكم

مع أطيب التحيات
لجنة تنظيم المؤتمر</textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fab fa-whatsapp me-2"></i>إرسال الدعوة
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Email Invitation Modal -->
<div class="modal fade" id="emailInvitationModal" tabindex="-1" aria-labelledby="emailInvitationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="emailInvitationModalLabel">
                    <i class="fas fa-envelope me-2"></i>إرسال دعوة بريد إلكتروني
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'organizations:send_official_email_invitation' %}">
                {% csrf_token %}
                <input type="hidden" id="email-official-id" name="official_id">
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-envelope me-2"></i>
                        سيتم إرسال دعوة بريد إلكتروني إلى: <strong id="email-official-name"></strong>
                        <br>البريد الإلكتروني: <strong id="email-official-email"></strong>
                    </div>

                    <div class="mb-3">
                        <label for="email-subject" class="form-label">موضوع الرسالة</label>
                        <input type="text" class="form-control" id="email-subject" name="subject"
                               value="دعوة للمشاركة في مؤتمر السيرة النبوية السنوي" required>
                    </div>

                    <div class="mb-3">
                        <label for="email-message" class="form-label">نص الرسالة</label>
                        <textarea class="form-control" id="email-message" name="message" rows="8" required>بسم الله الرحمن الرحيم

معالي النائب المحترم

السلام عليكم ورحمة الله وبركاته

يسعدنا أن ندعوكم للمشاركة في مؤتمر السيرة النبوية السنوي الذي يهدف إلى نشر العلم والمعرفة حول سيرة النبي محمد صلى الله عليه وسلم.

نتطلع إلى مشاركتكم القيمة وإثراء المؤتمر بخبرتكم.

وفقكم الله وبارك فيكم

مع أطيب التحيات
لجنة تنظيم المؤتمر</textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-envelope me-2"></i>إرسال الدعوة
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Bulk WhatsApp Invitation Modal -->
<div class="modal fade" id="bulkWhatsappInvitationModal" tabindex="-1" aria-labelledby="bulkWhatsappInvitationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bulkWhatsappInvitationModalLabel">
                    <i class="fab fa-whatsapp me-2"></i>إرسال دعوات جماعية WhatsApp
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'organizations:send_bulk_official_whatsapp_invitation' %}">
                {% csrf_token %}
                <input type="hidden" id="bulk-whatsapp-official-ids" name="official_ids">
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fab fa-whatsapp me-2"></i>
                        سيتم إرسال دعوات WhatsApp إلى <strong id="bulk-whatsapp-count">جميع</strong> النواب المحددين
                    </div>

                    <div class="mb-3">
                        <label for="bulk-whatsapp-subject" class="form-label">موضوع الرسالة</label>
                        <input type="text" class="form-control" id="bulk-whatsapp-subject" name="subject"
                               value="دعوة للمشاركة في مؤتمر السيرة النبوية السنوي" required>
                    </div>

                    <div class="mb-3">
                        <label for="bulk-whatsapp-message" class="form-label">نص الرسالة</label>
                        <textarea class="form-control" id="bulk-whatsapp-message" name="message" rows="8" required>بسم الله الرحمن الرحيم

معالي النواب المحترمين

السلام عليكم ورحمة الله وبركاته

يسعدنا أن ندعوكم للمشاركة في مؤتمر السيرة النبوية السنوي الذي يهدف إلى نشر العلم والمعرفة حول سيرة النبي محمد صلى الله عليه وسلم.

نتطلع إلى مشاركتكم القيمة وإثراء المؤتمر بخبرتكم.

وفقكم الله وبارك فيكم

مع أطيب التحيات
لجنة تنظيم المؤتمر</textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fab fa-whatsapp me-2"></i>إرسال الدعوات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Bulk Email Invitation Modal -->
<div class="modal fade" id="bulkEmailInvitationModal" tabindex="-1" aria-labelledby="bulkEmailInvitationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bulkEmailInvitationModalLabel">
                    <i class="fas fa-envelope me-2"></i>إرسال دعوات جماعية بالبريد الإلكتروني
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'organizations:send_bulk_official_email_invitation' %}">
                {% csrf_token %}
                <input type="hidden" id="bulk-email-official-ids" name="official_ids">
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-envelope me-2"></i>
                        سيتم إرسال دعوات بريد إلكتروني إلى <strong id="bulk-email-count">جميع</strong> النواب المحددين
                    </div>

                    <div class="mb-3">
                        <label for="bulk-email-subject" class="form-label">موضوع الرسالة</label>
                        <input type="text" class="form-control" id="bulk-email-subject" name="subject"
                               value="دعوة للمشاركة في مؤتمر السيرة النبوية السنوي" required>
                    </div>

                    <div class="mb-3">
                        <label for="bulk-email-message" class="form-label">نص الرسالة</label>
                        <textarea class="form-control" id="bulk-email-message" name="message" rows="8" required>بسم الله الرحمن الرحيم

معالي النواب المحترمين

السلام عليكم ورحمة الله وبركاته

يسعدنا أن ندعوكم للمشاركة في مؤتمر السيرة النبوية السنوي الذي يهدف إلى نشر العلم والمعرفة حول سيرة النبي محمد صلى الله عليه وسلم.

نتطلع إلى مشاركتكم القيمة وإثراء المؤتمر بخبرتكم.

وفقكم الله وبارك فيكم

مع أطيب التحيات
لجنة تنظيم المؤتمر</textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-envelope me-2"></i>إرسال الدعوات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تأثيرات بصرية للبطاقات
    const cards = document.querySelectorAll('.official-card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.boxShadow = '0 10px 25px rgba(0,0,0,0.15)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.boxShadow = '0 5px 15px rgba(0,0,0,0.1)';
        });
    });

    // Checkbox functionality
    const officialCheckboxes = document.querySelectorAll('.official-checkbox');
    const selectedCountSpan = document.getElementById('selected-whatsapp-count');
    const selectedEmailCountSpan = document.getElementById('selected-email-count');

    function updateSelectedCount() {
        const selectedCheckboxes = document.querySelectorAll('.official-checkbox:checked');
        const count = selectedCheckboxes.length;

        if (selectedCountSpan) selectedCountSpan.textContent = count;
        if (selectedEmailCountSpan) selectedEmailCountSpan.textContent = count;
    }

    // Add event listeners to all checkboxes
    officialCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedCount);
    });

    // Add "Select All" functionality
    const selectAllBtn = document.createElement('button');
    selectAllBtn.className = 'btn btn-sm btn-outline-secondary me-2';
    selectAllBtn.innerHTML = '<i class="fas fa-check-square"></i> تحديد الكل';
    selectAllBtn.type = 'button';

    // Insert select all button before the invitation dropdown
    const invitationDropdown = document.querySelector('.btn-group');
    if (invitationDropdown && officialCheckboxes.length > 0) {
        invitationDropdown.parentNode.insertBefore(selectAllBtn, invitationDropdown);
    }

    let allSelected = false;
    selectAllBtn.addEventListener('click', function() {
        allSelected = !allSelected;

        officialCheckboxes.forEach(checkbox => {
            checkbox.checked = allSelected;
        });

        if (allSelected) {
            this.innerHTML = '<i class="fas fa-square"></i> إلغاء التحديد';
            this.classList.remove('btn-outline-secondary');
            this.classList.add('btn-secondary');
        } else {
            this.innerHTML = '<i class="fas fa-check-square"></i> تحديد الكل';
            this.classList.remove('btn-secondary');
            this.classList.add('btn-outline-secondary');
        }

        updateSelectedCount();
    });

    // Initial count update
    updateSelectedCount();

    // معالجة أزرار WhatsApp
    $('.send-whatsapp-btn').on('click', function() {
        const officialId = $(this).data('official-id');
        const officialName = $(this).data('official-name');
        const officialPhone = $(this).data('official-phone');

        $('#whatsapp-official-id').val(officialId);
        $('#whatsapp-official-name').text(officialName);
        $('#whatsapp-official-phone').text(officialPhone);
    });

    // معالجة أزرار البريد الإلكتروني
    $('.send-email-btn').on('click', function() {
        const officialId = $(this).data('official-id');
        const officialName = $(this).data('official-name');
        const officialEmail = $(this).data('official-email');

        $('#email-official-id').val(officialId);
        $('#email-official-name').text(officialName);
        $('#email-official-email').text(officialEmail);
    });

    // إرسال دعوات WhatsApp للمحددين
    $('#send-selected-whatsapp-invitations').on('click', function(e) {
        e.preventDefault();
        console.log('Send selected WhatsApp invitations clicked');

        const selectedOfficials = $('.official-checkbox:checked');
        console.log('Selected officials:', selectedOfficials.length);

        if (selectedOfficials.length === 0) {
            alert('يرجى تحديد نائب واحد على الأقل');
            return;
        }

        const officialIds = selectedOfficials.map(function() {
            return this.value;
        }).get();

        console.log('Official IDs:', officialIds);

        // فتح نموذج الدعوات الجماعية
        $('#bulk-whatsapp-official-ids').val(officialIds.join(','));
        $('#bulk-whatsapp-count').text(officialIds.length);
        $('#bulkWhatsappInvitationModal').modal('show');
    });

    // إرسال دعوات WhatsApp جماعية
    $('#send-bulk-whatsapp-invitations').on('click', function(e) {
        e.preventDefault();
        console.log('Send bulk WhatsApp invitations clicked');

        // مسح قيمة official_ids لإرسال لجميع النواب
        $('#bulk-whatsapp-official-ids').val('');
        $('#bulk-whatsapp-count').text('جميع');
        $('#bulkWhatsappInvitationModal').modal('show');
    });

    // إرسال دعوات بريد إلكتروني للمحددين
    $('#send-selected-email-invitations').on('click', function(e) {
        e.preventDefault();
        console.log('Send selected email invitations clicked');

        const selectedOfficials = $('.official-checkbox:checked');
        console.log('Selected officials:', selectedOfficials.length);

        if (selectedOfficials.length === 0) {
            alert('يرجى تحديد نائب واحد على الأقل');
            return;
        }

        const officialIds = selectedOfficials.map(function() {
            return this.value;
        }).get();

        console.log('Official IDs:', officialIds);

        // فتح نموذج الدعوات الجماعية
        $('#bulk-email-official-ids').val(officialIds.join(','));
        $('#bulk-email-count').text(officialIds.length);
        $('#bulkEmailInvitationModal').modal('show');
    });

    // إرسال دعوات بريد إلكتروني جماعية
    $('#send-bulk-email-invitations').on('click', function(e) {
        e.preventDefault();
        console.log('Send bulk email invitations clicked');

        // مسح قيمة official_ids لإرسال لجميع النواب
        $('#bulk-email-official-ids').val('');
        $('#bulk-email-count').text('جميع');
        $('#bulkEmailInvitationModal').modal('show');
    });
});
</script>
{% endblock %}
