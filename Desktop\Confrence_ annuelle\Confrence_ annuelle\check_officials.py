#!/usr/bin/env python
import os
import django

# Configure Django settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'conference.settings')
django.setup()

from organizations.models import OfficialPerson

# عدد المسؤولين الرسميين
officials_count = OfficialPerson.objects.count()
print(f'عدد المسؤولين الرسميين في قاعدة البيانات: {officials_count}')

# إحصائيات حسب الفئة
categories = OfficialPerson.objects.values_list('category', flat=True).distinct()
print(f'\nعدد الفئات/الوزارات: {len(categories)}')

print('\nتوزيع المسؤولين حسب الفئة:')
for category in categories:
    count = OfficialPerson.objects.filter(category=category).count()
    category_display = dict(OfficialPerson.CATEGORY_CHOICES).get(category, category)
    print(f'- {category_display}: {count} مسؤول')

# عرض أول 10 مسؤولين
print(f'\nأول 10 مسؤولين:')
for official in OfficialPerson.objects.all()[:10]:
    print(f'- {official.title} {official.name} - {official.position}')

if officials_count > 10:
    print(f'... و {officials_count - 10} مسؤول آخر')

# إحصائيات إضافية
print(f'\nإحصائيات إضافية:')
print(f'- المسؤولين الذين لديهم أرقام هواتف: {OfficialPerson.objects.exclude(phone__isnull=True).exclude(phone="").count()}')
print(f'- المسؤولين الذين لديهم بريد إلكتروني: {OfficialPerson.objects.exclude(email__isnull=True).exclude(email="").count()}')
print(f'- المسؤولين الذين لديهم صور: {OfficialPerson.objects.exclude(photo__isnull=True).exclude(photo="").count()}')
