{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    /* تحسينات عامة للصفحة */
    .page-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 20px 20px;
    }

    .admin-card {
        border: none;
        border-radius: 15px;
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        overflow: hidden;
        background: white;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        position: relative;
    }

    .admin-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #667eea, #764ba2);
    }

    .admin-card:hover {
        transform: translateY(-8px) scale(1.02);
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }

    .card-header {
        border-bottom: 1px solid #f1f3f4;
        background: linear-gradient(135deg, #f8f9fc 0%, #e8f4fd 100%);
        padding: 1.5rem;
    }

    .card-title {
        font-size: 1.2rem;
        font-weight: 700;
        color: #2c3e50;
        margin-bottom: 0.5rem;
    }

    .badge {
        font-size: 0.75rem;
        padding: 0.5em 1em;
        border-radius: 50px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .btn-group .btn {
        border-radius: 8px;
        font-size: 0.85rem;
        padding: 0.5rem 1rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-group .btn:hover {
        transform: translateY(-2px);
    }

    .card-body {
        padding: 1.5rem;
    }

    .text-muted.small {
        font-size: 0.85rem;
        font-weight: 500;
    }

    /* إصلاح موضع الـ footer */
    body {
        min-height: 100vh;
        display: flex;
        flex-direction: column;
        background-color: #f8f9fa;
    }

    .main-content {
        flex: 1;
    }

    footer {
        margin-top: auto !important;
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 1000;
    }

    /* إضافة padding للمحتوى لتجنب تداخل الـ footer */
    .main-content {
        padding-bottom: 200px;
    }

    /* تحسين بطاقات الإحصائيات */
    .stats-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-bottom: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.05);
        position: relative;
        overflow: hidden;
        transition: all 0.3s ease;
        border: none;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
    }

    .stats-card::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 4px;
    }

    .stats-card.primary::after {
        background: linear-gradient(90deg, #4e73df, #224abe);
    }

    .stats-card.success::after {
        background: linear-gradient(90deg, #1cc88a, #13855c);
    }

    .stats-card.info::after {
        background: linear-gradient(90deg, #36b9cc, #258391);
    }

    .stats-card.warning::after {
        background: linear-gradient(90deg, #f6c23e, #dda20a);
    }

    .stats-card.secondary::after {
        background: linear-gradient(90deg, #858796, #60616f);
    }

    .stats-card h3 {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }

    .stats-card p {
        margin: 0;
        font-weight: 500;
        color: #6c757d;
    }

    .stats-card .icon {
        position: absolute;
        top: 1.5rem;
        right: 1.5rem;
        font-size: 2rem;
        opacity: 0.2;
    }

    /* تحسين بطاقة البحث */
    .search-card {
        background: white;
        backdrop-filter: blur(10px);
        border-radius: 15px;
        border: none;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
        margin-bottom: 2rem;
    }

    /* تحسين أزرار الإجراءات */
    .action-btn {
        border-radius: 50px;
        padding: 0.5rem 1.25rem;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    }

    .action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.15);
    }

    /* تحسين الأفاتار */
    .avatar-circle {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, #f8f9fc 0%, #e8f4fd 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        overflow: hidden;
        position: relative;
    }

    .avatar-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
        border-radius: 50%;
    }

    .avatar-initials {
        font-weight: bold;
        color: #667eea;
        font-size: 1.2rem;
    }

    .avatar-icon {
        font-size: 1.8rem;
    }

    /* تحسين معلومات البطاقة */
    .info-item {
        display: flex;
        align-items: center;
        padding: 0.5rem 0;
        border-bottom: 1px solid #f1f3f4;
        transition: all 0.3s ease;
    }

    .info-item:last-child {
        border-bottom: none;
    }

    .info-item:hover {
        background-color: #f8f9fa;
        border-radius: 8px;
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }

    .info-icon {
        width: 35px;
        height: 35px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-left: 0.75rem;
        font-size: 0.9rem;
    }

    /* تحسين الأزرار */
    .btn-action {
        border-radius: 8px;
        font-weight: 600;
        padding: 0.5rem 1rem;
        transition: all 0.3s ease;
        border: 2px solid transparent;
    }

    .btn-action:hover {
        transform: translateY(-2px);
        border-color: currentColor;
    }

    /* تحسين الاستجابة للشاشات الصغيرة */
    @media (max-width: 768px) {
        .page-header {
            padding: 1rem 0;
        }

        .page-header h1 {
            font-size: 1.5rem;
        }

        .admin-card {
            margin-bottom: 1rem;
        }

        .stats-card {
            margin-bottom: 1rem;
        }

        .btn-group {
            flex-direction: column;
        }

        .btn-group .btn {
            margin-bottom: 0.25rem;
        }
    }

    /* تحسين التحميل */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.9);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 9999;
        display: none;
    }

    .loading-spinner {
        width: 50px;
        height: 50px;
        border: 5px solid #f3f3f3;
        border-top: 5px solid #667eea;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    /* تحسين البحث المباشر */
    .search-results {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        z-index: 1000;
        max-height: 300px;
        overflow-y: auto;
        display: none;
    }

    .search-result-item {
        padding: 0.75rem 1rem;
        border-bottom: 1px solid #f1f3f4;
        cursor: pointer;
        transition: background-color 0.2s ease;
    }

    .search-result-item:hover {
        background-color: #f8f9fa;
    }

    .search-result-item:last-child {
        border-bottom: none;
    }
</style>
{% endblock %}

{% block content %}
<!-- Loading Overlay -->
<div class="loading-overlay" id="loadingOverlay">
    <div class="loading-spinner"></div>
</div>

<!-- Header Section -->
<div class="page-header">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="fas fa-users-cog me-3"></i>
                    {{ title }}
                </h1>
                <p class="mb-0 opacity-75">إدارة شاملة لحسابات المسؤولين في النظام مع صلاحيات متقدمة</p>
            </div>
            <div class="col-md-4 text-end">
                <div class="d-flex justify-content-end gap-2">
                    <button class="btn btn-light action-btn" onclick="exportAdmins()">
                        <i class="fas fa-download me-2"></i>تصدير
                    </button>
                    <a href="{% url 'core:admin_create' %}" class="btn btn-warning action-btn">
                        <i class="fas fa-plus me-2"></i>إضافة مسؤول
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">{%csrf_token%}

    <!-- إحصائيات محسنة -->
    <div class="row mb-4">
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="stats-card primary text-center">
                <i class="fas fa-users icon text-primary"></i>
                <h3 class="text-primary">{{ stats.total_admins }}</h3>
                <p>إجمالي المسؤولين</p>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="stats-card success text-center">
                <i class="fas fa-crown icon text-success"></i>
                <h3 class="text-success">{{ stats.super_admins }}</h3>
                <p>مسؤولين كبار</p>
            </div>
        </div>
        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
            <div class="stats-card info text-center">
                <i class="fas fa-user-tie icon text-info"></i>
                <h3 class="text-info">{{ stats.regular_admins }}</h3>
                <p>مسؤولين عاديين</p>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 col-sm-6 mb-3">
            <div class="stats-card warning text-center">
                <i class="fas fa-check-circle icon text-warning"></i>
                <h3 class="text-warning">{{ stats.active_admins }}</h3>
                <p>نشط</p>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 col-sm-6 mb-3">
            <div class="stats-card secondary text-center">
                <i class="fas fa-pause-circle icon text-secondary"></i>
                <h3 class="text-secondary">{{ stats.inactive_admins }}</h3>
                <p>غير نشط</p>
            </div>
        </div>
    </div>

    <!-- أدوات الإدارة المحسنة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h4 class="mb-1">قائمة المسؤولين</h4>
            <p class="text-muted mb-0">إدارة وتنظيم حسابات المسؤولين</p>
        </div>
        <div class="d-flex gap-2">
            <button class="btn btn-outline-primary action-btn" onclick="bulkActions()">
                <i class="fas fa-tasks me-2"></i>إجراءات جماعية
            </button>
            <button class="btn btn-outline-info action-btn" onclick="refreshData()">
                <i class="fas fa-sync-alt me-2"></i>تحديث
            </button>
            <div class="dropdown">
                <button class="btn btn-outline-secondary action-btn dropdown-toggle" type="button" data-bs-toggle="dropdown">
                    <i class="fas fa-filter me-2"></i>فلترة سريعة
                </button>
                <ul class="dropdown-menu">
                    <li><a class="dropdown-item" href="?admin_type=super_admin">المسؤولين الكبار</a></li>
                    <li><a class="dropdown-item" href="?admin_type=regular_admin">المسؤولين العاديين</a></li>
                    <li><a class="dropdown-item" href="?status=active">النشطين فقط</a></li>
                    <li><a class="dropdown-item" href="?status=inactive">غير النشطين</a></li>
                </ul>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث المحسنة -->
    <div class="search-card">
        <div class="card-header border-0 bg-transparent">
            <div class="d-flex align-items-center justify-content-between">
                <h6 class="card-title mb-0 fw-bold">
                    <i class="fas fa-search me-2 text-primary"></i>البحث والفلترة المتقدمة
                </h6>
                <button class="btn btn-sm btn-outline-primary" type="button" data-bs-toggle="collapse" data-bs-target="#searchFilters">
                    <i class="fas fa-chevron-down"></i>
                </button>
            </div>
        </div>
        <div class="collapse show" id="searchFilters">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label fw-bold text-dark">البحث العام</label>
                        <div class="input-group">
                            <span class="input-group-text bg-light border-end-0">
                                <i class="fas fa-search text-primary"></i>
                            </span>
                            <input type="text" name="search" class="form-control border-start-0"
                                   value="{{ search_query }}"
                                   placeholder="البحث بالاسم أو اسم المستخدم أو البريد الإلكتروني">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label fw-bold text-dark">نوع المسؤول</label>
                        <select name="admin_type" class="form-select">
                            <option value="">جميع الأنواع</option>
                            {% for value, label in admin_type_choices %}
                                <option value="{{ value }}" {% if admin_type_filter == value %}selected{% endif %}>{{ label }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label fw-bold text-dark">الحالة</label>
                        <select name="status" class="form-select">
                            <option value="">جميع الحالات</option>
                            <option value="active" {% if status_filter == 'active' %}selected{% endif %}>نشط</option>
                            <option value="inactive" {% if status_filter == 'inactive' %}selected{% endif %}>غير نشط</option>
                        </select>
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <div class="btn-group w-100">
                            <button type="submit" class="btn btn-primary action-btn">
                                <i class="fas fa-search"></i> بحث
                            </button>
                            <a href="{% url 'core:admin_list' %}" class="btn btn-outline-secondary action-btn">
                                <i class="fas fa-undo"></i> إعادة تعيين
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- قائمة المسؤولين المحسنة -->
    <div class="row">
        {% if page_obj %}
            {% for admin_profile in page_obj %}
            <div class="col-lg-6 col-xl-4 mb-4">
                <div class="admin-card h-100">
                    <div class="card-header border-0">
                        <div class="d-flex align-items-center justify-content-between">
                            <div class="d-flex align-items-center">
                                <div class="avatar-circle me-3">
                                    {% if admin_profile.profile_image %}
                                        <img src="{{ admin_profile.profile_image.url }}" alt="{{ admin_profile.get_display_name }}" class="avatar-image">
                                    {% else %}
                                        {% if admin_profile.get_admin_type_display == 'مسؤول كبير' %}
                                            <i class="fas fa-crown text-warning avatar-icon"></i>
                                        {% else %}
                                            <span class="avatar-initials">{{ admin_profile.get_initials }}</span>
                                        {% endif %}
                                    {% endif %}
                                </div>
                                <div>
                                    <h5 class="card-title mb-1">{{ admin_profile.get_display_name }}</h5>
                                    <small class="text-muted fw-bold">{{ admin_profile.get_admin_type_display }}</small>
                                </div>
                            </div>
                            <div>
                                {% if admin_profile.is_active %}
                                    <span class="badge bg-success">
                                        <i class="fas fa-check-circle me-1"></i>نشط
                                    </span>
                                {% else %}
                                    <span class="badge bg-secondary">
                                        <i class="fas fa-pause-circle me-1"></i>غير نشط
                                    </span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <div class="info-item">
                                <div class="info-icon bg-primary bg-opacity-10">
                                    <i class="fas fa-user text-primary"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <small class="text-muted d-block">اسم المستخدم</small>
                                    <strong class="text-dark">{{ admin_profile.user.username }}</strong>
                                </div>
                            </div>

                            {% if admin_profile.user.email %}
                            <div class="info-item">
                                <div class="info-icon bg-info bg-opacity-10">
                                    <i class="fas fa-envelope text-info"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <small class="text-muted d-block">البريد الإلكتروني</small>
                                    <span class="text-dark">{{ admin_profile.user.email }}</span>
                                </div>
                            </div>
                            {% endif %}

                            {% if admin_profile.phone %}
                            <div class="info-item">
                                <div class="info-icon bg-success bg-opacity-10">
                                    <i class="fas fa-phone text-success"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <small class="text-muted d-block">رقم الهاتف</small>
                                    <span class="text-dark">{{ admin_profile.phone }}</span>
                                </div>
                            </div>
                            {% endif %}

                            {% if admin_profile.department %}
                            <div class="info-item">
                                <div class="info-icon bg-warning bg-opacity-10">
                                    <i class="fas fa-building text-warning"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <small class="text-muted d-block">القسم</small>
                                    <span class="text-dark">{{ admin_profile.department }}</span>
                                </div>
                            </div>
                            {% endif %}

                            <div class="info-item">
                                <div class="info-icon bg-secondary bg-opacity-10">
                                    <i class="fas fa-calendar text-secondary"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <small class="text-muted d-block">تاريخ الإنشاء</small>
                                    <span class="text-dark">{{ admin_profile.created_at|date:"Y/m/d" }}</span>
                                </div>
                            </div>
                        </div>

                        <div class="d-grid gap-2">
                            <div class="row g-2">
                                <div class="col-6">
                                    <a href="{% url 'core:admin_detail' admin_profile.pk %}"
                                       class="btn btn-outline-primary btn-action w-100">
                                        <i class="fas fa-eye me-1"></i>عرض
                                    </a>
                                </div>
                                <div class="col-6">
                                    <a href="{% url 'core:admin_edit' admin_profile.pk %}"
                                       class="btn btn-outline-warning btn-action w-100">
                                        <i class="fas fa-edit me-1"></i>تعديل
                                    </a>
                                </div>
                            </div>

                            {% if admin_profile.user.username != 'Saad' and admin_profile.user != user %}
                            <div class="row g-2">
                                <div class="col-6">
                                    <button class="btn btn-outline-{% if admin_profile.is_active %}secondary{% else %}success{% endif %} btn-action w-100"
                                            onclick="toggleAdminStatus({{ admin_profile.pk }})">
                                        <i class="fas fa-{% if admin_profile.is_active %}pause{% else %}play{% endif %} me-1"></i>
                                        {% if admin_profile.is_active %}إيقاف{% else %}تفعيل{% endif %}
                                    </button>
                                </div>
                                <div class="col-6">
                                    <button class="btn btn-outline-danger btn-action w-100"
                                            onclick="deleteAdmin({{ admin_profile.pk }}, '{{ admin_profile.get_display_name }}')">
                                        <i class="fas fa-trash me-1"></i>حذف
                                    </button>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="col-12">
                        <div class="text-center py-5">
                            <i class="fas fa-users-cog fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">لا توجد حسابات مسؤولين</h5>
                            <p class="text-muted">ابدأ بإضافة مسؤولين جدد</p>
                            <a href="{% url 'core:admin_create' %}" class="btn btn-primary">
                                <i class="fas fa-plus"></i> إضافة مسؤول جديد
                            </a>
                        </div>
                    </div>
                {% endif %}
            </div>

            <!-- التصفح -->
            {% if page_obj.has_other_pages %}
            <nav aria-label="Page navigation" class="mt-4">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if admin_type_filter %}&admin_type={{ admin_type_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">السابق</a>
                        </li>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <li class="page-item active">
                                <span class="page-link">{{ num }}</span>
                            </li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if admin_type_filter %}&admin_type={{ admin_type_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">{{ num }}</a>
                            </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="?page={{ page_obj.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if admin_type_filter %}&admin_type={{ admin_type_filter }}{% endif %}{% if status_filter %}&status={{ status_filter }}{% endif %}">التالي</a>
                        </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
</div>

<script>
// تحسين وظائف الجافا سكريبت
function showToast(message, type = 'success') {
    // إنشاء toast notification
    const toast = document.createElement('div');
    toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    toast.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.body.appendChild(toast);

    // إزالة التنبيه بعد 5 ثوان
    setTimeout(() => {
        if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
        }
    }, 5000);
}

function toggleAdminStatus(adminId) {
    Swal.fire({
        title: 'تأكيد العملية',
        text: 'هل أنت متأكد من تغيير حالة هذا المسؤول؟',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'نعم، تأكيد',
        cancelButtonText: 'إلغاء'
    }).then((result) => {
        if (result.isConfirmed) {
            // إظهار loading
            Swal.fire({
                title: 'جاري المعالجة...',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            fetch(`/admin-management/${adminId}/toggle-status/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                Swal.close();
                if (data.success) {
                    Swal.fire({
                        title: 'تم بنجاح!',
                        text: data.message,
                        icon: 'success',
                        timer: 2000,
                        showConfirmButton: false
                    }).then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire('خطأ!', data.message, 'error');
                }
            })
            .catch(error => {
                Swal.close();
                Swal.fire('خطأ!', 'حدث خطأ في الاتصال', 'error');
            });
        }
    });
}

function deleteAdmin(adminId, adminName) {
    Swal.fire({
        title: 'تحذير!',
        text: `هل أنت متأكد من حذف المسؤول "${adminName}"؟`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'نعم، احذف',
        cancelButtonText: 'إلغاء',
        footer: '<small class="text-muted">هذا الإجراء لا يمكن التراجع عنه</small>'
    }).then((result) => {
        if (result.isConfirmed) {
            // إظهار loading
            Swal.fire({
                title: 'جاري الحذف...',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            fetch(`/admin-management/${adminId}/delete/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value,
                    'Content-Type': 'application/json',
                },
            })
            .then(response => response.json())
            .then(data => {
                Swal.close();
                if (data.success) {
                    Swal.fire({
                        title: 'تم الحذف!',
                        text: data.message,
                        icon: 'success',
                        timer: 2000,
                        showConfirmButton: false
                    }).then(() => {
                        location.reload();
                    });
                } else {
                    Swal.fire('خطأ!', data.message, 'error');
                }
            })
            .catch(error => {
                Swal.close();
                Swal.fire('خطأ!', 'حدث خطأ في الاتصال', 'error');
            });
        }
    });
}

function exportAdmins() {
    Swal.fire({
        title: 'تصدير البيانات',
        text: 'اختر تنسيق التصدير',
        icon: 'question',
        showCancelButton: true,
        confirmButtonText: 'CSV',
        cancelButtonText: 'إلغاء',
        showDenyButton: true,
        denyButtonText: 'إحصائيات'
    }).then((result) => {
        if (result.isConfirmed) {
            // تصدير CSV
            window.location.href = '{% url "core:export_admins_csv" %}';
        } else if (result.isDenied) {
            // عرض الإحصائيات
            window.location.href = '{% url "core:admin_statistics" %}';
        }
    });
}

function bulkActions() {
    Swal.fire({
        title: 'الإجراءات الجماعية',
        text: 'هذه الميزة قيد التطوير',
        icon: 'info',
        confirmButtonText: 'حسناً'
    });
}

function refreshData() {
    Swal.fire({
        title: 'تحديث البيانات',
        text: 'جاري تحديث البيانات...',
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });

    // محاكاة تحديث البيانات
    setTimeout(() => {
        location.reload();
    }, 1500);
}

// وظائف مساعدة
function showLoading() {
    document.getElementById('loadingOverlay').style.display = 'flex';
}

function hideLoading() {
    document.getElementById('loadingOverlay').style.display = 'none';
}

function animateCounter(element, target) {
    let current = 0;
    const increment = target / 50;
    const timer = setInterval(() => {
        current += increment;
        if (current >= target) {
            current = target;
            clearInterval(timer);
        }
        element.textContent = Math.floor(current);
    }, 20);
}

// تحسين تجربة المستخدم
document.addEventListener('DOMContentLoaded', function() {
    // إخفاء loading overlay
    hideLoading();

    // تحريك العدادات
    const counters = document.querySelectorAll('.stats-card h3');
    counters.forEach(counter => {
        const target = parseInt(counter.textContent);
        if (!isNaN(target)) {
            counter.textContent = '0';
            setTimeout(() => animateCounter(counter, target), 500);
        }
    });

    // إضافة تأثيرات hover للبطاقات
    const cards = document.querySelectorAll('.admin-card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-8px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // تحسين البحث المباشر
    const searchInput = document.querySelector('input[name="search"]');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.trim();

            if (query.length > 2) {
                searchTimeout = setTimeout(() => {
                    // تمييز النتائج المطابقة
                    highlightSearchResults(query);
                }, 300);
            } else {
                // إزالة التمييز
                removeHighlight();
            }
        });
    }

    // تحسين الفلاتر
    const filterSelects = document.querySelectorAll('select[name="admin_type"], select[name="status"]');
    filterSelects.forEach(select => {
        select.addEventListener('change', function() {
            showLoading();
            this.form.submit();
        });
    });

    // تحسين أزرار الإجراءات
    const actionButtons = document.querySelectorAll('.btn-action');
    actionButtons.forEach(button => {
        button.addEventListener('click', function() {
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = '';
            }, 150);
        });
    });
});

function highlightSearchResults(query) {
    const cards = document.querySelectorAll('.admin-card');
    cards.forEach(card => {
        const text = card.textContent.toLowerCase();
        if (text.includes(query.toLowerCase())) {
            card.style.border = '2px solid #667eea';
            card.style.boxShadow = '0 0 20px rgba(102, 126, 234, 0.3)';
        } else {
            card.style.opacity = '0.5';
        }
    });
}

function removeHighlight() {
    const cards = document.querySelectorAll('.admin-card');
    cards.forEach(card => {
        card.style.border = '';
        card.style.boxShadow = '';
        card.style.opacity = '';
    });
}

// تحسين التصفح
function goToPage(page) {
    showLoading();
    const url = new URL(window.location);
    url.searchParams.set('page', page);
    window.location.href = url.toString();
}

// تحسين الاستجابة للشاشات الصغيرة
function adjustForMobile() {
    if (window.innerWidth < 768) {
        const cards = document.querySelectorAll('.admin-card');
        cards.forEach(card => {
            card.style.marginBottom = '1rem';
        });
    }
}

window.addEventListener('resize', adjustForMobile);
adjustForMobile();
</script>

<!-- إضافة SweetAlert2 للتنبيهات المحسنة -->
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
{% endblock %}
