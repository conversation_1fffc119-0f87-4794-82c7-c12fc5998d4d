{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .stats-dashboard {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 2rem 0;
        margin-bottom: 2rem;
        border-radius: 0 0 20px 20px;
    }
    
    .chart-container {
        background: white;
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }
    
    .metric-card {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        text-align: center;
        box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }
    
    .metric-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0,0,0,0.15);
    }
    
    .metric-card::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 4px;
    }
    
    .metric-card.primary::after {
        background: linear-gradient(90deg, #4e73df, #224abe);
    }
    
    .metric-card.success::after {
        background: linear-gradient(90deg, #1cc88a, #13855c);
    }
    
    .metric-card.info::after {
        background: linear-gradient(90deg, #36b9cc, #258391);
    }
    
    .metric-card.warning::after {
        background: linear-gradient(90deg, #f6c23e, #dda20a);
    }
    
    .metric-value {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }
    
    .metric-label {
        color: #6c757d;
        font-weight: 500;
    }
    
    .metric-icon {
        position: absolute;
        top: 1rem;
        right: 1rem;
        font-size: 2rem;
        opacity: 0.2;
    }
</style>
{% endblock %}

{% block content %}
<!-- Header -->
<div class="stats-dashboard">
    <div class="container-fluid">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">
                    <i class="fas fa-chart-bar me-3"></i>
                    {{ title }}
                </h1>
                <p class="mb-0 opacity-75">تحليل شامل لبيانات المسؤولين في النظام</p>
            </div>
            <div class="col-md-4 text-end">
                <a href="{% url 'core:admin_list' %}" class="btn btn-light">
                    <i class="fas fa-arrow-right me-2"></i>العودة للقائمة
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <!-- الإحصائيات الرئيسية -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="metric-card primary">
                <i class="fas fa-users metric-icon text-primary"></i>
                <div class="metric-value text-primary">{{ total_admins }}</div>
                <div class="metric-label">إجمالي المسؤولين</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="metric-card success">
                <i class="fas fa-check-circle metric-icon text-success"></i>
                <div class="metric-value text-success">{{ active_admins }}</div>
                <div class="metric-label">المسؤولين النشطين</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="metric-card info">
                <i class="fas fa-crown metric-icon text-info"></i>
                <div class="metric-value text-info">
                    {% for type in admin_types %}
                        {% if type.admin_type == 'super_admin' %}{{ type.count }}{% endif %}
                    {% endfor %}
                </div>
                <div class="metric-label">المسؤولين الكبار</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="metric-card warning">
                <i class="fas fa-user-tie metric-icon text-warning"></i>
                <div class="metric-value text-warning">
                    {% for type in admin_types %}
                        {% if type.admin_type == 'regular_admin' %}{{ type.count }}{% endif %}
                    {% endfor %}
                </div>
                <div class="metric-label">المسؤولين العاديين</div>
            </div>
        </div>
    </div>

    <!-- الرسم البياني -->
    <div class="row">
        <div class="col-12">
            <div class="chart-container">
                <h4 class="mb-4">
                    <i class="fas fa-chart-line text-primary me-2"></i>
                    إحصائيات إنشاء المسؤولين (آخر 30 يوم)
                </h4>
                <canvas id="adminsChart" width="400" height="100"></canvas>
            </div>
        </div>
    </div>

    <!-- تفاصيل إضافية -->
    <div class="row">
        <div class="col-md-6">
            <div class="chart-container">
                <h5 class="mb-3">
                    <i class="fas fa-pie-chart text-primary me-2"></i>
                    توزيع أنواع المسؤولين
                </h5>
                <canvas id="adminTypesChart" width="400" height="200"></canvas>
            </div>
        </div>
        <div class="col-md-6">
            <div class="chart-container">
                <h5 class="mb-3">
                    <i class="fas fa-chart-bar text-primary me-2"></i>
                    حالة المسؤولين
                </h5>
                <canvas id="adminStatusChart" width="400" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// الرسم البياني الخطي لإنشاء المسؤولين
const ctx1 = document.getElementById('adminsChart').getContext('2d');
const adminsChart = new Chart(ctx1, {
    type: 'line',
    data: {
        labels: {{ chart_dates|safe }},
        datasets: [{
            label: 'المسؤولين الجدد',
            data: {{ chart_counts|safe }},
            borderColor: '#667eea',
            backgroundColor: 'rgba(102, 126, 234, 0.1)',
            borderWidth: 3,
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                display: true,
                position: 'top'
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        }
    }
});

// الرسم البياني الدائري لأنواع المسؤولين
const ctx2 = document.getElementById('adminTypesChart').getContext('2d');
const adminTypesChart = new Chart(ctx2, {
    type: 'doughnut',
    data: {
        labels: ['مسؤولين كبار', 'مسؤولين عاديين'],
        datasets: [{
            data: [
                {% for type in admin_types %}
                    {% if type.admin_type == 'super_admin' %}{{ type.count }}{% endif %}
                {% endfor %},
                {% for type in admin_types %}
                    {% if type.admin_type == 'regular_admin' %}{{ type.count }}{% endif %}
                {% endfor %}
            ],
            backgroundColor: ['#1cc88a', '#36b9cc'],
            borderWidth: 0
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

// الرسم البياني لحالة المسؤولين
const ctx3 = document.getElementById('adminStatusChart').getContext('2d');
const adminStatusChart = new Chart(ctx3, {
    type: 'bar',
    data: {
        labels: ['نشط', 'غير نشط'],
        datasets: [{
            label: 'عدد المسؤولين',
            data: [{{ active_admins }}, {{ total_admins|add:"-"|add:active_admins }}],
            backgroundColor: ['#1cc88a', '#858796'],
            borderWidth: 0,
            borderRadius: 8
        }]
    },
    options: {
        responsive: true,
        plugins: {
            legend: {
                display: false
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    stepSize: 1
                }
            }
        }
    }
});
</script>
{% endblock %}
