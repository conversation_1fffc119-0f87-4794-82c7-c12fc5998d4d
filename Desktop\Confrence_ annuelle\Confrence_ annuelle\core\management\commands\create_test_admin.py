#!/usr/bin/env python
"""
Django management command لإنشاء مسؤول عادي للاختبار
"""

from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from core.models import AdminProfile


class Command(BaseCommand):
    help = 'إنشاء مسؤول عادي للاختبار'

    def handle(self, *args, **options):
        # إنشاء مسؤول عادي للاختبار
        username = 'admin_test'
        password = 'test123'
        email = '<EMAIL>'
        
        # التحقق من وجود المستخدم
        user, created = User.objects.get_or_create(
            username=username,
            defaults={
                'email': email,
                'is_staff': True,
                'is_superuser': False,
                'is_active': True,
                'first_name': 'مسؤول',
                'last_name': 'تجريبي'
            }
        )
        
        # تعيين كلمة المرور
        user.set_password(password)
        user.save()
        
        # إنشاء أو تحديث ملف المسؤول
        admin_profile, profile_created = AdminProfile.objects.get_or_create(
            user=user,
            defaults={
                'admin_type': 'regular_admin',
                'full_name': 'مسؤول تجريبي',
                'department': 'قسم الاختبار',
                'notes': 'مسؤول عادي للاختبار',
                'is_active': True
            }
        )
        
        # تحديث البيانات إذا كان الملف موجوداً
        if not profile_created:
            admin_profile.admin_type = 'regular_admin'
            admin_profile.full_name = 'مسؤول تجريبي'
            admin_profile.department = 'قسم الاختبار'
            admin_profile.notes = 'مسؤول عادي للاختبار'
            admin_profile.is_active = True
            admin_profile.save()
        
        if created:
            self.stdout.write(
                self.style.SUCCESS(f'تم إنشاء حساب المسؤول العادي "{username}" بنجاح!')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(f'تم تحديث حساب المسؤول العادي "{username}" بنجاح!')
            )
        
        self.stdout.write(
            self.style.WARNING(f'اسم المستخدم: {username}')
        )
        self.stdout.write(
            self.style.WARNING(f'كلمة المرور: {password}')
        )
