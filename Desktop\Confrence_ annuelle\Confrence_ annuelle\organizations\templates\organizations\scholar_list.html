{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .scholar-card {
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 15px;
        background: white;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        transition: transform 0.2s;
    }

    .scholar-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    .scholar-photo {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        object-fit: cover;
        border: 2px solid #007bff;
    }

    .scholar-title {
        color: #007bff;
        font-weight: bold;
        margin-bottom: 5px;
    }

    .scholar-name {
        font-size: 1.1em;
        font-weight: 600;
        color: #333;
        margin-bottom: 5px;
    }

    .scholar-position {
        color: #666;
        font-style: italic;
        margin-bottom: 5px;
    }

    .scholar-organization {
        color: #555;
        margin-bottom: 5px;
    }

    .scholar-country {
        color: #777;
        font-size: 0.9em;
    }

    .status-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8em;
        font-weight: bold;
    }

    .status-invited { background-color: #fff3cd; color: #856404; }
    .status-confirmed { background-color: #d4edda; color: #155724; }
    .status-declined { background-color: #f8d7da; color: #721c24; }
    .status-attended { background-color: #d1ecf1; color: #0c5460; }

    .filter-section {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
    }

    .stats-section {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
    }

    .stat-item {
        text-align: center;
    }

    .stat-number {
        font-size: 2em;
        font-weight: bold;
        display: block;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="fas fa-graduation-cap text-primary"></i>
                    {{ title }}
                </h1>
                <div>
                    <a href="{% url 'organizations:scholar_create' %}" class="btn btn-primary me-2">
                        <i class="fas fa-plus"></i> إضافة عالم جديد
                    </a>

                    <!-- WhatsApp Invitation Buttons -->
                    <div class="btn-group me-2" role="group">
                        <button type="button" class="btn btn-outline-success dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fab fa-whatsapp"></i> دعوات WhatsApp
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'organizations:send_scholar_invitation' %}">
                                <i class="fab fa-whatsapp text-success"></i> دعوة فردية
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'organizations:send_bulk_scholar_invitation' %}">
                                <i class="fab fa-whatsapp text-success"></i> دعوات جماعية
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" id="send-selected-invitations">
                                <i class="fas fa-check-circle text-success"></i> دعوة المحددين (<span id="selected-scholars-count">0</span>)
                            </a></li>
                        </ul>
                    </div>

                    <!-- Email Invitation Buttons -->
                    <div class="btn-group me-2" role="group">
                        <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-envelope"></i> دعوات البريد الإلكتروني
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" data-bs-toggle="modal" data-bs-target="#emailInvitationModal">
                                <i class="fas fa-envelope text-primary"></i> دعوة فردية
                            </a></li>
                            <li><a class="dropdown-item" href="#" id="send-bulk-email-invitations">
                                <i class="fas fa-envelope text-primary"></i> دعوات جماعية
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" id="send-selected-email-invitations">
                                <i class="fas fa-envelope text-primary"></i> دعوة المحددين (<span id="selected-email-count">0</span>)
                            </a></li>
                        </ul>
                    </div>

                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-success dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-download"></i> تصدير
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'organizations:scholar_export_excel' %}">
                                <i class="fas fa-file-excel text-success"></i> تصدير Excel
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'organizations:scholar_export_csv' %}">
                                <i class="fas fa-file-csv text-info"></i> تصدير CSV
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Statistics -->
            <div class="stats-section">
                <div class="row">
                    <div class="col-md-3">
                        <div class="stat-item">
                            <span class="stat-number">{{ total_scholars }}</span>
                            <span>إجمالي العلماء</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <span class="stat-number">{{ confirmed_scholars }}</span>
                            <span>مؤكد المشاركة</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <span class="stat-number">{{ attended_scholars }}</span>
                            <span>حضر الفعالية</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="stat-item">
                            <span class="stat-number">{{ scholars|length }}</span>
                            <span>النتائج المعروضة</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters -->
            <div class="filter-section">
                <form method="get" class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">البحث</label>
                        <input type="text" class="form-control" name="search" value="{{ search_query }}"
                               placeholder="البحث في الأسماء والمناصب...">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">اللقب</label>
                        <select class="form-select" name="title">
                            <option value="">جميع الألقاب</option>
                            {% for value, label in title_choices %}
                                <option value="{{ value }}" {% if title_filter == value %}selected{% endif %}>
                                    {{ label }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">حالة المشاركة</label>
                        <select class="form-select" name="status">
                            <option value="">جميع الحالات</option>
                            {% for value, label in status_choices %}
                                <option value="{{ value }}" {% if status_filter == value %}selected{% endif %}>
                                    {{ label }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">البلد</label>
                        <input type="text" class="form-control" name="country" value="{{ country_filter }}"
                               placeholder="البلد">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">الترتيب</label>
                        <select class="form-select" name="sort">
                            <option value="name" {% if current_sort == 'name' %}selected{% endif %}>الاسم</option>
                            <option value="title" {% if current_sort == 'title' %}selected{% endif %}>اللقب</option>
                            <option value="status" {% if current_sort == 'status' %}selected{% endif %}>الحالة</option>
                            <option value="date" {% if current_sort == 'date' %}selected{% endif %}>تاريخ الإضافة</option>
                        </select>
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-primary d-block w-100">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </form>
            </div>

            <!-- Scholars List -->
            <div class="row">
                {% for scholar in scholars %}
                <div class="col-md-6 col-lg-4">
                    <div class="scholar-card">
                        <div class="d-flex align-items-start">
                            <div class="flex-shrink-0 me-2">
                                <input type="checkbox" class="form-check-input scholar-checkbox" name="selected_scholars" value="{{ scholar.id }}" id="scholar-{{ scholar.id }}">
                            </div>
                            <div class="flex-shrink-0">
                                {% if scholar.photo %}
                                    <img src="{{ scholar.photo.url }}" alt="{{ scholar.name }}" class="scholar-photo">
                                {% else %}
                                    <div class="scholar-photo d-flex align-items-center justify-content-center bg-primary text-white">
                                        <i class="fas fa-user"></i>
                                    </div>
                                {% endif %}
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <div class="scholar-title">{{ scholar.get_title_display }}</div>
                                <div class="scholar-name">{{ scholar.name }}</div>
                                {% if scholar.position %}
                                    <div class="scholar-position">{{ scholar.position }}</div>
                                {% endif %}
                                {% if scholar.organization %}
                                    <div class="scholar-organization">
                                        <i class="fas fa-building text-muted"></i> {{ scholar.organization }}
                                    </div>
                                {% endif %}
                                {% if scholar.country %}
                                    <div class="scholar-country">
                                        <i class="fas fa-map-marker-alt text-muted"></i> {{ scholar.country }}
                                        {% if scholar.city %}, {{ scholar.city }}{% endif %}
                                    </div>
                                {% endif %}
                                <div class="mt-2">
                                    <span class="status-badge status-{{ scholar.participation_status }}">
                                        {{ scholar.get_participation_status_display }}
                                    </span>
                                </div>
                            </div>
                        </div>

                        <div class="mt-3">
                            <div class="d-flex justify-content-between mb-2">
                                <a href="{% url 'organizations:scholar_detail' scholar.pk %}"
                                   class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i> عرض
                                </a>
                                <a href="{% url 'organizations:scholar_update' scholar.pk %}"
                                   class="btn btn-sm btn-outline-warning">
                                    <i class="fas fa-edit"></i> تعديل
                                </a>
                                <a href="{% url 'organizations:scholar_delete' scholar.pk %}"
                                   class="btn btn-sm btn-outline-danger"
                                   onclick="return confirm('هل أنت متأكد من حذف هذا العالم؟')">
                                    <i class="fas fa-trash"></i> حذف
                                </a>
                            </div>
                            <div class="d-grid gap-2">
                                <a href="{% url 'organizations:send_scholar_invitation' %}?scholar_id={{ scholar.id }}"
                                   class="btn btn-sm btn-success">
                                    <i class="fab fa-whatsapp"></i> دعوة WhatsApp
                                </a>
                                <button type="button" class="btn btn-sm btn-primary send-email-btn"
                                        data-scholar-id="{{ scholar.id }}"
                                        data-scholar-name="{{ scholar.name }}"
                                        data-scholar-email="{{ scholar.email }}"
                                        data-bs-toggle="modal"
                                        data-bs-target="#emailInvitationModal">
                                    <i class="fas fa-envelope"></i> دعوة بريد إلكتروني
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="col-12">
                    <div class="text-center py-5">
                        <i class="fas fa-graduation-cap fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">لا توجد نتائج</h4>
                        <p class="text-muted">لم يتم العثور على علماء مطابقين لمعايير البحث</p>
                        <a href="{% url 'organizations:scholar_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> إضافة عالم جديد
                        </a>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const scholarCheckboxes = document.querySelectorAll('.scholar-checkbox');
    const selectedCountSpan = document.getElementById('selected-scholars-count');
    const sendSelectedBtn = document.getElementById('send-selected-invitations');

    function updateSelectedCount() {
        const selectedCheckboxes = document.querySelectorAll('.scholar-checkbox:checked');
        const count = selectedCheckboxes.length;

        selectedCountSpan.textContent = count;

        // تحديث عداد البريد الإلكتروني
        const emailCountSpan = document.getElementById('selected-email-count');
        if (emailCountSpan) {
            emailCountSpan.textContent = count;
        }

        if (count > 0) {
            sendSelectedBtn.classList.remove('disabled');
            sendSelectedBtn.style.pointerEvents = 'auto';
        } else {
            sendSelectedBtn.classList.add('disabled');
            sendSelectedBtn.style.pointerEvents = 'none';
        }
    }

    // Add event listeners to all checkboxes
    scholarCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedCount);
    });

    // Handle send selected invitations
    sendSelectedBtn.addEventListener('click', function(e) {
        e.preventDefault();

        const selectedCheckboxes = document.querySelectorAll('.scholar-checkbox:checked');
        const selectedIds = Array.from(selectedCheckboxes).map(cb => cb.value);

        if (selectedIds.length === 0) {
            alert('الرجاء تحديد عالم واحد على الأقل');
            return;
        }

        // Redirect to bulk invitation page with selected IDs
        window.location.href = `{% url 'organizations:send_bulk_scholar_invitation' %}?selected=${selectedIds.join(',')}`;
    });

    // Add "Select All" functionality
    const selectAllBtn = document.createElement('button');
    selectAllBtn.className = 'btn btn-sm btn-outline-secondary me-2';
    selectAllBtn.innerHTML = '<i class="fas fa-check-square"></i> تحديد الكل';
    selectAllBtn.type = 'button';

    // Insert select all button before the invitation dropdown
    const invitationDropdown = document.querySelector('.btn-group[role="group"]');
    if (invitationDropdown) {
        invitationDropdown.parentNode.insertBefore(selectAllBtn, invitationDropdown);
    }

    let allSelected = false;
    selectAllBtn.addEventListener('click', function() {
        allSelected = !allSelected;

        scholarCheckboxes.forEach(checkbox => {
            checkbox.checked = allSelected;
        });

        if (allSelected) {
            this.innerHTML = '<i class="fas fa-square"></i> إلغاء التحديد';
            this.classList.remove('btn-outline-secondary');
            this.classList.add('btn-secondary');
        } else {
            this.innerHTML = '<i class="fas fa-check-square"></i> تحديد الكل';
            this.classList.remove('btn-secondary');
            this.classList.add('btn-outline-secondary');
        }

        updateSelectedCount();
    });

    // Initial count update
    updateSelectedCount();

    // معالجة أزرار البريد الإلكتروني
    $('.send-email-btn').on('click', function() {
        const scholarId = $(this).data('scholar-id');
        const scholarName = $(this).data('scholar-name');
        const scholarEmail = $(this).data('scholar-email');

        $('#email-scholar-id').val(scholarId);
        $('#email-scholar-name').text(scholarName);
        $('#email-scholar-email').text(scholarEmail);
    });

    // إرسال دعوات بريد إلكتروني للمحددين
    $('#send-selected-email-invitations').on('click', function(e) {
        e.preventDefault();
        console.log('Send selected email invitations clicked');

        const selectedScholars = $('.scholar-checkbox:checked');
        console.log('Selected scholars:', selectedScholars.length);

        if (selectedScholars.length === 0) {
            alert('يرجى تحديد عالم واحد على الأقل');
            return;
        }

        const scholarIds = selectedScholars.map(function() {
            return this.value;
        }).get();

        console.log('Scholar IDs:', scholarIds);

        // فتح نموذج الدعوات الجماعية
        $('#bulk-email-scholar-ids').val(scholarIds.join(','));
        $('#bulk-email-count').text(scholarIds.length);
        $('#bulkEmailInvitationModal').modal('show');
    });

    // إرسال دعوات جماعية
    $('#send-bulk-email-invitations').on('click', function(e) {
        e.preventDefault();
        console.log('Send bulk email invitations clicked');

        // مسح قيمة scholar_ids لإرسال لجميع العلماء
        $('#bulk-email-scholar-ids').val('');
        $('#bulk-email-count').text('جميع');
        $('#bulkEmailInvitationModal').modal('show');
    });
});
</script>

<!-- Email Invitation Modal -->
<div class="modal fade" id="emailInvitationModal" tabindex="-1" aria-labelledby="emailInvitationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="emailInvitationModalLabel">
                    <i class="fas fa-envelope me-2"></i>إرسال دعوة بريد إلكتروني
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'organizations:send_scholar_email_invitation' %}">
                {% csrf_token %}
                <input type="hidden" id="email-scholar-id" name="scholar_id">
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        سيتم إرسال دعوة بريد إلكتروني إلى العالم: <strong id="email-scholar-name"></strong>
                        <br>
                        البريد الإلكتروني: <strong id="email-scholar-email"></strong>
                    </div>

                    <div class="mb-3">
                        <label for="email-subject" class="form-label">موضوع الرسالة</label>
                        <input type="text" class="form-control" id="email-subject" name="subject"
                               value="دعوة للمشاركة في مؤتمر السيرة النبوية السنوي" required>
                    </div>

                    <div class="mb-3">
                        <label for="email-message" class="form-label">نص الرسالة</label>
                        <textarea class="form-control" id="email-message" name="message" rows="6" required>السلام عليكم ورحمة الله وبركاته

يسرنا دعوتكم للمشاركة في مؤتمر السيرة النبوية السنوي الذي سيقام قريباً.

نتطلع لمشاركتكم الكريمة وإثراء المؤتمر بعلمكم وخبرتكم.

للمزيد من المعلومات والتسجيل، يرجى زيارة موقعنا الإلكتروني.

وفقكم الله وبارك فيكم</textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane me-2"></i>إرسال الدعوة
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Bulk Email Invitation Modal -->
<div class="modal fade" id="bulkEmailInvitationModal" tabindex="-1" aria-labelledby="bulkEmailInvitationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bulkEmailInvitationModalLabel">
                    <i class="fas fa-envelope me-2"></i>إرسال دعوات جماعية بالبريد الإلكتروني
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'organizations:send_bulk_scholar_email_invitation' %}">
                {% csrf_token %}
                <input type="hidden" id="bulk-email-scholar-ids" name="scholar_ids">
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        سيتم إرسال دعوات بريد إلكتروني إلى <strong id="bulk-email-count">جميع</strong> العلماء المحددين
                    </div>

                    <div class="mb-3">
                        <label for="bulk-email-subject" class="form-label">موضوع الرسالة</label>
                        <input type="text" class="form-control" id="bulk-email-subject" name="subject"
                               value="دعوة للمشاركة في مؤتمر السيرة النبوية السنوي" required>
                    </div>

                    <div class="mb-3">
                        <label for="bulk-email-message" class="form-label">نص الرسالة</label>
                        <textarea class="form-control" id="bulk-email-message" name="message" rows="6" required>السلام عليكم ورحمة الله وبركاته

يسرنا دعوتكم للمشاركة في مؤتمر السيرة النبوية السنوي الذي سيقام قريباً.

نتطلع لمشاركتكم الكريمة وإثراء المؤتمر بعلمكم وخبرتكم.

للمزيد من المعلومات والتسجيل، يرجى زيارة موقعنا الإلكتروني.

وفقكم الله وبارك فيكم</textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-paper-plane me-2"></i>إرسال الدعوات
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

{% endblock %}