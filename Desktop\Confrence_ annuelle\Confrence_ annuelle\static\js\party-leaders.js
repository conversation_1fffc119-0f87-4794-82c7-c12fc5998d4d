// JavaScript لصفحة رؤساء الأحزاب

document.addEventListener('DOMContentLoaded', function() {
    // تفعيل التأثيرات المتحركة
    initAnimations();
    
    // تفعيل البحث المباشر
    initLiveSearch();
    
    // تفعيل تأثيرات الكروت
    initCardEffects();
    
    // تفعيل العد التصاعدي للإحصائيات
    initCounterAnimation();
});

// تفعيل التأثيرات المتحركة
function initAnimations() {
    // تأثير الظهور التدريجي للكروت
    const cards = document.querySelectorAll('.party-leader-card');
    
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in');
                observer.unobserve(entry.target);
            }
        });
    }, observerOptions);
    
    cards.forEach((card, index) => {
        // إضافة تأخير متدرج
        card.style.animationDelay = `${index * 0.1}s`;
        observer.observe(card);
    });
}

// تفعيل البحث المباشر
function initLiveSearch() {
    const searchInput = document.querySelector('.search-input');
    const cards = document.querySelectorAll('.party-leader-card');
    const noResultsContainer = document.querySelector('.no-results-container');
    
    if (!searchInput) return;
    
    let searchTimeout;
    
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        
        searchTimeout = setTimeout(() => {
            const searchTerm = this.value.toLowerCase().trim();
            let visibleCount = 0;
            
            cards.forEach(card => {
                const name = card.querySelector('.leader-name')?.textContent.toLowerCase() || '';
                const partyName = card.querySelector('.party-name')?.textContent.toLowerCase() || '';
                const ideology = card.querySelector('.contact-item:last-child')?.textContent.toLowerCase() || '';
                
                const isVisible = name.includes(searchTerm) || 
                                partyName.includes(searchTerm) || 
                                ideology.includes(searchTerm);
                
                if (isVisible) {
                    card.style.display = 'block';
                    card.classList.add('fade-in');
                    visibleCount++;
                } else {
                    card.style.display = 'none';
                    card.classList.remove('fade-in');
                }
            });
            
            // إظهار/إخفاء رسالة عدم وجود نتائج
            if (noResultsContainer) {
                noResultsContainer.style.display = visibleCount === 0 && searchTerm ? 'block' : 'none';
            }
            
            // تحديث عداد النتائج
            updateResultsCounter(visibleCount);
            
        }, 300);
    });
}

// تفعيل تأثيرات الكروت
function initCardEffects() {
    const cards = document.querySelectorAll('.party-leader-card');
    
    cards.forEach(card => {
        // تأثير التمرير
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
            this.style.zIndex = '10';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
            this.style.zIndex = '1';
        });
        
        // تأثير النقر
        card.addEventListener('mousedown', function() {
            this.style.transform = 'translateY(-8px) scale(0.98)';
        });
        
        card.addEventListener('mouseup', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
        });
    });
}

// تفعيل العد التصاعدي للإحصائيات
function initCounterAnimation() {
    const counters = document.querySelectorAll('.stat-number');
    
    const animateCounter = (counter) => {
        const target = parseInt(counter.textContent);
        const duration = 2000; // 2 ثانية
        const step = target / (duration / 16); // 60 FPS
        let current = 0;
        
        const timer = setInterval(() => {
            current += step;
            if (current >= target) {
                counter.textContent = target;
                clearInterval(timer);
            } else {
                counter.textContent = Math.floor(current);
            }
        }, 16);
    };
    
    // مراقب للتفعيل عند الوصول للإحصائيات
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                observer.unobserve(entry.target);
            }
        });
    }, { threshold: 0.5 });
    
    counters.forEach(counter => {
        observer.observe(counter);
    });
}

// تحديث عداد النتائج
function updateResultsCounter(count) {
    const resultCounter = document.querySelector('.results-counter');
    if (resultCounter) {
        if (count === 0) {
            resultCounter.textContent = 'لا توجد نتائج';
        } else if (count === 1) {
            resultCounter.textContent = 'نتيجة واحدة';
        } else {
            resultCounter.textContent = `${count} نتائج`;
        }
    }
}

// تأثيرات إضافية للبحث
function highlightSearchTerm(text, term) {
    if (!term) return text;
    
    const regex = new RegExp(`(${term})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
}

// تفعيل التمرير السلس
function smoothScrollTo(element) {
    element.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
    });
}

// تأثير الجسيمات في الخلفية
function initParticleEffect() {
    const hero = document.querySelector('.hero-section');
    if (!hero) return;
    
    for (let i = 0; i < 50; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        particle.style.cssText = `
            position: absolute;
            width: 2px;
            height: 2px;
            background: rgba(255, 255, 255, 0.5);
            border-radius: 50%;
            pointer-events: none;
            left: ${Math.random() * 100}%;
            top: ${Math.random() * 100}%;
            animation: float ${3 + Math.random() * 4}s infinite ease-in-out;
            animation-delay: ${Math.random() * 2}s;
        `;
        hero.appendChild(particle);
    }
}

// تفعيل تأثير الكتابة
function typeWriter(element, text, speed = 100) {
    let i = 0;
    element.textContent = '';
    
    function type() {
        if (i < text.length) {
            element.textContent += text.charAt(i);
            i++;
            setTimeout(type, speed);
        }
    }
    
    type();
}

// تفعيل الاختصارات لوحة المفاتيح
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + K للبحث
    if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
        e.preventDefault();
        const searchInput = document.querySelector('.search-input');
        if (searchInput) {
            searchInput.focus();
        }
    }
    
    // Escape لمسح البحث
    if (e.key === 'Escape') {
        const searchInput = document.querySelector('.search-input');
        if (searchInput && searchInput.value) {
            searchInput.value = '';
            searchInput.dispatchEvent(new Event('input'));
        }
    }
});

// تحسين الأداء - تأخير تحميل الصور
function initLazyLoading() {
    const images = document.querySelectorAll('img[data-src]');
    
    const imageObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('lazy');
                imageObserver.unobserve(img);
            }
        });
    });
    
    images.forEach(img => imageObserver.observe(img));
}

// تصدير الوظائف للاستخدام الخارجي
window.PartyLeadersJS = {
    initAnimations,
    initLiveSearch,
    initCardEffects,
    initCounterAnimation,
    highlightSearchTerm,
    smoothScrollTo
};
