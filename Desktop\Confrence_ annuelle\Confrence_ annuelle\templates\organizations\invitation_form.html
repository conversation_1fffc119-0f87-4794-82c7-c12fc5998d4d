{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}إرسال دعوة{% endblock %}

{% block extra_css %}
<style>
    .hero-mini {
        background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
        color: white;
        padding: 3rem 0;
        text-align: center;
        position: relative;
        overflow: hidden;
        margin-bottom: 2rem;
    }
    
    .hero-mini::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url('{% static "img/pattern.png" %}');
        opacity: 0.1;
    }
    
    .hero-mini .container {
        position: relative;
        z-index: 1;
    }
    
    .form-card {
        border: none;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
        border-radius: 0.5rem;
        overflow: hidden;
        margin-top: -1rem;
    }
    
    .form-card .card-header {
        background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
        padding: 1.25rem 1.5rem;
    }
    
    .form-card .card-body {
        padding: 2rem;
    }
    
    .form-label {
        font-weight: 600;
        color: #333;
    }
    
    .form-control, .form-select {
        padding: 0.75rem 1rem;
        border-radius: 0.5rem;
        border: 1px solid #dee2e6;
        transition: all 0.3s ease;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #3949ab;
        box-shadow: 0 0 0 0.25rem rgba(57, 73, 171, 0.25);
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
        border: none;
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-primary:hover {
        background: linear-gradient(135deg, #15196d 0%, #303f9f 100%);
        transform: translateY(-2px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }
    
    .btn-secondary {
        background: #f8f9fa;
        color: #333;
        border: 1px solid #dee2e6;
        padding: 0.75rem 1.5rem;
        border-radius: 0.5rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .btn-secondary:hover {
        background: #e9ecef;
        transform: translateY(-2px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="hero-mini text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-md-8 mx-auto text-center">
                <h1 class="display-5 fw-bold mb-3">إرسال دعوة</h1>
                <p class="lead">دعوة المؤسسات للمشاركة في المؤتمر السنوي</p>
            </div>
        </div>
    </div>
</div>
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card form-card">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0"><i class="fas fa-paper-plane me-2"></i>إرسال دعوة</h4>
            </div>
            <div class="card-body">
                <form method="post" class="needs-validation" novalidate>
                    {% csrf_token %}
                    
                    <div class="mb-4">
                        <label for="id_organization" class="form-label"><i class="fas fa-building text-primary me-2"></i>المؤسسة *</label>
                        {{ form.organization.errors }}
                        <select name="organization" id="id_organization" class="form-select form-select-lg {% if form.organization.errors %}is-invalid{% endif %}" required>
                            <option value="">-- اختر مؤسسة --</option>
                            {% for org in form.fields.organization.queryset %}
                            <option value="{{ org.id }}" {% if form.organization.value == org.id|stringformat:'i' %}selected{% endif %}>{{ org.name }}</option>
                            {% endfor %}
                        </select>
                        <div class="invalid-feedback">
                            يرجى اختيار مؤسسة.
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label for="id_subject" class="form-label"><i class="fas fa-heading text-primary me-2"></i>الموضوع *</label>
                        {{ form.subject.errors }}
                        <input type="text" name="subject" id="id_subject" class="form-control form-control-lg {% if form.subject.errors %}is-invalid{% endif %}" value="{{ form.subject.value|default:'' }}" placeholder="أدخل موضوع الدعوة" required>
                        <div class="invalid-feedback">
                            يرجى إدخال موضوع الدعوة.
                        </div>
                    </div>
                    
                    <div class="mb-4">
                        <label for="id_message" class="form-label"><i class="fas fa-envelope-open-text text-primary me-2"></i>محتوى الرسالة *</label>
                        {{ form.message.errors }}
                        <textarea name="message" id="id_message" rows="8" class="form-control form-control-lg {% if form.message.errors %}is-invalid{% endif %}" placeholder="أدخل محتوى رسالة الدعوة هنا..." required>{{ form.message.value|default:'' }}</textarea>
                        <div class="form-text text-muted">
                            يمكنك استخدام علامات HTML الأساسية مثل <code>&lt;b&gt;</code> للنص العريض و <code>&lt;i&gt;</code> للنص المائل.
                        </div>
                        <div class="invalid-feedback">
                            يرجى إدخال محتوى الرسالة.
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end mt-5">
                        <a href="{% url 'organizations:organization_list' %}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times me-2"></i>إلغاء
                        </a>
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="fas fa-paper-plane me-2"></i>إرسال الدعوة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}