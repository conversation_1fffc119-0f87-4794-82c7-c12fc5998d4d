{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .hero-mini {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        position: relative;
        overflow: hidden;
    }

    .hero-mini::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('{% static "img/pattern.png" %}') repeat;
        opacity: 0.1;
    }

    .confirmation-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        border: none;
        overflow: hidden;
        margin-top: -50px;
        position: relative;
        z-index: 2;
    }

    .confirmation-card .card-header {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        padding: 2rem;
        text-align: center;
    }

    .confirmation-card .card-body {
        padding: 3rem;
    }

    .scholar-info {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid #28a745;
    }

    .scholar-name {
        font-size: 1.5rem;
        font-weight: 700;
        color: #155724;
        margin-bottom: 0.5rem;
    }

    .scholar-details {
        color: #6c757d;
        margin-bottom: 0.25rem;
    }

    .invitation-details {
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
        border-radius: 15px;
        padding: 2rem;
        margin-bottom: 2rem;
        border: 2px solid #ffc107;
    }

    .invitation-subject {
        font-size: 1.25rem;
        font-weight: 600;
        color: #856404;
        margin-bottom: 1rem;
    }

    .invitation-message {
        background: white;
        border-radius: 10px;
        padding: 1.5rem;
        border: 1px solid #ffc107;
        white-space: pre-wrap;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        line-height: 1.6;
        color: #495057;
    }

    .whatsapp-section {
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
        border-radius: 15px;
        padding: 2rem;
        text-align: center;
        border: 2px solid #28a745;
    }

    .whatsapp-btn {
        background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
        border: none;
        border-radius: 50px;
        padding: 1rem 3rem;
        font-size: 1.25rem;
        font-weight: 600;
        color: white;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
        box-shadow: 0 10px 20px rgba(37, 211, 102, 0.3);
    }

    .whatsapp-btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 15px 30px rgba(37, 211, 102, 0.4);
        color: white;
        text-decoration: none;
    }

    .whatsapp-btn i {
        font-size: 1.5rem;
        margin-right: 0.75rem;
    }

    .action-buttons {
        text-align: center;
        margin-top: 2rem;
    }

    .btn-secondary {
        background: #6c757d;
        border: none;
        border-radius: 12px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-secondary:hover {
        background: #5a6268;
        transform: translateY(-2px);
    }

    .back-link {
        color: white;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .back-link:hover {
        color: #f8f9fa;
        text-decoration: none;
    }

    .success-icon {
        font-size: 4rem;
        color: #28a745;
        margin-bottom: 1rem;
    }

    .phone-info {
        background: #e7f3ff;
        border-radius: 10px;
        padding: 1rem;
        margin-top: 1rem;
        border-left: 4px solid #007bff;
    }

    .copy-btn {
        background: #007bff;
        border: none;
        border-radius: 8px;
        padding: 0.5rem 1rem;
        color: white;
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .copy-btn:hover {
        background: #0056b3;
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="hero-mini text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-md-8 mx-auto text-center">
                <h1 class="display-5 fw-bold mb-3">{{ title }}</h1>
                <p class="lead">دعوة جاهزة للإرسال عبر WhatsApp</p>
                <a href="{% url 'organizations:scholar_list' %}" class="back-link">
                    <i class="fas fa-arrow-right me-2"></i> العودة إلى قائمة العلماء
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card confirmation-card">
                <div class="card-header">
                    <div class="success-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <h4 class="mb-0 text-white">تم إعداد الدعوة بنجاح!</h4>
                </div>
                <div class="card-body">
                    <!-- Scholar Information -->
                    <div class="scholar-info">
                        <h5 class="mb-3">
                            <i class="fas fa-user-graduate me-2"></i>معلومات العالم
                        </h5>
                        <div class="scholar-name">{{ invitation.scholar.get_full_title_name }}</div>
                        {% if invitation.scholar.position %}
                            <div class="scholar-details">
                                <i class="fas fa-briefcase me-1"></i> {{ invitation.scholar.position }}
                            </div>
                        {% endif %}
                        {% if invitation.scholar.organization %}
                            <div class="scholar-details">
                                <i class="fas fa-building me-1"></i> {{ invitation.scholar.organization }}
                            </div>
                        {% endif %}
                        {% if invitation.scholar.country %}
                            <div class="scholar-details">
                                <i class="fas fa-map-marker-alt me-1"></i> {{ invitation.scholar.country }}
                                {% if invitation.scholar.city %}, {{ invitation.scholar.city }}{% endif %}
                            </div>
                        {% endif %}
                        
                        <div class="phone-info">
                            <strong>رقم الهاتف:</strong> {{ invitation.phone_number }}
                            <button class="copy-btn ms-2" onclick="copyToClipboard('{{ invitation.phone_number }}')">
                                <i class="fas fa-copy"></i> نسخ
                            </button>
                        </div>
                    </div>

                    <!-- Invitation Details -->
                    <div class="invitation-details">
                        <h5 class="mb-3">
                            <i class="fas fa-envelope me-2"></i>تفاصيل الدعوة
                        </h5>
                        <div class="invitation-subject">{{ invitation.subject }}</div>
                        <div class="invitation-message">{{ invitation.message }}</div>
                    </div>

                    <!-- WhatsApp Section -->
                    <div class="whatsapp-section">
                        <h5 class="mb-3">
                            <i class="fab fa-whatsapp me-2"></i>إرسال عبر WhatsApp
                        </h5>
                        <p class="mb-4">انقر على الزر أدناه لفتح WhatsApp وإرسال الدعوة مباشرة</p>
                        
                        <a href="{{ whatsapp_url }}" target="_blank" class="whatsapp-btn">
                            <i class="fab fa-whatsapp"></i>
                            إرسال عبر WhatsApp
                        </a>
                        
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                سيتم فتح WhatsApp في نافذة جديدة مع الرسالة جاهزة للإرسال
                            </small>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="action-buttons">
                        <a href="{% url 'organizations:scholar_list' %}" class="btn btn-secondary btn-lg me-3">
                            <i class="fas fa-list me-2"></i>العودة إلى القائمة
                        </a>
                        <a href="{% url 'organizations:send_scholar_invitation' %}" class="btn btn-outline-primary btn-lg">
                            <i class="fas fa-plus me-2"></i>إرسال دعوة أخرى
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(function() {
        // Show success message
        const btn = event.target.closest('.copy-btn');
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-check"></i> تم النسخ';
        btn.style.background = '#28a745';
        
        setTimeout(function() {
            btn.innerHTML = originalText;
            btn.style.background = '#007bff';
        }, 2000);
    }).catch(function(err) {
        console.error('فشل في نسخ النص: ', err);
        alert('فشل في نسخ النص');
    });
}

// Track WhatsApp link clicks
document.querySelector('.whatsapp-btn').addEventListener('click', function() {
    // You can add analytics tracking here
    console.log('WhatsApp invitation sent for scholar:', '{{ invitation.scholar.get_full_title_name }}');
});
</script>
{% endblock %}
