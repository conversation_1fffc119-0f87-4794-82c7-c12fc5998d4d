{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .invitation-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        transition: transform 0.3s ease;
        margin-bottom: 20px;
    }
    
    .invitation-card:hover {
        transform: translateY(-5px);
    }
    
    .whatsapp-btn {
        background: linear-gradient(135deg, #25d366 0%, #128c7e 100%);
        border: none;
        color: white;
        padding: 10px 20px;
        border-radius: 25px;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
    }
    
    .whatsapp-btn:hover {
        transform: scale(1.05);
        color: white;
        text-decoration: none;
    }
    
    .phone-number {
        font-family: 'Courier New', monospace;
        background: #f8f9fa;
        padding: 5px 10px;
        border-radius: 5px;
        font-weight: bold;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="text-center mb-4">
                <h1 class="display-4 mb-3">{{ title }}</h1>
                <p class="lead text-muted">اضغط على الأزرار أدناه لإرسال الدعوات عبر WhatsApp</p>
            </div>
        </div>
    </div>

    <div class="row">
        {% for invitation in invitation_links %}
        <div class="col-md-6 col-lg-4">
            <div class="card invitation-card">
                <div class="card-body text-center">
                    <div class="mb-3">
                        <i class="fas fa-user fa-2x text-primary mb-2"></i>
                        <h5 class="card-title">{{ invitation.name }}</h5>
                    </div>
                    
                    <div class="mb-3">
                        <small class="text-muted">رقم الهاتف:</small>
                        <div class="phone-number">{{ invitation.phone }}</div>
                    </div>
                    
                    <a href="{{ invitation.url }}" target="_blank" class="whatsapp-btn">
                        <i class="fab fa-whatsapp me-2"></i>إرسال عبر WhatsApp
                    </a>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>

    <div class="row mt-4">
        <div class="col-12 text-center">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">تعليمات الإرسال</h5>
                    <p class="card-text">
                        اضغط على أزرار "إرسال عبر WhatsApp" أعلاه لفتح محادثة WhatsApp مع كل منتخب.
                        <br>ستجد الرسالة جاهزة للإرسال في تطبيق WhatsApp.
                    </p>
                    <a href="{% url 'organizations:elected_officials_list' %}" class="btn btn-primary">
                        <i class="fas fa-arrow-right me-2"></i>العودة لقائمة المنتخبين
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تأثيرات بصرية للبطاقات
    const cards = document.querySelectorAll('.invitation-card');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.boxShadow = '0 10px 25px rgba(0,0,0,0.15)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.boxShadow = '0 5px 15px rgba(0,0,0,0.1)';
        });
    });
});
</script>
{% endblock %}
