from django.db import models
from django.utils.translation import gettext_lazy as _
from django.contrib.auth.models import User, Group
from django.db.models.signals import post_save
from django.dispatch import receiver

class Conference(models.Model):
    """Conference model"""
    title = models.CharField(_('Title'), max_length=255)
    description = models.TextField(_('Description'), blank=True)
    logo = models.ImageField(_('Logo'), upload_to='conferences/logos/', blank=True, null=True)
    banner = models.ImageField(_('Banner'), upload_to='conferences/banners/', blank=True, null=True)
    website = models.URLField(_('Website'), blank=True)
    email = models.EmailField(_('Email'), blank=True)
    phone = models.CharField(_('Phone'), max_length=20, blank=True)
    address = models.TextField(_('Address'), blank=True)
    is_active = models.BooleanField(_('Is active'), default=True)
    created_at = models.DateTimeField(_('Created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated at'), auto_now=True)
    
    class Meta:
        verbose_name = _('Conference')
        verbose_name_plural = _('Conferences')
    
    def __str__(self):
        return self.title

class ConferenceEdition(models.Model):
    """Conference edition model"""
    conference = models.ForeignKey(Conference, on_delete=models.CASCADE, related_name='editions')
    year = models.PositiveIntegerField(_('Year'))
    theme = models.CharField(_('Theme'), max_length=255, blank=True)
    start_date = models.DateField(_('Start date'))
    end_date = models.DateField(_('End date'))
    location = models.CharField(_('Location'), max_length=255, blank=True)
    description = models.TextField(_('Description'), blank=True)
    banner = models.ImageField(_('Banner'), upload_to='editions/banners/', blank=True, null=True)
    is_active = models.BooleanField(_('Is active'), default=True)
    created_at = models.DateTimeField(_('Created at'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated at'), auto_now=True)
    
    class Meta:
        verbose_name = _('Conference edition')
        verbose_name_plural = _('Conference editions')
        ordering = ['-year']
    
    def __str__(self):
        return f"{self.conference.title} {self.year}"

class Notification(models.Model):
    """Notification model"""
    NOTIFICATION_TYPES = (
        ('invitation_sent', _('Invitation Sent')),
        ('invitation_cancelled', _('Invitation Cancelled')),
        ('invitation_responded', _('Invitation Responded')),
        ('system', _('System Notification')),
    )
    
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='notifications')
    title = models.CharField(_('Title'), max_length=255)
    message = models.TextField(_('Message'))
    notification_type = models.CharField(_('Type'), max_length=50, choices=NOTIFICATION_TYPES, default='system')
    is_read = models.BooleanField(_('Is read'), default=False)
    related_object_id = models.PositiveIntegerField(_('Related object ID'), blank=True, null=True)
    related_object_type = models.CharField(_('Related object type'), max_length=100, blank=True, null=True)
    created_at = models.DateTimeField(_('Created at'), auto_now_add=True)
    
    class Meta:
        verbose_name = _('Notification')
        verbose_name_plural = _('Notifications')
        ordering = ['-created_at']
    
    def __str__(self):
        return self.title
    
    def mark_as_read(self):
        """Mark notification as read"""
        self.is_read = True
        self.save()
    
    class Meta:
        verbose_name = _('Notification')
        verbose_name_plural = _('Notifications')
        ordering = ['-created_at']
    
    def __str__(self):
        return self.title
    
    def mark_as_read(self):
        self.is_read = True
        self.save()


class AdminProfile(models.Model):
    """نموذج ملف المسؤول الإداري"""

    ADMIN_TYPE_CHOICES = (
        ('super_admin', _('مسؤول كبير')),
        ('regular_admin', _('مسؤول عادي')),
    )

    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='admin_profile')
    admin_type = models.CharField(_('نوع المسؤول'), max_length=20, choices=ADMIN_TYPE_CHOICES, default='regular_admin')
    full_name = models.CharField(_('الاسم الكامل'), max_length=255, blank=True)
    phone = models.CharField(_('رقم الهاتف'), max_length=20, blank=True)
    department = models.CharField(_('القسم'), max_length=100, blank=True)
    profile_image = models.ImageField(_('الصورة الشخصية'), upload_to='admin_profiles/', blank=True, null=True, help_text=_('صورة شخصية للمسؤول'))
    notes = models.TextField(_('ملاحظات'), blank=True)
    is_active = models.BooleanField(_('نشط'), default=True)
    created_at = models.DateTimeField(_('تاريخ الإنشاء'), auto_now_add=True)
    updated_at = models.DateTimeField(_('تاريخ التحديث'), auto_now=True)

    class Meta:
        verbose_name = _('ملف المسؤول')
        verbose_name_plural = _('ملفات المسؤولين')
        ordering = ['user__username']

    def __str__(self):
        return f"{self.user.username} - {self.get_admin_type_display()}"

    def is_super_admin(self):
        """التحقق من كون المسؤول مسؤولاً كبيراً"""
        return self.admin_type == 'super_admin'

    def is_regular_admin(self):
        """التحقق من كون المسؤول مسؤولاً عادياً"""
        return self.admin_type == 'regular_admin'

    def get_display_name(self):
        """الحصول على الاسم المعروض"""
        return self.full_name if self.full_name else self.user.username

    def get_profile_image_url(self):
        """الحصول على رابط الصورة الشخصية أو الصورة الافتراضية"""
        if self.profile_image and hasattr(self.profile_image, 'url'):
            return self.profile_image.url
        else:
            # إرجاع صورة افتراضية حسب نوع المسؤول
            if self.is_super_admin():
                return '/static/images/default-super-admin.png'
            else:
                return '/static/images/default-admin.png'

    def get_initials(self):
        """الحصول على الأحرف الأولى من الاسم"""
        name = self.get_display_name()
        words = name.split()
        if len(words) >= 2:
            return f"{words[0][0]}{words[1][0]}".upper()
        elif len(words) == 1:
            return words[0][:2].upper()
        else:
            return "AD"


@receiver(post_save, sender=User)
def create_admin_profile(sender, instance, created, **kwargs):
    """إنشاء ملف مسؤول تلقائياً عند إنشاء مستخدم جديد من المديرين"""
    if created and instance.is_staff:
        AdminProfile.objects.create(user=instance)


@receiver(post_save, sender=User)
def save_admin_profile(sender, instance, **kwargs):
    """حفظ ملف المسؤول عند حفظ المستخدم"""
    if instance.is_staff and hasattr(instance, 'admin_profile'):
        instance.admin_profile.save()
