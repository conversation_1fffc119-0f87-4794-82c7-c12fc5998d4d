{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .scholar-header {
        background: linear-gradient(135deg, #007bff, #0056b3);
        color: white;
        padding: 30px;
        border-radius: 10px;
        margin-bottom: 30px;
    }
    
    .scholar-photo-large {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        object-fit: cover;
        border: 4px solid white;
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }
    
    .scholar-title-large {
        font-size: 1.2em;
        font-weight: bold;
        margin-bottom: 10px;
    }
    
    .scholar-name-large {
        font-size: 2em;
        font-weight: bold;
        margin-bottom: 10px;
    }
    
    .scholar-position-large {
        font-size: 1.1em;
        opacity: 0.9;
        margin-bottom: 15px;
    }
    
    .info-card {
        background: white;
        border-radius: 10px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border-left: 4px solid #007bff;
    }
    
    .info-card h5 {
        color: #007bff;
        margin-bottom: 20px;
        font-weight: bold;
    }
    
    .info-item {
        margin-bottom: 15px;
        padding-bottom: 15px;
        border-bottom: 1px solid #eee;
    }
    
    .info-item:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
    }
    
    .info-label {
        font-weight: bold;
        color: #666;
        margin-bottom: 5px;
    }
    
    .info-value {
        color: #333;
    }
    
    .status-badge-large {
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 1em;
        font-weight: bold;
        display: inline-block;
    }
    
    .status-invited { background-color: #fff3cd; color: #856404; }
    .status-confirmed { background-color: #d4edda; color: #155724; }
    .status-declined { background-color: #f8d7da; color: #721c24; }
    .status-attended { background-color: #d1ecf1; color: #0c5460; }
    
    .action-buttons {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 20px;
    }
    
    .biography-section {
        background: #f8f9fa;
        padding: 25px;
        border-radius: 10px;
        margin-top: 20px;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- Scholar Header -->
            <div class="scholar-header">
                <div class="row align-items-center">
                    <div class="col-md-2 text-center">
                        {% if scholar.photo %}
                            <img src="{{ scholar.photo.url }}" alt="{{ scholar.name }}" class="scholar-photo-large">
                        {% else %}
                            <div class="scholar-photo-large d-flex align-items-center justify-content-center bg-light text-primary mx-auto">
                                <i class="fas fa-user fa-3x"></i>
                            </div>
                        {% endif %}
                    </div>
                    <div class="col-md-8">
                        <div class="scholar-title-large">{{ scholar.get_title_display }}</div>
                        <div class="scholar-name-large">{{ scholar.name }}</div>
                        {% if scholar.position %}
                            <div class="scholar-position-large">{{ scholar.position }}</div>
                        {% endif %}
                        <div class="mt-3">
                            <span class="status-badge-large status-{{ scholar.participation_status }}">
                                {{ scholar.get_participation_status_display }}
                            </span>
                        </div>
                    </div>
                    <div class="col-md-2 text-end">
                        <div class="action-buttons bg-transparent p-0">
                            <a href="{% url 'organizations:scholar_update' scholar.pk %}" 
                               class="btn btn-light btn-lg mb-2 d-block">
                                <i class="fas fa-edit"></i> تعديل البيانات
                            </a>
                            <a href="{% url 'organizations:scholar_list' %}" 
                               class="btn btn-outline-light btn-lg d-block">
                                <i class="fas fa-arrow-right"></i> العودة للقائمة
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <!-- Basic Information -->
                <div class="col-md-6">
                    <div class="info-card">
                        <h5><i class="fas fa-user"></i> المعلومات الأساسية</h5>
                        
                        <div class="info-item">
                            <div class="info-label">اللقب</div>
                            <div class="info-value">{{ scholar.get_title_display }}</div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-label">الاسم</div>
                            <div class="info-value">{{ scholar.name }}</div>
                        </div>
                        
                        {% if scholar.full_name %}
                        <div class="info-item">
                            <div class="info-label">الاسم الكامل</div>
                            <div class="info-value">{{ scholar.full_name }}</div>
                        </div>
                        {% endif %}
                        
                        {% if scholar.position %}
                        <div class="info-item">
                            <div class="info-label">المنصب/الوظيفة</div>
                            <div class="info-value">{{ scholar.position }}</div>
                        </div>
                        {% endif %}
                        
                        {% if scholar.specialization %}
                        <div class="info-item">
                            <div class="info-label">التخصص</div>
                            <div class="info-value">{{ scholar.specialization }}</div>
                        </div>
                        {% endif %}
                    </div>
                </div>

                <!-- Professional Information -->
                <div class="col-md-6">
                    <div class="info-card">
                        <h5><i class="fas fa-briefcase"></i> المعلومات المهنية</h5>
                        
                        {% if scholar.organization %}
                        <div class="info-item">
                            <div class="info-label">المؤسسة/الجامعة</div>
                            <div class="info-value">{{ scholar.organization }}</div>
                        </div>
                        {% endif %}
                        
                        {% if scholar.country %}
                        <div class="info-item">
                            <div class="info-label">البلد</div>
                            <div class="info-value">
                                <i class="fas fa-map-marker-alt text-muted"></i> {{ scholar.country }}
                                {% if scholar.city %}, {{ scholar.city }}{% endif %}
                            </div>
                        </div>
                        {% endif %}
                        
                        <div class="info-item">
                            <div class="info-label">حالة المشاركة</div>
                            <div class="info-value">
                                <span class="status-badge-large status-{{ scholar.participation_status }}">
                                    {{ scholar.get_participation_status_display }}
                                </span>
                            </div>
                        </div>
                        
                        <div class="info-item">
                            <div class="info-label">تاريخ الإضافة</div>
                            <div class="info-value">{{ scholar.created_at|date:"d/m/Y H:i" }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="row">
                <div class="col-md-12">
                    <div class="info-card">
                        <h5><i class="fas fa-address-book"></i> معلومات الاتصال</h5>
                        
                        <div class="row">
                            {% if scholar.email %}
                            <div class="col-md-4">
                                <div class="info-item">
                                    <div class="info-label">البريد الإلكتروني</div>
                                    <div class="info-value">
                                        <a href="mailto:{{ scholar.email }}" class="text-decoration-none">
                                            <i class="fas fa-envelope text-primary"></i> {{ scholar.email }}
                                        </a>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                            
                            {% if scholar.phone %}
                            <div class="col-md-4">
                                <div class="info-item">
                                    <div class="info-label">رقم الهاتف</div>
                                    <div class="info-value">
                                        <a href="tel:{{ scholar.phone }}" class="text-decoration-none">
                                            <i class="fas fa-phone text-success"></i> {{ scholar.phone }}
                                        </a>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                            
                            {% if scholar.website %}
                            <div class="col-md-4">
                                <div class="info-item">
                                    <div class="info-label">الموقع الإلكتروني</div>
                                    <div class="info-value">
                                        <a href="{{ scholar.website }}" target="_blank" class="text-decoration-none">
                                            <i class="fas fa-globe text-info"></i> زيارة الموقع
                                        </a>
                                    </div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Biography -->
            {% if scholar.biography %}
            <div class="row">
                <div class="col-md-12">
                    <div class="biography-section">
                        <h5><i class="fas fa-book-open text-primary"></i> السيرة الذاتية</h5>
                        <div class="mt-3">
                            {{ scholar.biography|linebreaks }}
                        </div>
                    </div>
                </div>
            </div>
            {% endif %}

            <!-- Action Buttons -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="action-buttons text-center">
                        <a href="{% url 'organizations:scholar_update' scholar.pk %}" 
                           class="btn btn-warning btn-lg me-3">
                            <i class="fas fa-edit"></i> تعديل البيانات
                        </a>
                        <a href="{% url 'organizations:scholar_list' %}" 
                           class="btn btn-secondary btn-lg me-3">
                            <i class="fas fa-list"></i> العودة للقائمة
                        </a>
                        <a href="{% url 'organizations:scholar_delete' scholar.pk %}" 
                           class="btn btn-danger btn-lg"
                           onclick="return confirm('هل أنت متأكد من حذف هذا العالم؟')">
                            <i class="fas fa-trash"></i> حذف العالم
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
