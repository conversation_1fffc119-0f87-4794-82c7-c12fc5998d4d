{% load i18n %}

<fieldset class="fieldset{% if fieldset.classes %} {{ fieldset.classes }}{% endif %}">
    {% if fieldset.name %}
        <h2>{{ fieldset.name }}</h2>
    {% endif %}
    
    <div class="fieldset-content">
        {% if fieldset.description %}
            <div class="description">{{ fieldset.description|safe }}</div>
        {% endif %}
        
        {% for line in fieldset %}
            <div class="form-row{% if line.fields|length_is:'1' and line.errors %} errors{% endif %}{% if not line.has_visible_field %} hidden{% endif %}{% for field in line %}{% if field.field.name %} field-{{ field.field.name }}{% endif %}{% endfor %}">
                {% if line.fields|length_is:'1' %}
                    {% for field in line %}
                        <div>
                            {% if field.is_checkbox %}
                                <div class="checkbox-row">
                                    {{ field.field }}
                                    {{ field.label_tag }}
                                </div>
                            {% else %}
                                {{ field.label_tag }}
                                {% if field.is_readonly %}
                                    <div class="readonly">{{ field.contents }}</div>
                                {% else %}
                                    {{ field.field }}
                                {% endif %}
                            {% endif %}
                            
                            {% if field.field.help_text %}
                                <div class="help">{{ field.field.help_text|safe }}</div>
                            {% endif %}
                            
                            {% for error in field.field.errors %}
                                <ul class="errorlist">
                                    <li>{{ error }}</li>
                                </ul>
                            {% endfor %}
                        </div>
                    {% endfor %}
                {% else %}
                    <div class="form-row-multiple">
                        {% for field in line %}
                            <div class="form-field">
                                {% if field.is_checkbox %}
                                    <div class="checkbox-row">
                                        {{ field.field }}
                                        {{ field.label_tag }}
                                    </div>
                                {% else %}
                                    {{ field.label_tag }}
                                    {% if field.is_readonly %}
                                        <div class="readonly">{{ field.contents }}</div>
                                    {% else %}
                                        {{ field.field }}
                                    {% endif %}
                                {% endif %}
                                
                                {% if field.field.help_text %}
                                    <div class="help">{{ field.field.help_text|safe }}</div>
                                {% endif %}
                                
                                {% for error in field.field.errors %}
                                    <ul class="errorlist">
                                        <li>{{ error }}</li>
                                    </ul>
                                {% endfor %}
                            </div>
                        {% endfor %}
                    </div>
                {% endif %}
            </div>
        {% endfor %}
    </div>
</fieldset>

<style>
    .form-row-multiple {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
    }
    
    .form-field {
        display: flex;
        flex-direction: column;
    }
    
    .readonly {
        background: #f8f9fa;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        padding: 12px 15px;
        color: var(--secondary-color);
        font-weight: 500;
    }
    
    .description {
        background: rgba(52, 152, 219, 0.1);
        border: 1px solid var(--info-color);
        border-radius: 8px;
        padding: 1rem 1.5rem;
        margin-bottom: 1.5rem;
        color: var(--info-color);
        font-weight: 500;
    }
    
    .description:before {
        content: 'ℹ️';
        margin-left: 10px;
    }
    
    @media (max-width: 768px) {
        .form-row-multiple {
            grid-template-columns: 1fr;
        }
    }
</style>
