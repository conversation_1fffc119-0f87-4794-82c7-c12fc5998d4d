{% extends 'base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .hero-mini {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        position: relative;
        overflow: hidden;
    }

    .hero-mini::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('{% static "img/pattern.png" %}') repeat;
        opacity: 0.1;
    }

    .form-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        border: none;
        overflow: hidden;
        margin-top: -50px;
        position: relative;
        z-index: 2;
    }

    .form-card .card-header {
        background: linear-gradient(135deg, #3949ab 0%, #5e72e4 100%);
        border: none;
        padding: 2rem;
        text-align: center;
    }

    .form-card .card-body {
        padding: 3rem;
    }

    .form-label {
        font-weight: 600;
        color: #2d3748;
        margin-bottom: 0.5rem;
        font-size: 1rem;
    }

    .form-control, .form-select {
        border: 2px solid #e2e8f0;
        border-radius: 12px;
        padding: 0.75rem 1rem;
        font-size: 1rem;
        transition: all 0.3s ease;
        background-color: #f8fafc;
    }

    .form-control:focus, .form-select:focus {
        border-color: #3949ab;
        box-shadow: 0 0 0 3px rgba(57, 73, 171, 0.1);
        background-color: white;
    }

    .scholars-selection {
        border: 2px solid #e2e8f0;
        border-radius: 12px;
        padding: 1rem;
        background-color: #f8fafc;
        max-height: 300px;
        overflow-y: auto;
    }

    .scholars-selection select {
        border: none;
        background: transparent;
        width: 100%;
        min-height: 200px;
    }

    .scholars-selection select:focus {
        outline: none;
        box-shadow: none;
    }

    .selection-controls {
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .btn-primary {
        background: linear-gradient(135deg, #3949ab 0%, #5e72e4 100%);
        border: none;
        border-radius: 12px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(57, 73, 171, 0.3);
    }

    .btn-secondary {
        background: #6c757d;
        border: none;
        border-radius: 12px;
        padding: 0.75rem 2rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-secondary:hover {
        background: #5a6268;
        transform: translateY(-2px);
    }

    .btn-outline-primary {
        border: 2px solid #3949ab;
        color: #3949ab;
        border-radius: 8px;
        padding: 0.5rem 1rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }

    .btn-outline-primary:hover {
        background: #3949ab;
        border-color: #3949ab;
        color: white;
    }

    .back-link {
        color: white;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .back-link:hover {
        color: #f8f9fa;
        text-decoration: none;
    }

    .required-field::after {
        content: ' *';
        color: #e53e3e;
    }

    .form-text {
        color: #718096;
        font-size: 0.875rem;
        margin-top: 0.25rem;
    }

    .alert {
        border-radius: 12px;
        border: none;
        margin-bottom: 2rem;
    }

    .alert-info {
        background: linear-gradient(135deg, #e6f3ff 0%, #cce7ff 100%);
        color: #0c5aa6;
    }

    .alert-warning {
        background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
        color: #e65100;
    }

    .selected-count {
        background: linear-gradient(135deg, #4caf50 0%, #66bb6a 100%);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: 600;
        display: inline-block;
        margin-top: 0.5rem;
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="hero-mini text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-md-8 mx-auto text-center">
                <h1 class="display-5 fw-bold mb-3">{{ title }}</h1>
                <p class="lead">إرسال دعوات جماعية للعلماء للمشاركة في المؤتمر</p>
                <a href="{% url 'organizations:scholar_list' %}" class="back-link">
                    <i class="fas fa-arrow-right me-2"></i> العودة إلى قائمة العلماء
                </a>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card form-card">
                <div class="card-header">
                    <h4 class="mb-0 text-white">
                        <i class="fas fa-envelope-open me-2"></i>إرسال دعوات جماعية للعلماء
                    </h4>
                </div>
                <div class="card-body">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <div class="alert alert-info">
                        <i class="fab fa-whatsapp me-2"></i>
                        <strong>ملاحظة:</strong> سيتم إعداد روابط WhatsApp لإرسال الدعوات إلى أرقام هواتف العلماء. تأكد من وجود أرقام هواتف صحيحة.
                    </div>

                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تنبيه:</strong> سيتم إعداد نفس الرسالة لجميع العلماء المحددين. يمكنك استخدام الدعوات الفردية للرسائل المخصصة.
                    </div>

                    <form method="post" class="needs-validation" novalidate id="bulk-invitation-form">
                        {% csrf_token %}

                        <!-- Scholars Selection -->
                        <div class="mb-4">
                            <label for="{{ form.scholars.id_for_label }}" class="form-label required-field">
                                {{ form.scholars.label }}
                            </label>

                            <div class="selection-controls">
                                <div class="d-flex align-items-center justify-content-between">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="select-all-scholars">
                                        <label class="form-check-label" for="select-all-scholars">
                                            تحديد جميع العلماء
                                        </label>
                                    </div>
                                    <div>
                                        <button type="button" class="btn btn-outline-primary btn-sm me-2" id="select-confirmed">
                                            تحديد المؤكدين فقط
                                        </button>
                                        <button type="button" class="btn btn-outline-primary btn-sm" id="select-invited">
                                            تحديد المدعوين فقط
                                        </button>
                                    </div>
                                </div>
                                <div class="selected-count" id="selected-count-display">
                                    <i class="fas fa-users me-1"></i>
                                    <span id="selected-count">0</span> عالم محدد
                                </div>
                            </div>

                            <div class="scholars-selection">
                                {{ form.scholars }}
                            </div>
                            {% if form.scholars.errors %}
                                <div class="text-danger mt-1">{{ form.scholars.errors }}</div>
                            {% endif %}
                            <div class="form-text">اختر العلماء الذين تريد إرسال الدعوة إليهم (يمكنك اختيار أكثر من عالم بالضغط على Ctrl)</div>
                        </div>

                        <!-- Subject -->
                        <div class="mb-4">
                            <label for="{{ form.subject.id_for_label }}" class="form-label required-field">
                                {{ form.subject.label }}
                            </label>
                            {{ form.subject }}
                            {% if form.subject.errors %}
                                <div class="text-danger mt-1">{{ form.subject.errors }}</div>
                            {% endif %}
                            <div class="form-text">عنوان الدعوة الذي سيظهر في البريد الإلكتروني</div>
                        </div>

                        <!-- Message -->
                        <div class="mb-4">
                            <label for="{{ form.message.id_for_label }}" class="form-label required-field">
                                {{ form.message.label }}
                            </label>
                            {{ form.message }}
                            {% if form.message.errors %}
                                <div class="text-danger mt-1">{{ form.message.errors }}</div>
                            {% endif %}
                            <div class="form-text">نص الدعوة الذي سيتم إرساله لجميع العلماء المحددين</div>
                        </div>

                        <!-- Form Actions -->
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <button type="submit" class="btn btn-primary btn-lg me-3" id="send-invitations-btn">
                                    <i class="fab fa-whatsapp me-2"></i>إعداد دعوات WhatsApp
                                    <span class="badge bg-light text-primary ms-2" id="send-count">0</span>
                                </button>
                                <a href="{% url 'organizations:scholar_list' %}" class="btn btn-secondary btn-lg">
                                    <i class="fas fa-times me-2"></i>إلغاء
                                </a>
                            </div>
                            <div class="text-muted">
                                <small><i class="fas fa-shield-alt me-1"></i> جميع البيانات محمية ومشفرة</small>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const scholarsSelect = document.getElementById('{{ form.scholars.id_for_label }}');
    const selectAllCheckbox = document.getElementById('select-all-scholars');
    const selectedCountDisplay = document.getElementById('selected-count');
    const sendCountBadge = document.getElementById('send-count');
    const sendButton = document.getElementById('send-invitations-btn');
    const selectConfirmedBtn = document.getElementById('select-confirmed');
    const selectInvitedBtn = document.getElementById('select-invited');

    function updateSelectedCount() {
        const selectedOptions = Array.from(scholarsSelect.selectedOptions);
        const count = selectedOptions.length;

        selectedCountDisplay.textContent = count;
        sendCountBadge.textContent = count;

        // Update button state
        if (count > 0) {
            sendButton.disabled = false;
            sendButton.classList.remove('btn-secondary');
            sendButton.classList.add('btn-primary');
        } else {
            sendButton.disabled = true;
            sendButton.classList.remove('btn-primary');
            sendButton.classList.add('btn-secondary');
        }

        // Update select all checkbox
        const totalOptions = scholarsSelect.options.length;
        selectAllCheckbox.checked = count === totalOptions;
        selectAllCheckbox.indeterminate = count > 0 && count < totalOptions;
    }

    // Handle select all checkbox
    selectAllCheckbox.addEventListener('change', function() {
        const options = Array.from(scholarsSelect.options);
        options.forEach(option => {
            option.selected = this.checked;
        });
        updateSelectedCount();
    });

    // Handle scholars selection change
    scholarsSelect.addEventListener('change', updateSelectedCount);

    // Handle select confirmed scholars
    selectConfirmedBtn.addEventListener('click', function() {
        const options = Array.from(scholarsSelect.options);
        options.forEach(option => {
            // Assuming the option text contains status information
            // You might need to adjust this based on your actual data structure
            option.selected = option.text.includes('مؤكد') || option.text.includes('confirmed');
        });
        updateSelectedCount();
    });

    // Handle select invited scholars
    selectInvitedBtn.addEventListener('click', function() {
        const options = Array.from(scholarsSelect.options);
        options.forEach(option => {
            option.selected = option.text.includes('مدعو') || option.text.includes('invited');
        });
        updateSelectedCount();
    });

    // Form validation
    const form = document.getElementById('bulk-invitation-form');
    form.addEventListener('submit', function(event) {
        const selectedCount = scholarsSelect.selectedOptions.length;

        if (selectedCount === 0) {
            event.preventDefault();
            alert('يرجى تحديد عالم واحد على الأقل لإرسال الدعوة');
            return false;
        }

        if (!confirm(`هل أنت متأكد من إرسال الدعوة إلى ${selectedCount} عالم؟`)) {
            event.preventDefault();
            return false;
        }

        // Show loading state
        sendButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإرسال...';
        sendButton.disabled = true;
    });

    // Auto-resize textarea
    const messageTextarea = document.getElementById('{{ form.message.id_for_label }}');
    if (messageTextarea) {
        messageTextarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = this.scrollHeight + 'px';
        });

        // Initial resize
        messageTextarea.style.height = messageTextarea.scrollHeight + 'px';
    }

    // Initial count update
    updateSelectedCount();
});
</script>
{% endblock %}
