{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}تفاصيل الدعوة{% endblock %}

{% block extra_css %}
<style>
    .hero-mini {
        background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
        color: white;
        padding: 3rem 0;
        text-align: center;
        position: relative;
        overflow: hidden;
        margin-bottom: 2rem;
    }
    
    .hero-mini::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url('{% static "img/pattern.png" %}');
        opacity: 0.1;
    }
    
    .hero-mini .container {
        position: relative;
        z-index: 1;
    }
    
    .invitation-detail-card {
        border: none;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
        border-radius: 0.5rem;
        overflow: hidden;
        margin-bottom: 2rem;
    }
    
    .invitation-detail-card .card-header {
        background: linear-gradient(135deg, #f5f5f5 0%, #e0e0e0 100%);
        border-bottom: none;
        padding: 1.25rem 1.5rem;
    }
    
    .invitation-detail-card .card-body {
        padding: 1.5rem;
    }
    
    .status-badge {
        padding: 0.5rem 1rem;
        border-radius: 50px;
        font-weight: 600;
        font-size: 0.8rem;
    }
    
    .status-draft {
        background-color: #e9ecef;
        color: #495057;
    }
    
    .status-sent {
        background-color: #d4edda;
        color: #155724;
    }
    
    .status-cancelled {
        background-color: #f8d7da;
        color: #721c24;
    }
    
    .status-responded {
        background-color: #cce5ff;
        color: #004085;
    }
    
    .invitation-message {
        background-color: #f8f9fa;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-top: 1rem;
        border-left: 4px solid #3949ab;
    }
    
    .invitation-actions {
        background-color: #f8f9fa;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
        box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.05);
    }
    
    .invitation-history {
        background-color: #fff;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-top: 2rem;
        box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.05);
    }
    
    .history-item {
        padding: 1rem;
        border-bottom: 1px solid #e9ecef;
    }
    
    .history-item:last-child {
        border-bottom: none;
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section -->
<div class="hero-mini text-white py-5">
    <div class="container">
        <div class="row">
            <div class="col-md-8 mx-auto text-center">
                <h1 class="display-5 fw-bold mb-3">تفاصيل الدعوة</h1>
                <p class="lead">عرض ومتابعة حالة الدعوة المرسلة للمؤسسة</p>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <!-- Navigation Links -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'organizations:organization_list' %}">المؤسسات</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'organizations:invitation_list' %}">الدعوات</a></li>
                    <li class="breadcrumb-item active" aria-current="page">تفاصيل الدعوة</li>
                </ol>
            </nav>
        </div>
    </div>
    
    <!-- Action Buttons -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="invitation-actions">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>إدارة الدعوة</h5>
                    <div>
                        <a href="{% url 'organizations:invitation_list' %}" class="btn btn-secondary me-2">
                            <i class="fas fa-arrow-left me-1"></i> العودة للقائمة
                        </a>
                        
                        {% if invitation.status == 'draft' %}
                        <a href="{% url 'organizations:invitation_update' invitation.pk %}" class="btn btn-info me-2">
                            <i class="fas fa-edit me-1"></i> تعديل
                        </a>
                        <form method="post" action="{% url 'organizations:invitation_send' invitation.pk %}" class="d-inline">
                            {% csrf_token %}
                            <button type="submit" class="btn btn-success me-2">
                                <i class="fas fa-paper-plane me-1"></i> إرسال
                            </button>
                        </form>
                        {% elif invitation.status == 'sent' %}
                        <a href="{% url 'organizations:invitation_cancel' invitation.pk %}" class="btn btn-warning me-2">
                            <i class="fas fa-ban me-1"></i> إلغاء
                        </a>
                        {% endif %}
                        
                        <a href="{% url 'organizations:invitation_delete' invitation.pk %}" class="btn btn-danger">
                            <i class="fas fa-trash me-1"></i> حذف
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Invitation Details -->
    <div class="row">
        <div class="col-md-8">
            <div class="card invitation-detail-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="mb-0">{{ invitation.subject }}</h4>
                    <span class="status-badge status-{{ invitation.status }}">
                        {% if invitation.status == 'draft' %}
                        <i class="fas fa-file me-1"></i> مسودة
                        {% elif invitation.status == 'sent' %}
                        <i class="fas fa-paper-plane me-1"></i> مرسلة
                        {% elif invitation.status == 'cancelled' %}
                        <i class="fas fa-ban me-1"></i> ملغاة
                        {% elif invitation.status == 'responded' %}
                        <i class="fas fa-reply me-1"></i> تم الرد
                        {% endif %}
                    </span>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5 class="mb-3"><i class="fas fa-info-circle me-2 text-primary"></i>معلومات الدعوة</h5>
                            <div class="mb-3">
                                <strong>المؤسسة:</strong>
                                <a href="{% url 'organizations:organization_detail' invitation.organization.pk %}" class="text-decoration-none">
                                    {{ invitation.organization.name }}
                                </a>
                            </div>
                            <div class="mb-3">
                                <strong>البريد الإلكتروني:</strong>
                                <span>{{ invitation.organization.email }}</span>
                            </div>
                            <div class="mb-3">
                                <strong>تاريخ الإنشاء:</strong>
                                <span>{{ invitation.created_at|date:"Y-m-d H:i" }}</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h5 class="mb-3"><i class="fas fa-history me-2 text-primary"></i>سجل الدعوة</h5>
                            <div class="mb-3">
                                <strong>تاريخ الإرسال:</strong>
                                {% if invitation.is_sent %}
                                <span>{{ invitation.sent_at|date:"Y-m-d H:i" }}</span>
                                {% else %}
                                <span class="text-muted">لم يتم الإرسال بعد</span>
                                {% endif %}
                            </div>
                            <div class="mb-3">
                                <strong>آخر تحديث:</strong>
                                <span>{{ invitation.updated_at|date:"Y-m-d H:i" }}</span>
                            </div>
                            <div class="mb-3">
                                <strong>حالة الدعوة:</strong>
                                <span class="status-badge status-{{ invitation.status }} mt-1 d-inline-block">
                                    {% if invitation.status == 'draft' %}
                                    <i class="fas fa-file me-1"></i> مسودة
                                    {% elif invitation.status == 'sent' %}
                                    <i class="fas fa-paper-plane me-1"></i> مرسلة
                                    {% elif invitation.status == 'cancelled' %}
                                    <i class="fas fa-ban me-1"></i> ملغاة
                                    {% elif invitation.status == 'responded' %}
                                    <i class="fas fa-reply me-1"></i> تم الرد
                                    {% endif %}
                                </span>
                            </div>
                        </div>
                    </div>
                    
                    <h5 class="mb-3"><i class="fas fa-envelope-open-text me-2 text-primary"></i>محتوى الرسالة</h5>
                    <div class="invitation-message">
                        {{ invitation.message|linebreaks }}
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <!-- Organization Card -->
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-building me-2"></i>معلومات المؤسسة</h5>
                </div>
                <div class="card-body text-center">
                    {% if invitation.organization.logo %}
                    <img src="{{ invitation.organization.logo.url }}" alt="{{ invitation.organization.name }}" class="img-fluid rounded-circle mb-3" style="width: 100px; height: 100px; object-fit: cover;">
                    {% else %}
                    <div class="bg-light rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 100px; height: 100px;">
                        <i class="fas fa-building fa-3x text-secondary"></i>
                    </div>
                    {% endif %}
                    <h5>{{ invitation.organization.name }}</h5>
                    <p class="text-muted">{{ invitation.organization.email }}</p>
                    <a href="{% url 'organizations:organization_detail' invitation.organization.pk %}" class="btn btn-outline-primary w-100">
                        <i class="fas fa-eye me-1"></i> عرض المؤسسة
                    </a>
                </div>
            </div>
            
            <!-- Response Status Card (if sent) -->
            {% if invitation.status == 'sent' or invitation.status == 'responded' %}
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0"><i class="fas fa-reply me-2"></i>حالة الرد</h5>
                </div>
                <div class="card-body">
                    {% if invitation.status == 'responded' %}
                    <div class="text-center mb-3">
                        <div class="bg-success text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                            <i class="fas fa-check fa-3x"></i>
                        </div>
                        <h5>تم الرد على الدعوة</h5>
                        <p class="text-muted">قامت المؤسسة بالرد على الدعوة</p>
                    </div>
                    <div class="mb-3">
                        <strong>تاريخ الرد:</strong>
                        <span>{{ invitation.response_date|date:"Y-m-d H:i" }}</span>
                    </div>
                    <div class="mb-3">
                        <strong>حالة المشاركة:</strong>
                        <span class="badge {% if invitation.response_status == 'accepted' %}bg-success{% else %}bg-danger{% endif %}">
                            {% if invitation.response_status == 'accepted' %}
                            <i class="fas fa-check me-1"></i> موافق على المشاركة
                            {% else %}
                            <i class="fas fa-times me-1"></i> اعتذار عن المشاركة
                            {% endif %}
                        </span>
                    </div>
                    {% else %}
                    <div class="text-center mb-3">
                        <div class="bg-warning text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3" style="width: 80px; height: 80px;">
                            <i class="fas fa-clock fa-3x"></i>
                        </div>
                        <h5>في انتظار الرد</h5>
                        <p class="text-muted">لم تقم المؤسسة بالرد على الدعوة بعد</p>
                    </div>
                    <div class="mb-3">
                        <strong>تاريخ الإرسال:</strong>
                        <span>{{ invitation.sent_at|date:"Y-m-d H:i" }}</span>
                    </div>
                    <div class="mb-3">
                        <strong>المدة المنقضية:</strong>
                        <span>{{ invitation.sent_at|timesince }}</span>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}