{% load static %}
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>لوحة إدارة المؤتمر السنوي</title>
    
    <!-- Bootstrap RTL CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c5aa0;
            --secondary-color: #34495e;
            --success-color: #27ae60;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --info-color: #3498db;
            --light-color: #ecf0f1;
            --dark-color: #2c3e50;
            --sidebar-width: 280px;
        }

        * {
            font-family: 'Noto Sans Arabic', sans-serif;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            margin: 0;
            padding: 0;
        }

        .admin-wrapper {
            display: flex;
            min-height: 100vh;
        }

        /* Sidebar */
        .admin-sidebar {
            width: var(--sidebar-width);
            background: linear-gradient(180deg, #2c5aa0 0%, #1e3a8a 100%);
            color: white;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            box-shadow: 4px 0 15px rgba(0,0,0,0.1);
            z-index: 1000;
        }

        .sidebar-header {
            padding: 2rem 1.5rem;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .sidebar-header h3 {
            color: white;
            font-weight: 600;
            margin: 0;
            font-size: 1.3rem;
        }

        .sidebar-header p {
            color: rgba(255,255,255,0.8);
            margin: 0.5rem 0 0 0;
            font-size: 0.9rem;
        }

        .sidebar-menu {
            padding: 1rem 0;
        }

        .menu-section {
            margin-bottom: 2rem;
        }

        .menu-section-title {
            padding: 0.5rem 1.5rem;
            font-size: 0.8rem;
            font-weight: 600;
            color: rgba(255,255,255,0.6);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .menu-item {
            display: block;
            padding: 0.8rem 1.5rem;
            color: rgba(255,255,255,0.9);
            text-decoration: none;
            transition: all 0.3s ease;
            border-right: 3px solid transparent;
        }

        .menu-item:hover {
            background: rgba(255,255,255,0.1);
            color: white;
            border-right-color: #ffd700;
            transform: translateX(-5px);
        }

        .menu-item.active {
            background: rgba(255,255,255,0.15);
            border-right-color: #ffd700;
            color: white;
        }

        .menu-item i {
            width: 20px;
            margin-left: 10px;
            text-align: center;
        }

        /* Main Content */
        .admin-content {
            margin-right: var(--sidebar-width);
            flex: 1;
            padding: 0;
        }

        .admin-header {
            background: white;
            padding: 1rem 2rem;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .admin-header h1 {
            margin: 0;
            color: var(--dark-color);
            font-weight: 600;
        }

        .admin-main {
            padding: 2rem;
        }

        /* Dashboard Cards */
        .dashboard-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin-bottom: 2rem;
        }

        .dashboard-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: none;
            position: relative;
            overflow: hidden;
        }

        .dashboard-card::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100%;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--info-color));
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .card-icon {
            width: 60px;
            height: 60px;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
            margin-bottom: 1rem;
        }

        .card-icon.primary { background: linear-gradient(135deg, var(--primary-color), var(--info-color)); }
        .card-icon.success { background: linear-gradient(135deg, var(--success-color), #2ecc71); }
        .card-icon.warning { background: linear-gradient(135deg, var(--warning-color), #e67e22); }
        .card-icon.danger { background: linear-gradient(135deg, var(--danger-color), #c0392b); }

        .card-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 0.5rem;
        }

        .card-value {
            font-size: 2rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .card-description {
            color: #7f8c8d;
            font-size: 0.9rem;
        }

        .card-actions {
            margin-top: 1rem;
            display: flex;
            gap: 0.5rem;
        }

        .btn-custom {
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-size: 0.9rem;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }

        .btn-primary-custom {
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            color: white;
        }

        .btn-primary-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(44, 90, 160, 0.4);
            color: white;
        }

        .btn-outline-custom {
            border: 2px solid var(--primary-color);
            color: var(--primary-color);
            background: transparent;
        }

        .btn-outline-custom:hover {
            background: var(--primary-color);
            color: white;
        }

        /* Quick Actions */
        .quick-actions {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }

        .quick-actions h3 {
            color: var(--dark-color);
            font-weight: 600;
            margin-bottom: 1.5rem;
            display: flex;
            align-items: center;
        }

        .quick-actions h3 i {
            margin-left: 10px;
            color: var(--primary-color);
        }

        .action-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
        }

        .action-btn {
            display: flex;
            align-items: center;
            padding: 1rem;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border: 2px solid transparent;
            border-radius: 10px;
            text-decoration: none;
            color: var(--dark-color);
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .action-btn:hover {
            border-color: var(--primary-color);
            background: linear-gradient(135deg, var(--primary-color), var(--info-color));
            color: white;
            transform: translateY(-2px);
        }

        .action-btn i {
            font-size: 1.2rem;
            margin-left: 10px;
            width: 25px;
            text-align: center;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .admin-sidebar {
                transform: translateX(100%);
                transition: transform 0.3s ease;
            }
            
            .admin-sidebar.show {
                transform: translateX(0);
            }
            
            .admin-content {
                margin-right: 0;
            }
            
            .dashboard-cards {
                grid-template-columns: 1fr;
            }
            
            .action-buttons {
                grid-template-columns: 1fr;
            }
        }

        /* User Info */
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
        }

        .logout-btn {
            background: var(--danger-color);
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .logout-btn:hover {
            background: #c0392b;
            color: white;
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="admin-wrapper">
        <!-- Sidebar -->
        <nav class="admin-sidebar">
            <div class="sidebar-header">
                <h3>لوحة الإدارة</h3>
                <p>مؤتمر السيرة النبوية السنوي</p>
            </div>
            
            <div class="sidebar-menu">
                <div class="menu-section">
                    <div class="menu-section-title">الرئيسية</div>
                    <a href="{% url 'admin:index' %}" class="menu-item active">
                        <i class="fas fa-tachometer-alt"></i>
                        لوحة التحكم
                    </a>
                </div>
                
                <div class="menu-section">
                    <div class="menu-section-title">المؤسسات</div>
                    <a href="{% url 'admin:organizations_organization_changelist' %}" class="menu-item">
                        <i class="fas fa-building"></i>
                        إدارة المؤسسات
                    </a>
                    <a href="{% url 'admin:organizations_invitation_changelist' %}" class="menu-item">
                        <i class="fas fa-envelope"></i>
                        دعوات المؤسسات
                    </a>
                </div>
                
                <div class="menu-section">
                    <div class="menu-section-title">العلماء</div>
                    <a href="{% url 'admin:organizations_scholar_changelist' %}" class="menu-item">
                        <i class="fas fa-user-graduate"></i>
                        إدارة العلماء
                    </a>
                    <a href="{% url 'admin:organizations_scholarinvitation_changelist' %}" class="menu-item">
                        <i class="fas fa-paper-plane"></i>
                        دعوات العلماء
                    </a>
                </div>
                
                <div class="menu-section">
                    <div class="menu-section-title">النظام</div>
                    <a href="{% url 'admin:auth_user_changelist' %}" class="menu-item">
                        <i class="fas fa-users"></i>
                        المستخدمون
                    </a>
                    <a href="{% url 'admin:auth_group_changelist' %}" class="menu-item">
                        <i class="fas fa-users-cog"></i>
                        المجموعات
                    </a>
                </div>
            </div>
        </nav>
        
        <!-- Main Content -->
        <main class="admin-content">
            <header class="admin-header">
                <h1>مرحباً بك في لوحة إدارة المؤتمر</h1>
                <div class="user-info">
                    <div class="user-avatar">
                        {{ user.first_name.0|default:user.username.0|upper }}
                    </div>
                    <span>{{ user.get_full_name|default:user.username }}</span>
                    <a href="{% url 'admin:logout' %}" class="logout-btn">
                        <i class="fas fa-sign-out-alt"></i>
                        تسجيل الخروج
                    </a>
                </div>
            </header>
            
            <div class="admin-main">
                <!-- Dashboard Cards -->
                <div class="dashboard-cards">
                    <div class="dashboard-card">
                        <div class="card-icon primary">
                            <i class="fas fa-building"></i>
                        </div>
                        <div class="card-title">إجمالي المؤسسات</div>
                        <div class="card-value">{{ total_organizations|default:0 }}</div>
                        <div class="card-description">المؤسسات المسجلة في النظام</div>
                        <div class="card-actions">
                            <a href="{% url 'admin:organizations_organization_changelist' %}" class="btn-custom btn-primary-custom">
                                عرض الكل
                            </a>
                            <a href="{% url 'admin:organizations_organization_add' %}" class="btn-custom btn-outline-custom">
                                إضافة جديد
                            </a>
                        </div>
                    </div>
                    
                    <div class="dashboard-card">
                        <div class="card-icon success">
                            <i class="fas fa-user-graduate"></i>
                        </div>
                        <div class="card-title">إجمالي العلماء</div>
                        <div class="card-value">{{ total_scholars|default:0 }}</div>
                        <div class="card-description">العلماء المسجلون في النظام</div>
                        <div class="card-actions">
                            <a href="{% url 'admin:organizations_scholar_changelist' %}" class="btn-custom btn-primary-custom">
                                عرض الكل
                            </a>
                            <a href="{% url 'admin:organizations_scholar_add' %}" class="btn-custom btn-outline-custom">
                                إضافة جديد
                            </a>
                        </div>
                    </div>
                    
                    <div class="dashboard-card">
                        <div class="card-icon warning">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <div class="card-title">الدعوات المرسلة</div>
                        <div class="card-value">{{ total_invitations|default:0 }}</div>
                        <div class="card-description">دعوات المؤسسات والعلماء</div>
                        <div class="card-actions">
                            <a href="{% url 'admin:organizations_invitation_changelist' %}" class="btn-custom btn-primary-custom">
                                دعوات المؤسسات
                            </a>
                            <a href="{% url 'admin:organizations_scholarinvitation_changelist' %}" class="btn-custom btn-outline-custom">
                                دعوات العلماء
                            </a>
                        </div>
                    </div>
                    
                    <div class="dashboard-card">
                        <div class="card-icon danger">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="card-title">المشاركات المؤكدة</div>
                        <div class="card-value">{{ confirmed_participants|default:0 }}</div>
                        <div class="card-description">المؤسسات والعلماء المؤكدون</div>
                        <div class="card-actions">
                            <a href="#" class="btn-custom btn-primary-custom">
                                عرض التفاصيل
                            </a>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="quick-actions">
                    <h3>
                        <i class="fas fa-bolt"></i>
                        الإجراءات السريعة
                    </h3>
                    <div class="action-buttons">
                        <a href="{% url 'admin:organizations_organization_add' %}" class="action-btn">
                            <i class="fas fa-plus"></i>
                            إضافة مؤسسة جديدة
                        </a>
                        <a href="{% url 'admin:organizations_scholar_add' %}" class="action-btn">
                            <i class="fas fa-user-plus"></i>
                            إضافة عالم جديد
                        </a>
                        <a href="{% url 'organizations:send_bulk_invitation' %}" class="action-btn">
                            <i class="fas fa-paper-plane"></i>
                            إرسال دعوات جماعية
                        </a>
                        <a href="{% url 'organizations:export_scholars_excel' %}" class="action-btn">
                            <i class="fas fa-file-excel"></i>
                            تصدير بيانات العلماء
                        </a>
                        <a href="{% url 'organizations:organization_list' %}" class="action-btn">
                            <i class="fas fa-map"></i>
                            خريطة المؤسسات
                        </a>
                        <a href="{% url 'admin:auth_user_changelist' %}" class="action-btn">
                            <i class="fas fa-users-cog"></i>
                            إدارة المستخدمين
                        </a>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
