{% extends 'base.html' %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<style>
    .import-container {
        background: white;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        padding: 30px;
        margin-top: 20px;
    }
    
    .import-header {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;
        padding: 20px;
        border-radius: 10px;
        margin-bottom: 30px;
        text-align: center;
    }
    
    .step-card {
        background: #f8f9fa;
        border: 1px solid #e9ecef;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        position: relative;
    }
    
    .step-number {
        position: absolute;
        top: -15px;
        left: 20px;
        background: #28a745;
        color: white;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: bold;
        font-size: 0.9rem;
    }
    
    .step-title {
        color: #28a745;
        font-weight: bold;
        margin-bottom: 15px;
        margin-top: 5px;
    }
    
    .file-upload-area {
        border: 2px dashed #28a745;
        border-radius: 10px;
        padding: 40px;
        text-align: center;
        background: #f8fff9;
        transition: all 0.3s ease;
        cursor: pointer;
    }
    
    .file-upload-area:hover {
        background: #e8f5e8;
        border-color: #20c997;
    }
    
    .file-upload-area.dragover {
        background: #d4edda;
        border-color: #155724;
    }
    
    .upload-icon {
        font-size: 3rem;
        color: #28a745;
        margin-bottom: 15px;
    }
    
    .file-info {
        background: #e3f2fd;
        border: 1px solid #bbdefb;
        border-radius: 8px;
        padding: 15px;
        margin-top: 15px;
        display: none;
    }
    
    .sample-table {
        font-size: 0.85rem;
        margin-top: 15px;
    }
    
    .sample-table th {
        background: #28a745;
        color: white;
        font-weight: bold;
        text-align: center;
        padding: 8px;
        font-size: 0.8rem;
    }
    
    .sample-table td {
        padding: 6px;
        text-align: center;
        border: 1px solid #dee2e6;
    }
    
    .btn-success {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: bold;
        transition: all 0.3s ease;
    }
    
    .btn-success:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
    }
    
    .btn-secondary {
        background: #6c757d;
        border: none;
        padding: 12px 30px;
        border-radius: 25px;
        font-weight: bold;
        transition: all 0.3s ease;
    }
    
    .btn-secondary:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(108, 117, 125, 0.4);
    }
    
    .alert {
        border-radius: 10px;
        border: none;
        padding: 15px 20px;
    }
    
    .requirements-list {
        list-style: none;
        padding: 0;
    }
    
    .requirements-list li {
        padding: 8px 0;
        border-bottom: 1px solid #e9ecef;
        position: relative;
        padding-left: 25px;
    }
    
    .requirements-list li:before {
        content: "✓";
        position: absolute;
        left: 0;
        color: #28a745;
        font-weight: bold;
    }
    
    .requirements-list li:last-child {
        border-bottom: none;
    }
    
    .progress-container {
        display: none;
        margin-top: 20px;
    }
    
    .progress {
        height: 25px;
        border-radius: 15px;
        background: #e9ecef;
    }
    
    .progress-bar {
        border-radius: 15px;
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        transition: width 0.3s ease;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-10">
            <div class="import-container">
                <div class="import-header">
                    <h3><i class="fas fa-file-import"></i> {{ title }}</h3>
                    <p class="mb-0">استيراد بيانات رؤساء الأحزاب من ملف Excel</p>
                </div>

                <!-- الخطوة الأولى: تحضير الملف -->
                <div class="step-card">
                    <div class="step-number">1</div>
                    <h5 class="step-title">تحضير ملف Excel</h5>
                    <p>يجب أن يحتوي ملف Excel على الأعمدة التالية بنفس الترتيب:</p>
                    
                    <div class="table-responsive">
                        <table class="table table-bordered sample-table">
                            <thead>
                                <tr>
                                    <th>الاسم الكامل</th>
                                    <th>اسم الحزب</th>
                                    <th>المنصب</th>
                                    <th>رقم الهاتف</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>العنوان</th>
                                    <th>سنة التأسيس</th>
                                    <th>الأيديولوجية</th>
                                    <th>مقر الحزب</th>
                                    <th>السيرة الذاتية</th>
                                    <th>الإنجازات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>محمد أحمد</td>
                                    <td>حزب التقدم</td>
                                    <td>رئيس الحزب</td>
                                    <td>22123456</td>
                                    <td><EMAIL></td>
                                    <td>نواكشوط</td>
                                    <td>2010</td>
                                    <td>ليبرالي</td>
                                    <td>شارع الاستقلال</td>
                                    <td>سياسي محنك</td>
                                    <td>قاد الحزب لسنوات</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- الخطوة الثانية: متطلبات الملف -->
                <div class="step-card">
                    <div class="step-number">2</div>
                    <h5 class="step-title">متطلبات الملف</h5>
                    <ul class="requirements-list">
                        <li>يجب أن يكون الملف بصيغة Excel (.xlsx أو .xls)</li>
                        <li>الصف الأول يجب أن يحتوي على عناوين الأعمدة</li>
                        <li>الاسم الكامل واسم الحزب مطلوبان (لا يمكن أن يكونا فارغين)</li>
                        <li>سنة التأسيس يجب أن تكون رقماً صحيحاً</li>
                        <li>البريد الإلكتروني يجب أن يكون بصيغة صحيحة</li>
                        <li>الحد الأقصى لحجم الملف: 10 ميجابايت</li>
                    </ul>
                </div>

                <!-- الخطوة الثالثة: رفع الملف -->
                <div class="step-card">
                    <div class="step-number">3</div>
                    <h5 class="step-title">رفع الملف</h5>
                    
                    <form method="post" enctype="multipart/form-data" id="importForm">
                        {% csrf_token %}
                        
                        <div class="file-upload-area" id="fileUploadArea">
                            <div class="upload-icon">
                                <i class="fas fa-cloud-upload-alt"></i>
                            </div>
                            <h5>اسحب الملف هنا أو انقر للاختيار</h5>
                            <p class="text-muted">يدعم ملفات Excel (.xlsx, .xls) فقط</p>
                            <input type="file" name="file" id="fileInput" accept=".xlsx,.xls" style="display: none;" required>
                        </div>
                        
                        <div class="file-info" id="fileInfo">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-file-excel text-success me-2"></i>
                                <div>
                                    <strong id="fileName"></strong><br>
                                    <small class="text-muted" id="fileSize"></small>
                                </div>
                                <button type="button" class="btn btn-sm btn-outline-danger ms-auto" id="removeFile">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="progress-container" id="progressContainer">
                            <div class="progress">
                                <div class="progress-bar" role="progressbar" style="width: 0%">
                                    <span class="progress-text">0%</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-success me-3" id="importBtn" disabled>
                                <i class="fas fa-file-import"></i> بدء الاستيراد
                            </button>
                            <a href="{% url 'organizations:party_leaders' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> العودة للقائمة
                            </a>
                        </div>
                    </form>
                </div>

                <!-- تحذيرات مهمة -->
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-triangle"></i> تحذيرات مهمة:</h6>
                    <ul class="mb-0">
                        <li>سيتم إنشاء رؤساء أحزاب جدد فقط، ولن يتم تحديث البيانات الموجودة</li>
                        <li>في حالة وجود رئيس حزب بنفس الاسم واسم الحزب، سيتم تجاهل السجل</li>
                        <li>تأكد من صحة البيانات قبل الاستيراد</li>
                        <li>يُنصح بعمل نسخة احتياطية من البيانات قبل الاستيراد</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const fileUploadArea = document.getElementById('fileUploadArea');
    const fileInput = document.getElementById('fileInput');
    const fileInfo = document.getElementById('fileInfo');
    const fileName = document.getElementById('fileName');
    const fileSize = document.getElementById('fileSize');
    const removeFile = document.getElementById('removeFile');
    const importBtn = document.getElementById('importBtn');
    const importForm = document.getElementById('importForm');
    const progressContainer = document.getElementById('progressContainer');
    
    // النقر على منطقة الرفع
    fileUploadArea.addEventListener('click', () => fileInput.click());
    
    // السحب والإفلات
    fileUploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        fileUploadArea.classList.add('dragover');
    });
    
    fileUploadArea.addEventListener('dragleave', () => {
        fileUploadArea.classList.remove('dragover');
    });
    
    fileUploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        fileUploadArea.classList.remove('dragover');
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileSelect(files[0]);
        }
    });
    
    // اختيار الملف
    fileInput.addEventListener('change', (e) => {
        if (e.target.files.length > 0) {
            handleFileSelect(e.target.files[0]);
        }
    });
    
    // معالجة اختيار الملف
    function handleFileSelect(file) {
        // التحقق من نوع الملف
        const allowedTypes = ['.xlsx', '.xls'];
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
        
        if (!allowedTypes.includes(fileExtension)) {
            alert('يرجى اختيار ملف Excel صحيح (.xlsx أو .xls)');
            return;
        }
        
        // التحقق من حجم الملف (10 ميجابايت)
        if (file.size > 10 * 1024 * 1024) {
            alert('حجم الملف كبير جداً. الحد الأقصى 10 ميجابايت');
            return;
        }
        
        // عرض معلومات الملف
        fileName.textContent = file.name;
        fileSize.textContent = formatFileSize(file.size);
        fileInfo.style.display = 'block';
        importBtn.disabled = false;
        
        // إخفاء منطقة الرفع
        fileUploadArea.style.display = 'none';
    }
    
    // إزالة الملف
    removeFile.addEventListener('click', () => {
        fileInput.value = '';
        fileInfo.style.display = 'none';
        fileUploadArea.style.display = 'block';
        importBtn.disabled = true;
    });
    
    // تنسيق حجم الملف
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    // إرسال النموذج مع شريط التقدم
    importForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (!fileInput.files[0]) {
            alert('يرجى اختيار ملف للاستيراد');
            return;
        }
        
        // إظهار شريط التقدم
        progressContainer.style.display = 'block';
        importBtn.disabled = true;
        importBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الاستيراد...';
        
        // محاكاة التقدم
        let progress = 0;
        const progressBar = document.querySelector('.progress-bar');
        const progressText = document.querySelector('.progress-text');
        
        const progressInterval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress > 90) progress = 90;
            
            progressBar.style.width = progress + '%';
            progressText.textContent = Math.round(progress) + '%';
        }, 200);
        
        // إرسال النموذج
        const formData = new FormData(importForm);
        
        fetch(importForm.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            }
        })
        .then(response => {
            clearInterval(progressInterval);
            progressBar.style.width = '100%';
            progressText.textContent = '100%';
            
            setTimeout(() => {
                window.location.href = '{% url "organizations:party_leaders" %}';
            }, 1000);
        })
        .catch(error => {
            clearInterval(progressInterval);
            alert('حدث خطأ أثناء الاستيراد: ' + error.message);
            importBtn.disabled = false;
            importBtn.innerHTML = '<i class="fas fa-file-import"></i> بدء الاستيراد';
            progressContainer.style.display = 'none';
        });
    });
});
</script>
{% endblock %}
