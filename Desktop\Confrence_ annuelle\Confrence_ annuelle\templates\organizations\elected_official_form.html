{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/modern-theme.css' %}">
<style>
    .form-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 30px;
        margin-bottom: 30px;
        text-align: center;
    }
    
    .form-card {
        border: none;
        border-radius: 15px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }
    
    .form-section {
        background-color: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .form-section h5 {
        color: #495057;
        margin-bottom: 15px;
        font-weight: 600;
    }
    
    .form-control, .form-select {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        padding: 12px 15px;
        transition: all 0.3s ease;
    }
    
    .form-control:focus, .form-select:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
    
    .btn-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        border-radius: 10px;
        padding: 12px 30px;
        font-weight: 600;
    }
    
    .btn-secondary {
        border-radius: 10px;
        padding: 12px 30px;
        font-weight: 600;
    }
    
    .required-field::after {
        content: " *";
        color: #dc3545;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid" dir="rtl">
    <!-- Header -->
    <div class="form-header">
        <h1 class="mb-3">{{ title }}</h1>
        <p class="mb-0">{% if official %}تعديل بيانات المنتخب{% else %}إضافة منتخب جديد إلى النظام{% endif %}</p>
    </div>

    <!-- Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'organizations:elected_officials_list' %}">المنتخبون</a></li>
                    <li class="breadcrumb-item active">{{ title }}</li>
                </ol>
            </nav>
        </div>
    </div>

    <!-- Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card form-card">
                <div class="card-body p-4">
                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <!-- المعلومات الأساسية -->
                        <div class="form-section">
                            <h5><i class="fas fa-user me-2"></i>المعلومات الأساسية</h5>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.name.id_for_label }}" class="form-label required-field">{{ form.name.label }}</label>
                                    {{ form.name }}
                                    {% if form.name.errors %}
                                        <div class="text-danger small mt-1">
                                            {% for error in form.name.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.position.id_for_label }}" class="form-label required-field">{{ form.position.label }}</label>
                                    {{ form.position }}
                                    {% if form.position.errors %}
                                        <div class="text-danger small mt-1">
                                            {% for error in form.position.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.region.id_for_label }}" class="form-label required-field">{{ form.region.label }}</label>
                                    {{ form.region }}
                                    {% if form.region.errors %}
                                        <div class="text-danger small mt-1">
                                            {% for error in form.region.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.party.id_for_label }}" class="form-label">{{ form.party.label }}</label>
                                    {{ form.party }}
                                    {% if form.party.errors %}
                                        <div class="text-danger small mt-1">
                                            {% for error in form.party.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- معلومات الاتصال -->
                        <div class="form-section">
                            <h5><i class="fas fa-phone me-2"></i>معلومات الاتصال</h5>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.phone.id_for_label }}" class="form-label">{{ form.phone.label }}</label>
                                    {{ form.phone }}
                                    {% if form.phone.errors %}
                                        <div class="text-danger small mt-1">
                                            {% for error in form.phone.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="{{ form.email.id_for_label }}" class="form-label">{{ form.email.label }}</label>
                                    {{ form.email }}
                                    {% if form.email.errors %}
                                        <div class="text-danger small mt-1">
                                            {% for error in form.email.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <!-- تفاصيل المنصب -->
                        <div class="form-section">
                            <h5><i class="fas fa-calendar me-2"></i>تفاصيل المنصب</h5>
                            <div class="row">
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.status.id_for_label }}" class="form-label required-field">{{ form.status.label }}</label>
                                    {{ form.status }}
                                    {% if form.status.errors %}
                                        <div class="text-danger small mt-1">
                                            {% for error in form.status.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.start_date.id_for_label }}" class="form-label required-field">{{ form.start_date.label }}</label>
                                    {{ form.start_date }}
                                    {% if form.start_date.errors %}
                                        <div class="text-danger small mt-1">
                                            {% for error in form.start_date.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-4 mb-3">
                                    <label for="{{ form.end_date.id_for_label }}" class="form-label">{{ form.end_date.label }}</label>
                                    {{ form.end_date }}
                                    {% if form.end_date.errors %}
                                        <div class="text-danger small mt-1">
                                            {% for error in form.end_date.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="{{ form.notes.id_for_label }}" class="form-label">{{ form.notes.label }}</label>
                                {{ form.notes }}
                                {% if form.notes.errors %}
                                    <div class="text-danger small mt-1">
                                        {% for error in form.notes.errors %}{{ error }}{% endfor %}
                                    </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- أخطاء النموذج العامة -->
                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {% for error in form.non_field_errors %}
                                    <div>{{ error }}</div>
                                {% endfor %}
                            </div>
                        {% endif %}

                        <!-- أزرار الإجراءات -->
                        <div class="d-flex justify-content-between pt-3">
                            <a href="{% url 'organizations:elected_officials_list' %}" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>{{ submit_text }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // تحسين تجربة المستخدم للنموذج
    const form = document.querySelector('form');
    const submitBtn = form.querySelector('button[type="submit"]');
    
    form.addEventListener('submit', function() {
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحفظ...';
    });
    
    // تحسين عرض الأخطاء
    const errorFields = document.querySelectorAll('.form-control.is-invalid, .form-select.is-invalid');
    errorFields.forEach(field => {
        field.addEventListener('input', function() {
            this.classList.remove('is-invalid');
            const errorDiv = this.parentNode.querySelector('.text-danger');
            if (errorDiv) {
                errorDiv.style.display = 'none';
            }
        });
    });
});
</script>
{% endblock %}
