{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{% static 'css/modern-theme.css' %}">
<style>
    .stats-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    }
    
    .stats-number {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 5px;
    }
    
    .position-badge {
        padding: 5px 10px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: bold;
    }
    
    .badge-mayor { background-color: #28a745; color: white; }
    .badge-deputy { background-color: #007bff; color: white; }
    .badge-head { background-color: #6f42c1; color: white; }
    
    .status-badge {
        padding: 3px 8px;
        border-radius: 15px;
        font-size: 0.8rem;
    }
    
    .status-active { background-color: #d4edda; color: #155724; }
    .status-inactive { background-color: #f8d7da; color: #721c24; }
    .status-retired { background-color: #fff3cd; color: #856404; }

    .section-card {
        border: none;
        border-radius: 20px;
        box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        overflow: hidden;
        position: relative;
    }

    .section-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 25px 50px rgba(0,0,0,0.15);
    }

    .section-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 5px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .section-icon {
        position: relative;
        display: inline-block;
    }

    .section-icon::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 100px;
        height: 100px;
        background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
        border-radius: 50%;
        z-index: -1;
    }

    .stats-badge .badge {
        font-size: 1rem !important;
        padding: 8px 16px;
        border-radius: 25px;
    }

    .bg-purple {
        background-color: #6f42c1 !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid" dir="rtl">
    <div class="row">
        <div class="col-12">
            <div class="text-center mb-5">
                <h1 class="display-4 mb-3">{{ title }}</h1>
                <p class="lead text-muted">اختر القسم المناسب لإدارة المنتخبين</p>
                <div class="mt-4">
                    <a href="{% url 'organizations:add_elected_official' %}" class="btn btn-primary me-3">
                        <i class="fas fa-plus"></i> إضافة منتخب جديد
                    </a>
                    <a href="{% url 'organizations:export_elected_officials' %}" class="btn btn-success">
                        <i class="fas fa-file-excel"></i> تصدير Excel
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card text-center">
                <div class="stats-number">{{ stats.total }}</div>
                <div>إجمالي المنتخبين</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card text-center">
                <div class="stats-number">{{ stats.mayors }}</div>
                <div>العمد</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card text-center">
                <div class="stats-number">{{ stats.deputies }}</div>
                <div>النواب</div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card text-center">
                <div class="stats-number">{{ stats.heads }}</div>
                <div>رؤساء الجهات</div>
            </div>
        </div>
    </div>

    <!-- أقسام المنتخبين - التركيز الرئيسي -->
    <div class="row mb-5">
        <div class="col-md-4 mb-4">
            <div class="card h-100 section-card">
                <div class="card-body text-center p-4">
                    <div class="section-icon mb-4">
                        <i class="fas fa-user-tie fa-4x text-success"></i>
                    </div>
                    <h3 class="card-title mb-3">قسم العمد</h3>
                    <p class="card-text mb-4">إدارة العمد والمسؤولين المحليين في جميع أنحاء البلاد</p>
                    <div class="stats-badge mb-3">
                        <span class="badge bg-success fs-6">{{ stats.mayors }} عمدة</span>
                    </div>
                    <a href="{% url 'organizations:mayors_list' %}" class="btn btn-success btn-lg">
                        <i class="fas fa-arrow-left me-2"></i>دخول القسم
                    </a>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card h-100 section-card">
                <div class="card-body text-center p-4">
                    <div class="section-icon mb-4">
                        <i class="fas fa-users fa-4x text-primary"></i>
                    </div>
                    <h3 class="card-title mb-3">قسم النواب</h3>
                    <p class="card-text mb-4">إدارة النواب وأعضاء البرلمان والمجالس التشريعية</p>
                    <div class="stats-badge mb-3">
                        <span class="badge bg-primary fs-6">{{ stats.deputies }} نائب</span>
                    </div>
                    <a href="{% url 'organizations:deputies_list' %}" class="btn btn-primary btn-lg">
                        <i class="fas fa-arrow-left me-2"></i>دخول القسم
                    </a>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-4">
            <div class="card h-100 section-card">
                <div class="card-body text-center p-4">
                    <div class="section-icon mb-4">
                        <i class="fas fa-crown fa-4x text-purple"></i>
                    </div>
                    <h3 class="card-title mb-3">قسم رؤساء الجهات</h3>
                    <p class="card-text mb-4">إدارة رؤساء الجهات والمناطق الإدارية</p>
                    <div class="stats-badge mb-3">
                        <span class="badge bg-purple fs-6">{{ stats.heads }} رئيس جهة</span>
                    </div>
                    <a href="{% url 'organizations:heads_list' %}" class="btn btn-purple btn-lg">
                        <i class="fas fa-arrow-left me-2"></i>دخول القسم
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- إضافة رابط لعرض جميع المنتخبين -->
    <div class="row mb-4">
        <div class="col-12 text-center">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">عرض جميع المنتخبين</h5>
                    <p class="card-text">عرض قائمة شاملة بجميع المنتخبين من كافة الأقسام</p>
                    <a href="#" onclick="showAllElected()" class="btn btn-outline-primary">
                        <i class="fas fa-list me-2"></i>عرض القائمة الكاملة
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث والقائمة التفصيلية - مخفية افتراضياً -->
    <div id="detailedView" style="display: none;">
        <div class="card mb-4">
            <div class="card-body">
                <form method="get" class="row g-3">
                    <div class="col-md-4">
                        <label class="form-label">البحث</label>
                        <input type="text" name="search" class="form-control" value="{{ search_query }}" placeholder="البحث بالاسم أو المنطقة أو الحزب">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">المنصب</label>
                        <select name="position" class="form-select">
                            <option value="">جميع المناصب</option>
                            {% for value, label in position_choices %}
                                <option value="{{ value }}" {% if current_position == value %}selected{% endif %}>{{ label }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">الحالة</label>
                        <select name="status" class="form-select">
                            <option value="">جميع الحالات</option>
                            {% for value, label in status_choices %}
                                <option value="{{ value }}" {% if current_status == value %}selected{% endif %}>{{ label }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-primary">بحث</button>
                            <a href="{% url 'organizations:elected_officials_list' %}" class="btn btn-secondary">إعادة تعيين</a>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- قائمة المنتخبين -->
        <div class="card">
            <div class="card-body">
                {% if officials %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>الاسم</th>
                                    <th>المنصب</th>
                                    <th>المنطقة</th>
                                    <th>الحزب</th>
                                    <th>الهاتف</th>
                                    <th>الحالة</th>
                                    <th>تاريخ البداية</th>
                                    <th>الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for official in officials %}
                                <tr>
                                    <td><strong>{{ official.name }}</strong></td>
                                    <td>
                                        <span class="position-badge badge-{{ official.position }}">
                                            {{ official.get_position_display }}
                                        </span>
                                    </td>
                                    <td>{{ official.region }}</td>
                                    <td>{{ official.party|default:"-" }}</td>
                                    <td>{{ official.phone|default:"-" }}</td>
                                    <td>
                                        <span class="status-badge status-{{ official.status }}">
                                            {{ official.get_status_display }}
                                        </span>
                                    </td>
                                    <td>{{ official.start_date|date:"Y-m-d" }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="{% url 'organizations:edit_elected_official' official.pk %}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="{% url 'organizations:delete_elected_official' official.pk %}" class="btn btn-sm btn-outline-danger">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">لا توجد بيانات منتخبين</h5>
                        <p class="text-muted">ابدأ بإضافة منتخبين جدد</p>
                        <a href="{% url 'organizations:add_elected_official' %}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> إضافة منتخب جديد
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<style>
    .btn-purple {
        background-color: #6f42c1;
        border-color: #6f42c1;
        color: white;
    }

    .btn-purple:hover {
        background-color: #5a32a3;
        border-color: #5a32a3;
        color: white;
    }

    .text-purple {
        color: #6f42c1 !important;
    }
</style>

<script>
function showAllElected() {
    const detailedView = document.getElementById('detailedView');
    const sectionsView = document.querySelector('.row.mb-5');
    const showAllSection = document.querySelector('.row.mb-4');

    if (detailedView.style.display === 'none') {
        // إظهار القائمة التفصيلية
        detailedView.style.display = 'block';
        sectionsView.style.display = 'none';
        showAllSection.style.display = 'none';

        // تحديث عنوان الصفحة
        document.querySelector('h1').textContent = 'قائمة جميع المنتخبين';

        // إضافة زر العودة
        const backButton = document.createElement('button');
        backButton.className = 'btn btn-secondary mb-3';
        backButton.innerHTML = '<i class="fas fa-arrow-right me-2"></i>العودة للأقسام';
        backButton.onclick = function() {
            detailedView.style.display = 'none';
            sectionsView.style.display = 'block';
            showAllSection.style.display = 'block';
            document.querySelector('h1').textContent = 'المنتخبون';
            this.remove();
        };

        detailedView.parentNode.insertBefore(backButton, detailedView);
    }
}

// تأثيرات بصرية للبطاقات
document.addEventListener('DOMContentLoaded', function() {
    const cards = document.querySelectorAll('.section-card');

    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) scale(1.02)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });
});
</script>
{% endblock %}
