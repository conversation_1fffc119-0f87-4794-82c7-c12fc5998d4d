{% extends "base.html" %}
{% load i18n %}
{% load static %}

{% block extra_css %}
<style>
    /* تأكد من أن جميع الأقسام تظهر بشكل صحيح */
    .organizations-container .row {
        display: flex !important;
        flex-wrap: wrap !important;
    }
    
    .organizations-container .col-md-4 {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }
    
    /* تأكد من أن البطاقات تظهر بشكل صحيح */
    .organization-section-card {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
    }

    /* Hero Mini Styles */
    .hero-mini {
        background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
        color: white;
        padding: 4rem 0;
        text-align: center;
        position: relative;
        overflow: hidden;
    }
    
    .hero-mini::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url('{% static "img/pattern.png" %}');
        opacity: 0.1;
    }
    
    /* Organization Card Styles - Glassmorphism Effect */
    .organization-card {
        border: none;
        background: rgba(255, 255, 255, 0.7);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border-radius: 15px;
        border: 1px solid rgba(255, 255, 255, 0.18);
        box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.15);
        overflow: hidden;
        transition: all 0.4s ease;
    }
    
    .organization-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
    }
    
    /* Organization Section Card Styles */
    .organization-section-card {
        border: none;
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        border-radius: 15px;
        border: 1px solid rgba(255, 255, 255, 0.18);
        box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.15);
        overflow: hidden;
        transition: all 0.4s ease;
        padding: 2rem;
    }
    
    .organization-section-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
    }
    
    .section-icon {
        width: 120px;
        height: 120px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto;
        border-radius: 50%;
        background: linear-gradient(135deg, #1a237e 0%, #3949ab 100%);
        color: white;
        box-shadow: 0 10px 20px rgba(26, 35, 126, 0.3);
        transition: all 0.5s ease;
    }
    
    .organization-section-card:hover .section-icon {
        transform: scale(1.1) rotate(10deg);
    }
    
    .organization-logo {
        width: 120px;
        height: 120px;
        object-fit: contain;
        margin: 0 auto;
        border-radius: 50%;
        padding: 0.5rem;
        background-color: rgba(255, 255, 255, 0.8);
        border: 1px solid rgba(255, 255, 255, 0.5);
        transition: all 0.3s ease;
    }
    
    .organization-logo:hover {
        transform: scale(1.05) rotate(5deg);
        box-shadow: 0 0 20px rgba(26, 35, 126, 0.3);
    }
    
    .organization-card .card-title {
        font-weight: 600;
        text-align: center;
        margin-top: 1rem;
        color: #1a237e;
        position: relative;
    }
    
    .organization-card .card-title:after {
        content: '';
        position: absolute;
        width: 50px;
        height: 3px;
        background: linear-gradient(90deg, #1a237e, #3949ab);
        bottom: -8px;
        left: 50%;
        transform: translateX(-50%);
        border-radius: 3px;
    }
    
    .organization-card .card-text {
        text-align: center;
        color: #6c757d;
    }
    
    .organization-card .card-footer {
        background-color: rgba(26, 35, 126, 0.05);
        border-top: none;
    }
    
    .organization-filters {
        background: rgba(255, 255, 255, 0.7);
        backdrop-filter: blur(10px);
        -webkit-backdrop-filter: blur(10px);
        padding: 1.5rem;
        border-radius: 15px;
        margin-bottom: 2rem;
        box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.18);
    }
    
    .btn-action {
        border-radius: 50px;
        padding: 0.375rem 1rem;
        transition: all 0.3s ease;
    }
    
    .btn-action:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }
    
    /* Background decoration */
    .bg-decoration {
        position: fixed;
        width: 300px;
        height: 300px;
        border-radius: 50%;
        background: linear-gradient(45deg, rgba(26, 35, 126, 0.1), rgba(57, 73, 171, 0.1));
        filter: blur(80px);
        z-index: -1;
    }
    
    .bg-decoration-1 {
        top: 10%;
        left: 10%;
        animation: float 8s ease-in-out infinite;
    }
    
    .bg-decoration-2 {
        bottom: 10%;
        right: 10%;
        animation: float 10s ease-in-out infinite reverse;
    }
    
    .bg-decoration-3 {
        top: 50%;
        right: 20%;
        width: 200px;
        height: 200px;
        animation: float 12s ease-in-out infinite;
    }
    
    .bg-decoration-4 {
        bottom: 30%;
        left: 20%;
        width: 250px;
        height: 250px;
        animation: float 15s ease-in-out infinite reverse;
    }
    
    @keyframes float {
        0% { transform: translate(0, 0) rotate(0deg); }
        50% { transform: translate(30px, 30px) rotate(10deg); }
        100% { transform: translate(0, 0) rotate(0deg); }
    }
    
    /* Section header */
    .section-header h2 {
        position: relative;
        display: inline-block;
        padding-bottom: 10px;
    }
    
    .section-header h2:after {
        content: '';
        position: absolute;
        width: 100%;
        height: 4px;
        background: linear-gradient(90deg, #1a237e, #3949ab, transparent);
        bottom: 0;
        left: 0;
        border-radius: 2px;
    }
</style>
<link rel="stylesheet" href="{% static 'css/organizations.css' %}">
{% endblock %}

{% block title %}{% trans "المؤسسات" %} | MS-Consulting{% endblock %}

{% block content %}
<div class="container mt-5 organizations-container position-relative animated-bg">
    <!-- عناصر الزخرفة الخلفية -->
    <div class="bg-decoration bg-decoration-1"></div>
    <div class="bg-decoration bg-decoration-2"></div>
    <div class="bg-decoration bg-decoration-3"></div>
    <div class="bg-decoration bg-decoration-4"></div>
    
    <div class="row">
        <div class="col-12">
            <h1 class="text-center mb-4 wavy-underline neon-text fade-in-element">
                <i class="fas fa-building me-2 icon-pulse"></i>{% trans "المؤسسات المشاركة" %}
            </h1>
        </div>
    </div>
    
    <!-- أقسام المؤسسات -->
    <div class="row mb-5" style="display: flex; flex-wrap: wrap;">
        <!-- قسم المؤسسات الحكومية -->
        <div class="col-md-4 mb-4" style="display: block;">
            <div class="card h-100 organization-section-card glass-effect glow-border card-hover-effect scroll-animate-right">
                <div class="card-body text-center">
                    <div class="section-icon mb-3">
                        <i class="fas fa-landmark fa-4x text-primary icon-float"></i>
                    </div>
                    <h3 class="card-title">{% trans "المؤسسات الحكومية" %}</h3>
                    <p class="card-text">{% trans "عرض جميع المؤسسات الحكومية المشاركة في المؤتمر" %}</p>
                    <div class="mt-3">
                        {% if government_organizations %}
                        <p class="text-success"><i class="fas fa-check-circle"></i> {% trans "يوجد" %} {{ government_organizations.count }} {% trans "مؤسسة حكومية" %}</p>
                        {% else %}
                        <p class="text-muted"><i class="fas fa-info-circle"></i> {% trans "لا توجد مؤسسات حكومية حتى الآن" %}</p>
                        {% endif %}
                    </div>
                    <a href="{% url 'organizations:government_organizations' %}" class="btn btn-primary btn-lg mt-3 btn-3d btn-hover-expand">
                        <i class="fas fa-arrow-right me-2"></i> {% trans "عرض المؤسسات الحكومية" %}
                    </a>
                </div>
            </div>
        </div>
        
        <!-- قسم مؤسسات MS-Consulting -->
        <div class="col-md-4 mb-4" style="display: block;">
            <div class="card h-100 organization-section-card glass-effect glow-border card-hover-effect scroll-animate-left">
                <div class="card-body text-center">
                    <div class="section-icon mb-3">
                        <i class="fas fa-briefcase fa-4x text-primary icon-float"></i>
                    </div>
                    <h3 class="card-title">{% trans "المؤسسات الاستشارية" %}</h3>
                    <p class="card-text">{% trans "عرض جميع المؤسسات الاستشارية المشاركة في المؤتمر" %}</p>
                    <div class="mt-3">
                        {% if private_organizations %}
                        <p class="text-success"><i class="fas fa-check-circle"></i> {% trans "يوجد" %} {{ private_organizations.count }} {% trans "مؤسسة استشارية" %}</p>
                        {% else %}
                        <p class="text-muted"><i class="fas fa-info-circle"></i> {% trans "لا توجد مؤسسات استشارية حتى الآن" %}</p>
                        {% endif %}
                    </div>
                    <a href="{% url 'organizations:consulting_organizations' %}" class="btn btn-primary btn-lg mt-3 btn-3d btn-hover-expand">
                        <i class="fas fa-arrow-right me-2"></i> {% trans "عرض المؤسسات الاستشارية" %}
                    </a>
                </div>
            </div>
        </div>
        
        <!-- قسم العلماء -->
        <div class="col-md-4 mb-4" style="display: block;">
            <div class="card h-100 organization-section-card glass-effect glow-border card-hover-effect scroll-animate-bottom">
                <div class="card-body text-center">
                    <div class="section-icon mb-3">
                        <i class="fas fa-graduation-cap fa-4x text-primary icon-float"></i>
                    </div>
                    <h3 class="card-title">{% trans "العلماء" %}</h3>
                    <p class="card-text">{% trans "عرض جميع العلماء المشاركين في المؤتمر" %}</p>
                    <div class="mt-3">
                        {% if scholar_organizations %}
                        <p class="text-success"><i class="fas fa-check-circle"></i> {% trans "يوجد" %} {{ scholar_organizations.count }} {% trans "عالم" %}</p>
                        {% else %}
                        <p class="text-muted"><i class="fas fa-info-circle"></i> {% trans "لا يوجد علماء حتى الآن" %}</p>
                        {% endif %}
                    </div>
                    <a href="{% url 'organizations:scholars' %}" class="btn btn-primary btn-lg mt-3 btn-3d btn-hover-expand">
                        <i class="fas fa-arrow-right me-2"></i> {% trans "عرض العلماء" %}
                    </a>
                </div>
            </div>
        </div>
    </div>

<div class="container mb-5 fade-in-element">
    <div class="organization-filters shadow-sm glass-effect multi-layer-shadow">
        <div class="row align-items-center">
            <div class="col-md-5">
                <div class="d-flex align-items-center">
                    <div class="form-check mb-0">
                        <input class="form-check-input" type="checkbox" id="select-all-organizations">
                        <label class="form-check-label fw-bold" for="select-all-organizations">
                            <i class="fas fa-check-square me-1 text-primary pulse-element"></i> {% trans "تحديد الكل" %}
                        </label>
                    </div>
                    <span class="badge bg-primary ms-3 rounded-pill pulse-element" id="selected-count">0</span>
                </div>
            </div>
            <div class="col-md-7 text-md-end mt-3 mt-md-0">
                <div class="d-flex flex-wrap justify-content-md-end gap-2">
                    <a href="{% url 'organizations:export_organizations_excel' %}" class="btn btn-primary btn-action btn-glow btn-hover-expand">
                        <i class="fas fa-file-excel me-2 icon-float"></i> {% trans "تصدير إلى Excel" %}
                    </a>
                    <a href="{% url 'organizations:send_bulk_invitation' %}" class="btn btn-success btn-action btn-glow btn-hover-expand">
                        <i class="fas fa-envelope me-2 icon-float"></i> {% trans "إرسال دعوات جماعية" %}
                    </a>
                    <a href="{% url 'organizations:send_invitation' %}" class="btn btn-info btn-action btn-glow btn-hover-expand">
                        <i class="fas fa-envelope-open-text me-2 icon-float"></i> {% trans "إرسال دعوة فردية" %}
                    </a>
                    <button type="button" class="btn btn-outline-danger btn-action btn-glow btn-hover-expand" id="clear-selection" disabled>
                        <i class="fas fa-times-circle me-2"></i> {% trans "إلغاء التحديد" %}
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Background Decorations -->
<div class="bg-decoration bg-decoration-1"></div>
<div class="bg-decoration bg-decoration-2"></div>

<!-- معلومات إضافية -->
<div class="container mb-5 fade-in-element">
    <div class="row">
        <div class="col-12">
            <div class="alert alert-info glass-effect multi-layer-shadow">
                <i class="fas fa-info-circle me-2 pulse-element"></i> {% trans "يمكنك الوصول إلى تفاصيل المؤسسات من خلال الضغط على الأقسام أعلاه." %}
            </div>
        </div>
    </div>
</div>


{% endblock %}

{% block extra_js %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
<script src="{% static 'js/organizations.js' %}"></script>
<script src="{% static 'js/organization_effects.js' %}"></script>
<script>
    // تحديث عداد المؤسسات المحددة
    function updateSelectedCount() {
        const selectedOrgs = document.querySelectorAll('input[name="selected_organizations"]:checked').length;
        document.getElementById('selected-count').textContent = selectedOrgs;
    }

    // تحديد/إلغاء تحديد كل المؤسسات
    document.getElementById('select-all').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('input[name="selected_organizations"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateSelectedCount();
    });

    // تحديث العداد عند تغيير أي اختيار
    document.querySelectorAll('input[name="selected_organizations"]').forEach(checkbox => {
        checkbox.addEventListener('change', updateSelectedCount);
    });

    // تحديث العداد عند تحميل الصفحة
    updateSelectedCount();
</script>
{% endblock %}