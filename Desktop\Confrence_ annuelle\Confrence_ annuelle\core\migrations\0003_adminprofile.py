# Generated by Django 5.1.2 on 2025-07-14 14:41

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0002_alter_conference_options_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='AdminProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('admin_type', models.CharField(choices=[('super_admin', 'مسؤول كبير'), ('regular_admin', 'مسؤول عادي')], default='regular_admin', max_length=20, verbose_name='نوع المسؤول')),
                ('full_name', models.CharField(blank=True, max_length=255, verbose_name='الاسم الكامل')),
                ('phone', models.Char<PERSON>ield(blank=True, max_length=20, verbose_name='رقم الهاتف')),
                ('department', models.CharField(blank=True, max_length=100, verbose_name='القسم')),
                ('notes', models.TextField(blank=True, verbose_name='ملاحظات')),
                ('is_active', models.BooleanField(default=True, verbose_name='نشط')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='تاريخ الإنشاء')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='تاريخ التحديث')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='admin_profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'ملف المسؤول',
                'verbose_name_plural': 'ملفات المسؤولين',
                'ordering': ['user__username'],
            },
        ),
    ]
